# OA Web

Next.js 15 application for migrating SillyTavern character tooling into a modern web workspace. Import character cards, manage per-character chats, and configure LLM providers/models through a Postgres-backed dashboard.

## Getting Started
1. Install dependencies:
   ```bash
   npm install
   ```
2. Provide environment variables (see [Environment](#environment)).
3. Run the dev server:
   ```bash
   npm run dev
   ```
4. Open `http://localhost:3000` to explore the workspace.

## Available Scripts
- `npm run dev` – Start the Turbopack dev server.
- `npm run build` – Create a production build.
- `npm run start` – Serve the production bundle.
- `npm run lint` – Run ESLint (excludes generated Prisma code and `SillyTavern/`).

## Documentation
- [Project Overview](PROJECT_OVERVIEW.md) – Architecture, backend flows, and shared utilities.
- `AGENTS.md` – Repository practices, naming conventions, and process guidelines.

## Tech Stack Highlights
- Next.js 15 App Router with React 19.
- Tailwind CSS 4 + shadcn/ui component primitives.
- Prisma 6 with PostgreSQL.
- OpenAI SDK for multi-provider chat streaming.

## Environment
Set the following in `.env.local` (or export in your shell):

```bash
DATABASE_URL="postgres://user:password@localhost:5432/oa_web"
NEXTAUTH_SECRET="replace-with-generated-secret"
# Optional provider overrides
OPENAI_API_KEY="your-openai-key"
CHARACTER_CHAT_API_KEY="fallback-api-key"
CHARACTER_CHAT_OPENROUTER_API_KEY="your-openrouter-key"
CHARACTER_CHAT_OPENROUTER_SITE="https://your-site.example"
CHARACTER_CHAT_OPENROUTER_TITLE="OA Character Workspace"
```

Generate a secret with `openssl rand -base64 32`. The first registered account is promoted to `ADMIN`; subsequent registrations receive the `USER` role.

Regenerate Prisma client after schema changes and apply the migration:

```bash
npx prisma generate
npx prisma migrate dev
```

## Contributing
Follow the coding standards in `AGENTS.md`, run `npm run lint` before committing, and document any new environment variables in `.env.example` and the docs.
