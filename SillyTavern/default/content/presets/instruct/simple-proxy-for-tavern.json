{"input_sequence": "### Instruction:\n#### {{name}}:", "output_sequence": "### Response:\n#### {{name}}:", "last_output_sequence": "### Response (2 paragraphs, engaging, natural, authentic, descriptive, creative):\n#### {{name}}:", "system_sequence": "", "stop_sequence": "", "wrap": true, "macro": true, "names_behavior": "none", "activation_regex": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "", "input_suffix": "", "system_suffix": "", "user_alignment_message": "", "system_same_as_user": false, "last_system_sequence": "", "first_input_sequence": "", "last_input_sequence": "", "sequences_as_stop_strings": true, "story_string_prefix": "## {{char}}\n- You're \"{{char}}\" in this never-ending roleplay with \"{{user}}\".\n### Input:", "story_string_suffix": "### Response:\n(OOC) Understood. I will take this info into account for the roleplay. (end OOC)\n", "name": "simple-proxy-for-tavern"}