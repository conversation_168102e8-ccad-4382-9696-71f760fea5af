{"input_sequence": "### Instruction:", "output_sequence": "### Response:", "last_output_sequence": "### Response: (length = unlimited)", "system_sequence": "", "stop_sequence": "", "wrap": true, "macro": true, "names_behavior": "force", "activation_regex": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "\n\n", "input_suffix": "\n\n", "system_suffix": "", "user_alignment_message": "", "system_same_as_user": true, "last_system_sequence": "", "first_input_sequence": "", "last_input_sequence": "", "sequences_as_stop_strings": true, "story_string_prefix": "Below is an instruction that describes a task. Write a response that appropriately completes the request.\n\n### Instruction:", "story_string_suffix": "\n\n", "name": "Lightning 1.1"}