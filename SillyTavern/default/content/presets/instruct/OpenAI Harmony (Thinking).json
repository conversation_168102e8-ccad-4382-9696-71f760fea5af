{"input_sequence": "<|start|>user<|message|>", "output_sequence": "<|start|>assistant<|channel|>final<|message|>", "last_output_sequence": "{{noop}}", "system_sequence": "<|start|>developer<|message|>", "stop_sequence": "", "wrap": false, "macro": true, "activation_regex": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "<|end|>", "input_suffix": "<|end|>", "system_suffix": "<|end|>", "user_alignment_message": "", "system_same_as_user": false, "last_system_sequence": "", "first_input_sequence": "", "last_input_sequence": "", "names_behavior": "force", "sequences_as_stop_strings": false, "story_string_prefix": "<|start|>system<|message|>", "story_string_suffix": "<|end|>", "name": "OpenAI Harmony (Thinking)"}