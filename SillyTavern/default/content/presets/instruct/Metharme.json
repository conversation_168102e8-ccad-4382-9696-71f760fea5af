{"input_sequence": "<|user|>", "output_sequence": "<|model|>", "last_output_sequence": "", "system_sequence": "", "stop_sequence": "</s>", "wrap": false, "macro": true, "names_behavior": "force", "activation_regex": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "", "input_suffix": "", "system_suffix": "", "user_alignment_message": "", "system_same_as_user": true, "last_system_sequence": "", "first_input_sequence": "", "last_input_sequence": "", "sequences_as_stop_strings": true, "story_string_prefix": "<|system|>", "story_string_suffix": "", "name": "<PERSON><PERSON><PERSON>"}