{"input_sequence": "[INST] ", "output_sequence": "", "last_output_sequence": "", "system_sequence": "", "stop_sequence": "", "wrap": false, "macro": true, "names_behavior": "force", "activation_regex": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "\n", "input_suffix": " [/INST]\n", "system_suffix": "", "user_alignment_message": "Let's get started. Please respond based on the information and instructions provided above.", "system_same_as_user": true, "last_system_sequence": "", "first_input_sequence": "", "last_input_sequence": "", "sequences_as_stop_strings": true, "story_string_prefix": "[INST] <<SYS>>\n", "story_string_suffix": "\n<</SYS>> Understood. [/INST]", "name": "Llama 2 Chat"}