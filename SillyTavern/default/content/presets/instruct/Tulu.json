{"input_sequence": "<|user|>\n", "output_sequence": "<|assistant|>\n", "last_output_sequence": "", "system_sequence": "<|system|>\n", "stop_sequence": "<|end_of_text|>", "wrap": false, "macro": true, "names_behavior": "force", "activation_regex": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "<|end_of_text|>\n", "input_suffix": "\n", "system_suffix": "\n", "user_alignment_message": "", "system_same_as_user": false, "last_system_sequence": "", "first_input_sequence": "", "last_input_sequence": "", "sequences_as_stop_strings": true, "story_string_prefix": "<|system|>\n", "story_string_suffix": "\n", "name": "<PERSON><PERSON>"}