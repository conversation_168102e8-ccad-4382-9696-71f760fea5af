{"input_sequence": "<start_of_turn>user", "output_sequence": "<start_of_turn>model", "last_output_sequence": "", "system_sequence": "<start_of_turn>system", "stop_sequence": "<end_of_turn>", "wrap": true, "macro": true, "names_behavior": "force", "activation_regex": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "<end_of_turn>\n", "input_suffix": "<end_of_turn>\n", "system_suffix": "<end_of_turn>\n", "user_alignment_message": "", "system_same_as_user": true, "last_system_sequence": "", "first_input_sequence": "", "last_input_sequence": "", "sequences_as_stop_strings": true, "story_string_prefix": "<start_of_turn>user", "story_string_suffix": "<end_of_turn>\n", "name": "Gemma 2"}