.scrollable-buttons-container {
    /* Use viewport height instead of fixed pixels */
    max-height: 50vh;
    /* Momentum scrolling on iOS */
    -webkit-overflow-scrolling: touch;
    /* m-t-1 is equivalent to margin-top: 1rem; */
    margin-top: 1rem;
    flex-shrink: 1;
    min-height: 0;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.scrollable-buttons-container::-webkit-scrollbar {
    width: 6px;
}

.scrollable-buttons-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}
