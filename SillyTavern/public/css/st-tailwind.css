.text_warning {
    color: rgb(220 173 16);
}

.text_danger {
    color: var(--fullred);
}

.highlighted {
    color: black;
    background-color: yellow;
    text-shadow: none !important;
}

.m-t-0 {
    margin-top: 0;
}

.m-t-1 {
    margin-top: 1em;
}

.m-t-2 {
    margin-top: 2em;
}

.m-t-3 {
    margin-top: 3em;
}

.m-t-4 {
    margin-top: 4em;
}

.m-t-5 {
    margin-top: 5em;
}

.m-b-1 {
    margin-bottom: 1em;
}

.m-b-2 {
    margin-bottom: 2em;
}

.m-b-3 {
    margin-bottom: 3em;
}

.m-b-4 {
    margin-bottom: 4em;
}

.m-b-5 {
    margin-bottom: 5em;
}

.tooltip {
    cursor: help;
}

.margin-bot-10px,
.marginBot10 {
    margin-bottom: 10px !important;
}

.marginTop10 {
    margin-top: 10px !important;
}

.marginBot5 {
    margin-bottom: 5px !important;
}

.marginTop5 {
    margin-top: 5px !important;
}

.marginTopBot5 {
    margin-top: 5px !important;
    margin-bottom: 5px !important;
}

.margin5 {
    margin: 5px;
}

.marginLeft5 {
    margin-left: 5px;
}

.overflowYAuto {
    overflow-y: auto;
}

.heightMinContent {
    height: min-content;
}

.justifySpaceBetween {
    justify-content: space-between;
}

.justifySpaceEvenly {
    justify-content: space-evenly;
}

.justifySpaceAround {
    justify-content: space-around;
}

.alignitemsflexstart {
    align-items: flex-start !important;
}

.alignItemsFlexEnd {
    align-items: flex-end !important;
}

.alignItemsBaseline {
    align-items: baseline !important;
}

.alignSelfStart {
    align-self: start;
}

.gap0 {
    gap: 0 !important;
}

.gap3px {
    gap: 3px !important;
}

.gap5px {
    gap: 5px !important;
}

.gap10px {
    gap: 10px !important;
}

.gap10h20v {
    gap: 10px 20px !important;
}

.gap10h5v {
    gap: 5px 10px !important;
}

.wide10pMinFit {
    width: 10%;
    min-width: fit-content;
}

.wide100pLess70px {
    width: calc(100% - 70px);
}

.wideMax100px {
    max-width: 100px;
}

.width100px {
    width: 100px;
}

.widthUnset {
    width: unset;
}

.no-border {
    border: none !important;
}

.no-shadow {
    box-shadow: none !important;
}

.height100p {
    height: 100%;
}

.height100pSpaceEvenly {
    align-content: space-evenly;
    height: 100%;
}

.height32px {
    height: 32px;
}

.TxtLrgBoldCenter {
    text-align: center;
    font-size: large;
    font-weight: 600;
}

.textAlignCenter {
    text-align: center;
}

.margin-right-10px {
    margin-right: 10px;
}


.success {
    color: green;
}

.failure {
    color: red;
}

.optional {
    color: lightgray;
}

.monospace {
    font-family: var(--monoFontFamily);
}

.expander {
    flex-grow: 1;
}

.redOverlayGlow {
    color: #800;
    opacity: 0.8 !important;
    text-shadow: none !important;
}

.width100p {
    width: 100%;
}

.flex {
    display: flex;
}

.flexBasis100p {
    flex-basis: 100%;
}

.flexBasis50p {
    flex-basis: 50%
}

.flexBasis25p {
    flex-basis: 25%
}

.flexBasis200px {
    flex-basis: 200px
}

.flexBasis48p {
    flex-basis: 48%
}

.flexBasis30p {
    flex-basis: 30%;
}

.flex-container {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.flexNoGap {
    gap: unset !important;
}

.flexGrow {
    flex-grow: 1;
}

.flexShrink {
    flex-shrink: 1
}

.flexWrap {
    flex-wrap: wrap;
}

.flexnowrap,
.flexNoWrap {
    flex-wrap: nowrap;
}

.inline-flex {
    display: inline-flex;
}

.inline-block {
    display: inline-block;
}

.alignitemscenter,
.alignItemsCenter {
    align-items: center;
}

.alignitemsstart,
.alignItemsStart {
    align-items: start;
}

.overflow-hidden {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.maxWidth200px {
    max-width: 200px;
}

.alignContentFlexStart {
    align-content: flex-start;
}

.alignContentCenter {
    align-content: center;
}

.overflowHidden {
    overflow: hidden;
}

.overflowYScroll {
    overflow-y: scroll;
}

.padding0 {
    padding: 0;
}

.padding5 {
    padding: 5px;
}

.padding10 {
    padding: 10px;
}

.margin0 {
    margin: 0;
}

.margin0auto {
    margin: 0 auto;
}

.margin-r5 {
    margin-right: 5px;
}

.margin-r2 {
    margin-right: 2px;
}

.flexAuto {
    flex: auto;
}

.flex0 {
    flex: 0;
}

.flex1 {
    flex: 1;
}

.flex2 {
    flex: 2 !important;
}

.flex3 {
    flex: 3;
}

.flex4 {
    flex: 4;
}

.flexFlowColumn {
    flex-flow: column;
}

.flexFlowRow {
    flex-flow: row;
}

.wideMinContent {
    width: min-content;
}

.flexWide50p {
    flex: 50%;
}

.wide25p {
    width: 25%;
}

.wide30p {
    width: 30% !important;
}

.justifyLeft {
    text-align: start;
    justify-content: left;
    margin-left: unset;
}

.justifyCenter {
    justify-content: center;
    margin: 0 auto;
}

.justifyContentSpaceAround {
    justify-content: space-around;
}

.justifyContentFlexStart {
    justify-content: flex-start;
}

.justifyContentFlexEnd {
    justify-content: flex-end;
}

.spaceEvenly {
    justify-content: space-evenly;
}

.spaceBetween {
    justify-content: space-between;
}

.widthNatural {
    width: unset !important;
    min-width: unset !important;
    max-width: unset !important;
}

.widthFreeExpand {
    width: -webkit-fill-available;
    width: -moz-available;
}

.wide100p {
    width: 100%;
}

.wide50p {
    width: 50%;
}

.wide50px {
    width: 50px;
}

.indent20p {
    margin-left: 20px;
}

.textarea_compact {
    font-size: calc(var(--mainFontSize) * 0.95);
    line-height: 1.2;
}

.hoverglow {
    transition: opacity var(--animation-duration-2x);
}

.hoverglow:hover {
    opacity: 1 !important;
    cursor: pointer;
}

input:disabled,
textarea:disabled {
    cursor: not-allowed;
    filter: brightness(0.5);
}

#AdvancedFormatting .disabled {
    filter: brightness(0.5);
}

.debug-red {
    border: 1px solid red !important;
}

.debug-yellow {
    border: 1px solid yellow !important;
}

.debug-green {
    border: 1px solid green !important;
}

.debug-blue {
    border: 1px solid blue !important;
}

.debug-purple {
    border: 1px solid purple !important;
}

.fontsize120p {
    font-size: calc(var(--mainFontSize) * 1.2) !important;
}

.fontsize90p {
    font-size: calc(var(--mainFontSize) * 0.9) !important;
}

.fontsize80p {
    font-size: calc(var(--mainFontSize) * 0.8) !important;
}

.fontsize60p {
    font-size: calc(var(--mainFontSize) * 0.6) !important;
}

.paddingBottom5px {
    padding: unset;
    padding-bottom: 5px;
}

.paddingTopBot5 {
    padding: 5px 0;
}

.paddingLeftRight5 {
    padding: 0 5px;
}

.heightFitContent {
    height: fit-content;
}

.widthFitContent {
    width: fit-content;
    min-width: fit-content;
}

.flexGap2 {
    gap: 2px;
}

.flexGap5 {
    gap: 5px;
}

.flexGap10 {
    gap: 10px;
}

.opacity50p {
    opacity: 0.5
}

.grayscale {
    filter: grayscale(100%);
}

.opacity1 {
    opacity: 1 !important;
}

.circleborder30px {
    right: 30px;
    top: 10px;
    position: absolute;
    border: 1px solid var(--SmartThemeBodyColor);
    border-radius: 100%;
    aspect-ratio: 1 / 1;
    height: 30px;
    text-align: center;
    padding: 5px;
}

ul.li-padding-b-1 li {
    padding-bottom: 1em;
}

ul.li-padding-b-2 li {
    padding-bottom: 2em;
}

ul.li-padding-b-5 li {
    padding-bottom: 5em;
}

ul.li-padding-bot5 li {
    padding-bottom: 5px;
}

ul.li-padding-bot10 li {
    padding-bottom: 10px;
}

.wordBreakAll {
    word-break: break-all;
}
