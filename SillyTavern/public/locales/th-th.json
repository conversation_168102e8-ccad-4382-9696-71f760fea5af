{"Favorite": "รายการโปรด", "Tag": "แท็ก", "Duplicate": "คัดลอก", "Persona": "ตัวตน", "Delete": "ลบ", "AI Response Configuration": "ตั้งค่าการตอบกลับ AI", "AI Configuration panel will stay open": "แผงการตั้งค่า AI จะคงเปิดอยู่", "clickslidertips": "คลิกเพื่อดูคำแนะนำสไลเดอร์", "MAD LAB MODE ON": "เปิดโหมด Mad Lab", "Documentation on sampling parameters": "เอกสารอธิบายค่าพารามิเตอร์การสุ่ม", "kobldpresets": "พรีเซ็ต KoboldAI", "guikoboldaisettings": "การตั้งค่า GUI KoboldAI", "Update current preset": "อัปเดตพรีเซ็ตปัจจุบัน", "Save preset as": "บันทึกการปรับแต่ง", "Import preset": "นำเข้าการปรับแต่ง", "Export preset": "ส่งออกการปรับแต่ง", "Restore current preset": "กู้คืนการปรับแต่งล่าสุด", "Delete the preset": "ลบการปรับแต่ง", "novelaipresets": "พรีเซ็ต NovelAI", "Default": "ค่าเริ่มต้น", "openaipresets": "พรีเซ็ต OpenAI", "Text Completion presets": "รูปแบบของ Text Completion", "AI Module": "โมดูลเอไอ", "Changes the style of the generated text.": "ปรับเปลี่ยนสไตล์ในการสร้างข้อความ", "No Module": "ไม่พบโมดูล", "Instruct": "โหมดคำสั่ง", "Prose Augmenter": "Prose Augmenter", "Text Adventure": "เกมข้อความผจญภัย", "response legth(tokens)": "ความยาวในการตอบ (ต่อโทเค็น)", "Streaming": "สตรีมมิ่ง", "Streaming_desc": "แสดงข้อความเป็นบิตต่อบิตขณะที่ถูกสร้างขึ้น", "context size(tokens)": "ขนาดความจำ (ต่อโทเค็น)", "unlocked": "ปลดล็อค", "Only enable this if your model supports context sizes greater than 8192 tokens": "ให้เปิดสิ่งนี้เฉพาะถ้าหากความจำในโมเดลของคุณรองรับได้มากกว่า 8192 โทเค็น", "Max prompt cost:": "ค่าใช้จ่ายสูงสุดของพรอมต์:", "Display the response bit by bit as it is generated.": "แสดงข้อความทีละตัวอักษรขณะกำลังถูกสร้าง", "When this is off, responses will be displayed all at once when they are complete.": "หากปิดสิ่งนี้ ระบบจะแสดงข้อความมาให้ทั้งหมดในครั้งเดียวเมื่อถูกสร้างจนจบ", "Temperature": "Temperature", "rep.pen": "rep.pen", "Rep. Pen. Range.": "Rep. Pen. <PERSON>.", "Rep. Pen. Slope": "<PERSON><PERSON> <PERSON><PERSON>", "Rep. Pen. Freq.": "Rep. <PERSON><PERSON>.", "Rep. Pen. Presence": "Rep. <PERSON><PERSON>", "TFS": "การสุ่มแบบ Tail Free", "Phrase Repetition Penalty": "โทษการพูดซ้ำ", "Off": "ปิด", "Very light": "สว่างมาก", "Light": "สว่าง", "Medium": "ปานกลาง", "Aggressive": "รุนแรง", "Very aggressive": "รุนแรงมาก", "Unlocked Context Size": "ปลดล๊อคขนาดความจำ", "Unrestricted maximum value for the context slider": "ค่าสูงสุดไม่จำกัดสำหรับตัวปรับความจำ", "Context Size (tokens)": "ขนาดความจำ (ต่อโทเค็น)", "Max Response Length (tokens)": "ความยาวในการตอบสูงสุด (ต่อโทเค็น)", "Multiple swipes per generation": "ปัดหลายครั้งต่อการสร้าง", "Enable OpenAI completion streaming": "เปิดใช้งานการสตรีมผลลัพธ์จาก OpenAI", "Frequency Penalty": "Frequency Penalty", "Presence Penalty": "Presence Penalty", "Count Penalty": "Count Penalty", "Top K": "Top K", "Top P": "Top P", "Repetition Penalty": "Repetition Penalty", "Min P": "<PERSON>", "Top A": "Top A", "Quick Prompts Edit": "แก้ไขชุดคำสั่งแบบรวบรัด", "Main": "หน้าหลัก", "NSFW": "เนื้อหาไม่เหมาะสม", "Jailbreak": "เจลเบรค", "Utility Prompts": "ชุดคำสั่งเพิ่มเติม", "Impersonation prompt": "ชุดคำสั่งสำหรับการสวมบทบาท", "Restore default prompt": "คืนค่าชุดคำสั่งเริ่มต้น", "Prompt that is used for Impersonation function": "ชุดคำสั่งที่จะถูกใช้ในฟังชั่นการสวมบทบาท", "World Info Format Template": "เท็มเพลตรูปแบบของ World Info", "Restore default format": "คืนค่ารูปแบบเริ่มต้น", "Wraps activated World Info entries before inserting into the prompt.": "ห่อหุ้มรายการ World Info ที่เปิดใช้งานก่อนแทรกลงในพรอมต์", "scenario_format_template_part_1": "ใช้", "scenario_format_template_part_2": "เพื่อทำเครื่องหมายตำแหน่งที่จะแทรกเนื้อหา", "Scenario Format Template": "เทมเพลตรูปแบบสถานการณ์", "Personality Format Template": "เทมเพลตรูปแบบบุคลิกภาพ", "Group Nudge Prompt Template": "เทมเพลตพรอมต์กระตุ้นกลุ่ม", "Sent at the end of the group chat history to force reply from a specific character.": "ส่งท้ายประวัติแชทกลุ่มเพื่อบังคับให้ตัวละครหนึ่งตอบกลับ", "New Chat": "เริ่มแชทใหม่", "Restore new chat prompt": "คืนค่าชุดคำสั่งสำหรับแชทใหม่", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "ตั้งค่าไว้ต้นประวัติการแชทเพื่อระบุว่าแชทใหม่กำลังจะเริ่มต้น", "New Group Chat": "เริ่มต้นแชทกลุ่มใหม่", "Restore new group chat prompt": "คืนค่าชุดคำสั่งแชทกลุ่มใหม่", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "ตั้งค่าไว้ต้นประวัติการแชทเพื่อระบุว่าแชทกลุ่มใหม่กำลังจะเริ่มต้น", "New Example Chat": "ตัวอย่างการพูดคุยใหม่", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "ตั้งค่าไว้ต้นตัวอย่างบทสนทนาเพื่อระบุว่าตัวอย่างแชทใหม่กำลังจะเริ่มต้น", "Continue nudge": "กระตุ้นให้ดำเนินต่อ", "Set at the end of the chat history when the continue button is pressed.": "ตั้งค่าไว้ท้ายประวัติการแชทเมื่อกดปุ่มดำเนินต่อ", "Replace empty message": "เขียนทับข้อความที่ว่างเปล่า", "Send this text instead of nothing when the text box is empty.": "ส่งข้อความที่กำหนดนี้ไปแทนข้อความที่ว่างเปล่า หากช่องแชทไม่ได้ส่งข้อความใดๆไป", "Seed": "Seed", "Set to get deterministic results. Use -1 for random seed.": "ตั้งค่าเพื่อให้ได้ผลลัพธ์ที่แน่นอน ใช้ -1 สำหรับ seed แบบสุ่ม", "Temperature controls the randomness in token selection": "Temperature ควบคุมอัตราสุ่มในการเลือกโทเค็น", "Top_K_desc": "จำกัดทางเลือกโทเค็นตามอันดับความน่าจะเป็นสูงสุด K ตัว", "Top_P_desc": "จำกัดทางเลือกโทเค็นตามเปอร์เซ็นต์ความน่าจะเป็นสะสม", "Typical P": "Typical P", "Typical_P_desc": "การควบคุมการสุ่มโดยเลือกโทเค็นที่มีความน่าจะเป็นใกล้เคียงกับค่าเฉลี่ย", "Min_P_desc": "กำหนดขีดจำกัดความน่าจะเป็นขั้นต่ำสำหรับการเลือกโทเค็น", "Top_A_desc": "การสุ่มแบบ Top A ที่ปรับให้เหมาะสมกับบริบท", "Tail_Free_Sampling_desc": "การสุ่มโดยตัดโทเค็นที่มีความน่าจะเป็นต่ำออก", "rep.pen range": "ขอบเขตโทษการซ้ำ", "Mirostat": "Mirostat", "Mode": "โหมด", "Mirostat_Mode_desc": "อธิบายโหมด Mirostat", "Tau": "Tau", "Mirostat_Tau_desc": "พารามิเตอร์ความแปรปรวนสำหรับผลลัพธ์ Mirostat", "Eta": "Eta", "Mirostat_Eta_desc": "อัตราการเรียนรู้ของ Mirostat", "Ban EOS Token": "ห้ามใช้โทเค็น EOS", "Ban_EOS_Token_desc": "บังคับให้โมเดลไม่จบการสร้างข้อความก่อนเวลาอันควร", "GBNF Grammar": "ไวยากรณ์ GBNF", "Type in the desired custom grammar": "พิมพ์ไวยากรณ์ที่ต้องการ", "Samplers Order": "ลำดับของตัวสุ่ม", "Samplers will be applied in a top-down order. Use with caution.": "ตัวสุ่มจะถูกใช้งานตามลำดับจากบนลงล่าง ใช้ด้วยความระมัดระวัง", "Tail Free Sampling": "การสุ่มแบบ Tail Free", "Load koboldcpp order": "โหลดลำดับของ koboldcpp", "Preamble": "คำนำ", "Use style tags to modify the writing style of the output.": "ใช้แท็กสไตล์เพื่อปรับเปลี่ยนรูปแบบการเขียนของผลลัพธ์", "Banned Tokens": "โทเค็นที่ถูกห้าม", "Sequences you don't want to appear in the output. One per line.": "ลำดับที่คุณไม่ต้องการให้ปรากฏในผลลัพธ์ หนึ่งรายการต่อบรรทัด", "Logit Bias": "Logit Bias", "Add": "เพิ่ม", "Helps to ban or reenforce the usage of certain words": "ช่วยห้ามหรือเสริมการใช้คำบางคำ", "CFG Scale": "CFG Scale", "Negative Prompt": "Negative Prompt", "Add text here that would make the AI generate things you don't want in your outputs.": "เพิ่มข้อความที่นี่ที่จะทำให้ AI สร้างสิ่งที่คุณไม่ต้องการในผลลัพธ์", "Used if CFG Scale is unset globally, per chat or character": "ใช้เมื่อ CFG Scale ไม่ได้ถูกตั้งค่าทั่วไป ต่อแชท หรือตัวละคร", "Mirostat Tau": "Mirostat Tau", "Mirostat LR": "Mirostat LR", "Min Length": "ความยาวต่ำสุด", "Top K Sampling": "การสุ่มแบบ Top K", "Nucleus Sampling": "การสุ่มแบบ Nucleus", "Top A Sampling": "การสุ่มแบบ Top A", "CFG": "CFG", "Neutralize Samplers": "ทำให้ตัวสุ่มเป็นกลาง", "Set all samplers to their neutral/disabled state.": "ตั้งค่าตัวสุ่มทั้งหมดให้เป็นสถานะเป็นกลาง/ปิดใช้งาน", "Sampler Select": "เลือกตัวสุ่ม", "Customize displayed samplers or add custom samplers.": "ปรับแต่งตัวสุ่มที่แสดงหรือเพิ่มตัวสุ่มแบบกำหนดเอง", "Epsilon Cutoff": "Epsilon Cutoff", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "Epsilon cutoff กำหนดขีดจำกัดความน่าจะเป็นขั้นต่ำที่โทเค็นจะถูกแยกออกจากการสุ่ม", "Eta Cutoff": "<PERSON><PERSON>", "Eta_Cutoff_desc": "คำอธิบาย <PERSON>ta Cutoff สำหรับการกรองโทเค็น", "rep.pen decay": "การลดลงของ rep.pen", "Encoder Rep. Pen.": "Encoder Rep. Pen.", "No Repeat Ngram Size": "ขนาด Ngram ที่ไม่ซ้ำ", "Skew": "ความเบี้ยว", "Max Tokens Second": "โทเค็นสูงสุดต่อวินาที", "Smooth Sampling": "Smooth Sampling", "Smooth_Sampling_desc": "สามารถปรับการกระจายโดยใช้การแปลงดีกรี 2/3 ได้ ค่าที่ต่ำกว่าของ Smoothing Factor จะให้ความคิดสร้างสรรค์มากขึ้น โดยปกติ 0.2-0.3 จะเป็นจุดที่เหมาะสม (สมมติว่าเส้นโค้ง = 1) ค่าที่สูงขึ้นของ Smoothing Curve จะทำให้เส้นโค้งชันขึ้น และการเลือกที่มีความน่าจะเป็นต่ำจะได้รับการลงโทษอย่างรุนแรงมากขึ้น เส้นโค้ง 1.0 เหมือนกับการใช้ Smoothing Factor เพียงอย่างเดียว", "Smoothing Factor": "Smoothing Factor", "Smoothing Curve": "Smoothing Curve", "DRY_Repetition_Penalty_desc": "DRY จะลงโทษโทเค็นที่ขยายจุดสิ้นสุดของข้อมูลนำเข้าเป็นลำดับที่เกิดขึ้นก่อนหน้านี้ในข้อมูลนำเข้า ตั้งค่า multiplier เป็น 0 เพื่อปิดใช้งาน", "DRY Repetition Penalty": "DRY Repetition Penalty", "DRY_Multiplier_desc": "ตั้งค่าให้มากกว่า 0 เพื่อเปิดใช้งาน DRY ควบคุมขนาดของโทษสำหรับลำดับสั้นที่สุดที่ได้รับโทษ", "Multiplier": "Multiplier", "DRY_Base_desc": "ควบคุมอัตราที่โทษเพิ่มขึ้นตามความยาวของลำดับที่เพิ่มขึ้น", "Base": "ค่าพื้นฐาน", "DRY_Allowed_Length_desc": "ลำดับที่ยาวที่สุดที่สามารถทำซ้ำได้โดยไม่มีโทษ", "Allowed Length": "ความยาวที่อนุญาต", "Penalty Range": "Penalty Range", "DRY_Sequence_Breakers_desc": "โทเค็นที่ทำให้การจับคู่ลำดับไม่ต่อเนื่อง ระบุเป็นรายการที่คั่นด้วยเครื่องหมายจุลภาคของสตริงที่อยู่ในเครื่องหมายคำพูด", "Sequence Breakers": "Sequence Breakers", "JSON-serialized array of strings.": "อาร์เรย์ของสตริงในรูปแบบ JSON", "Dynamic Temperature": "Tempurature แบบไดนามิก", "Scale Temperature dynamically per token, based on the variation of probabilities": "ปรับขนาด Tempurature แบบไดนามิกต่อโทเค็น ตามความแปรปรวนของความน่าจะเป็น", "Minimum Temp": "Temp ต่ำสุด", "Maximum Temp": "Temp สูงสุด", "Exponent": "เลขชี้กำลัง", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (โหมด=1 สำหรับ llama.cpp เท่านั้น)", "Mirostat_desc": "อธิบาย Mirostat", "Mirostat Mode": "โหมด Mirostat", "Variability parameter for Mirostat outputs": "พารามิเตอร์ความแปรปรวนสำหรับผลลัพธ์ Mirostat", "Mirostat Eta": "Mirostat Eta", "Learning rate of Mirostat": "อัตราการเรียนรู้ของ Mirostat", "Beam search": "การค้นหาแบบ Beam", "Helpful tip coming soon.": "เคล็ดลับมีประโยชน์จะมาเร็วๆ นี้", "Number of Beams": "จำนวน Beam", "Length Penalty": "โทษความยาว", "Early Stopping": "การหยุดล่วงหน้า", "Contrastive search": "การค้นหาแบบ Contrastive", "Penalty Alpha": "Penalty Alpha", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "ความแรงของเทอม regularization ของ Contrastive Search ตั้งเป็น 0 เพื่อปิด CS", "Do Sample": "Do Sample", "Add BOS Token": "เพิ่ม BOS Token", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "เพิ่ม bos_token ไว้ต้นพรอมต์ การปิดสิ่งนี้อาจทำให้การตอบกลับสร้างสรรค์มากขึ้น", "Ban the eos_token. This forces the model to never end the generation prematurely": "ห้าม eos_token ทำให้โมเดลไม่สิ้นสุดการสร้างก่อนเวลาอันควร", "Ignore EOS Token": "เพิกเฉยต่อ EOS Token", "Ignore the EOS Token even if it generates.": "เพิกเฉย EOS Token แม้ว่าจะถูกสร้างขึ้น", "Skip Special Tokens": "ข้าม Special Tokens", "Temperature Last": "อุณหภูมิสุดท้าย", "Temperature_Last_desc": "ใช้อุณหภูมิเป็นขั้นตอนสุดท้ายในการสุ่ม", "Speculative Ngram": "Speculative Ngram", "Use a different speculative decoding method without a draft model": "ใช้วิธีการ speculative decoding ที่แตกต่างกันโดยไม่ต้องใช้โมเดลร่าง", "Spaces Between Special Tokens": "ช่องว่างระหว่าง Special Tokens", "LLaMA / Mistral / Yi models only": "เฉพาะโมเดล LLaMA / Mistral / Yi เท่านั้น", "Example: some text [42, 69, 1337]": "ตัวอย่าง: ข้อความบางส่วน [42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "คำแนะนำฟรีเกี่ยวกับเครื่องมือจำแนกประเภท จะมีเคล็ดลับที่เป็นประโยชน์เพิ่มเติมเร็วๆ นี้", "Scale": "ขนาด", "JSON Schema": "JSON Schema", "Type in the desired JSON schema": "พิมพ์ JSON schema ที่ต้องการ", "Grammar String": "สตริงไวยากรณ์", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF หรือ EBNF ขึ้นอยู่กับแบ็กเอนด์ที่ใช้ หากคุณใช้สิ่งนี้ คุณควรรู้ว่าเป็นแบบไหน", "Top P & Min P": "Top P & Min P", "Load default order": "โหลดลำดับเริ่มต้น", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "เฉพาะ llama.cpp กำหนดลำดับของตัวสุ่ม หากโหมด Mirostat ไม่ใช่ 0 ลำดับตัวสุ่มจะถูกเพิกเฉย", "Sampler Priority": "ลำดับความสำคัญของตัวสุ่ม", "Ooba only. Determines the order of samplers.": "เฉพาะ Ooba กำหนดลำดับของตัวสุ่ม", "Character Names Behavior": "พฤติกรรมการแสดงชื่อตัวละคร", "Helps the model to associate messages with characters.": "ช่วยให้โมเดลเชื่อมโยงข้อความกับตัวละคร", "None": "ไม่มี", "character_names_default": "ชื่อตัวละครเริ่มต้น", "Don't add character names.": "ไม่เพิ่มชื่อตัวละคร", "Completion": "การเสร็จสิ้น", "character_names_completion": "การเสร็จสิ้นชื่อตัวละคร", "Add character names to completion objects.": "เพิ่มชื่อตัวละครไปยังออบเจ็กต์การเสร็จสิ้น", "Message Content": "เนื้อหาของข้อความ", "Prepend character names to message contents.": "เพิ่มชื่อตัวละครไว้หน้าเนื้อหาข้อความ", "Continue Postfix": "Continue Postfix", "The next chunk of the continued message will be appended using this as a separator.": "ส่วนต่อไปของข้อความที่ต่อเนื่องจะถูกผนวกโดยใช้สิ่งนี้เป็นตัวคั่น", "Space": "พื้นที่ว่าง", "Newline": "ขึ้นบรรทัดใหม่", "Double Newline": "ขึ้นสองบรรทัด", "Wrap user messages in quotes before sending": "ครอบข้อความของผู้ใช้ด้วยเครื่องหมายคำพูดก่อนส่ง", "Wrap in Quotes": "Wrap in Quotes", "Wrap entire user message in quotes before sending.": "ครอบข้อความของผู้ใช้ทั้งหมดด้วยเครื่องหมายคำพูดก่อนส่ง", "Leave off if you use quotes manually for speech.": "ปิดถ้าคุณใช้เครื่องหมายคำพูดด้วยตนเองสำหรับการพูด", "Continue prefill": "Continue prefill", "Continue sends the last message as assistant role instead of system message with instruction.": "Continue ส่งข้อความสุดท้ายเป็นบทบาทผู้ช่วยแทนที่จะเป็นข้อความระบบพร้อมคำแนะนำ", "Squash system messages": "Squash system messages", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "รวมข้อความระบบที่ติดต่อกันเป็นหนึ่งเดียว (ไม่รวมบทสนทนาตัวอย่าง) อาจปรับปรุงความสอดคล้องสำหรับโมเดลบางรุ่น", "Enable function calling": "Enable function calling", "enable_functions_desc_1": "อนุญาตให้มีการใช้งาน ", "enable_functions_desc_2": "function tools", "enable_functions_desc_3": "สามารถใช้งานโดยใช้ผ่าน Extension เพื่อเพิ่มฟังก์ชั่นเพิ่มเติม", "enable_functions_desc_4": "ไม่ Support ถ้า Prompt Post-Processing with \"no tools\" ถูกเปิดใช้งาน", "Allows the model to return its thinking process.": "อนุญาตให้โมเดลส่งคืนกระบวนการคิดของตัวเอง", "This setting affects visibility only.": "การตั้งค่านี้มีผลต่อการมองเห็นเท่านั้น", "Send inline images": "ส่งรูปภาพแบบ inline", "image_inlining_hint_1": "ส่งรูปภาพในพรอมต์หากโมเดลรองรับ โดยใช้", "image_inlining_hint_2": "เพื่อดำเนินการในข้อความนั้นๆ หรือใช้ตัวเลือก", "image_inlining_hint_3": "เพื่อแนบไฟล์รูปภาพลงในแชท", "Inline Image Quality": "คุณภาพรูปภาพ Inline", "openai_inline_image_quality_auto": "อัตโนมัติ", "openai_inline_image_quality_low": "ต่ำ", "openai_inline_image_quality_high": "สูง", "Use AI21 Tokenizer": "ใช้ AI21 Tokenizer", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "ใช้ tokenizer ที่เหมาะสมสำหรับโมเดล Jurassic ซึ่งมีประสิทธิภาพมากกว่าของ GPT", "Use Google Tokenizer": "ใช้ Google Tokenizer", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "ใช้ tokenizer ที่เหมาะสมสำหรับโมเดล Google ผ่าน API ช้ากว่าในการประมวลผลพรอมต์ แต่นับโทเค็นได้แม่นยำมากกว่า", "Use system prompt": "ใช้คำสั่งระบบ (system prompt)", "(Gemini 1.5 Pro/Flash only)": "(เฉพาะ Gemini 1.5 Pro/Flash เท่านั้น)", "Merges_all_system_messages_desc_1": "รวมข้อความระบบทั้งหมดจนถึงข้อความแรกที่ไม่ใช่บทบาทระบบ และส่งในฟิลด์", "Merges_all_system_messages_desc_2": "เดี่ยว", "Assistant Prefill": "Assistant Prefill", "Start Claude's answer with...": "เริ่มคำตอบของ Claude ด้วย...", "Assistant Impersonation Prefill": "Assistant Impersonation Prefill", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "ส่งพรอมต์ระบบสำหรับโมเดลที่รองรับ หากปิดใช้งาน ข้อความผู้ใช้จะถูกเพิ่มไว้ต้นพรอมต์", "User first message": "ข้อความเริ่มต้นของผู้ใช้", "Restore User first message": "คืนค่าข้อความเริ่มต้นของผู้ใช้", "Human message": "ข้อความของยูเซอร์", "New preset": "เพิ่มการปรับแต่งใหม่", "Delete preset": "ลบการปรับแต่ง", "View / Edit bias preset": "ดู / แก้ไข bias preset", "Add bias entry": "เพิ่มรายการ bias", "Most tokens have a leading space.": "โทเค็นส่วนใหญ่มีช่องว่างนำหน้า", "API Connections": "การเชื่อมต่อ API", "Text Completion": "Text Completion", "Chat Completion": "Chat Completion", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "โปรดหลีกเลี่ยงการส่งข้อมูลที่ละเอียดอ่อนไปยัง Horde", "Review the Privacy statement": "ตรวจสอบนโยบายความเป็นส่วนตัว", "Register a Horde account for faster queue times": "ลงทะเบียนบัญชี Horde เพื่อเวลาคิวที่เร็วขึ้น", "Learn how to contribute your idle GPU cycles to the Horde": "เรียนรู้วิธีการใช้งาน GPU ของคุณกับ Horde", "Adjust context size to worker capabilities": "ปรับขนาดบริบทให้เหมาะกับความสามารถของ worker", "Adjust response length to worker capabilities": "ปรับความยาวการตอบกลับให้เหมาะกับความสามารถของ worker", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "สามารถช่วยแก้ไขการตอบกลับที่ไม่ดีโดยการจัดคิวเฉพาะ worker ที่ได้รับการอนุมัติ อาจทำให้เวลาตอบกลับช้าลง", "Trusted workers only": "เฉพาะ worker ที่เชื่อถือได้", "API key": "คีย์ API", "Get it here:": "รับได้ที่นี่:", "Register": "ลงทะเบียน", "View my Kudos": "ดู Kudos ของฉัน", "Enter": "ส่ง", "to use anonymous mode.": "เพื่อใช้โหมดไม่ระบุชื่อ", "Clear your API key": "ล้างคีย์ API ของคุณ", "For privacy reasons, your API key will be hidden after you reload the page.": "ด้วยเหตุผลด้านความเป็นส่วนตัว คีย์ API ของคุณจะถูกซ่อนไว้หลังจากรีเฟรชหน้าเว็บไซต์แล้ว", "Models": "รุ่นโมเดล", "Refresh models": "รีเฟรชโมเดล", "-- Horde models not loaded --": "-- โมเดล Horde ไม่ได้โหลด --", "Not connected...": "ไม่มีการเชื่อมต่อ...", "API url": "URL ของ API", "Example: http://127.0.0.1:5000/api ": "ตัวอย่าง: http://127.0.0.1:5000/api", "Connect": "เชื่อมต่อ", "Cancel": "ยกเลิก", "Novel API key": "คีย์ API ของ Novel", "Get your NovelAI API Key": "รับคีย์ API ของ NovelAI ได้ที่นี่", "Enter it in the box below": "กรอกคีย์ที่ได้รับลงในช่องด้านล่าง", "Novel AI Model": "โมเดลของ NovelAI", "No connection...": "ไม่มีการเชื่อมต่อ...", "API Type": "ประเภทของ API", "Default (completions compatible)": "ค่าเริ่มต้น (รองรับการเติมข้อความอัตโนมัติ)", "TogetherAI API Key": "คีย์ API ของ Together AI", "TogetherAI Model": "โมเดลของ TogetherAI", "-- Connect to the API --": "-- เชื่อมต่อ API --", "OpenRouter API Key": "คีย์ API ของ OpenRouter", "Click Authorize below or get the key from": "กดปุ่ม Authorize ข้างล่างเพื่อยืนยัน Persona หรือให้ทำการนำคีย์มาใส่เพื่อใช้งาน", "View Remaining Credits": "เช็คเครดิตคงเหลือ", "OpenRouter Model": "OpenRouter โมเดล", "Model Providers": "ผู้ให้บริการโมเดล", "InfermaticAI API Key": "คีย์ API ของ InfermaticAI", "InfermaticAI Model": "โมเดลของ InfermaticAI", "DreamGen API key": "คีย์ API ของ DreamGen", "DreamGen Model": "โมเดลของ DreamGen", "Mancer API key": "คีย์ API ของ Mancer", "Mancer Model": "โมเดลของ Mancer", "Make sure you run it with": "ให้แน่ใจว่าคุณเปิดมันโดยใช้...", "flag": "flag", "API key (optional)": "API key (ไม่บังคับ)", "Server url": "ที่อยู่ของเซิร์ฟเวอร์ (URL)", "Example: http://127.0.0.1:5000": "ตัวอย่าง: http://127.0.0.1:5000", "Custom model (optional)": "โมเดลที่กำหนดเอง (ไม่บังคับ)", "vllm-project/vllm": "vllm-project/vllm", "vLLM API key": "คีย์ API ของ vLLM", "Example: http://127.0.0.1:8000": "ตัวอย่าง: http://127.0.0.1:8000", "vLLM Model": "โมเดลของ vLLM", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine", "Aphrodite API key": "คีย์ API ของ Aphrodite", "Aphrodite Model": "โมเดลของ Aphrodite", "ggerganov/llama.cpp": "ggerganov/llama.cpp", "Example: http://127.0.0.1:8080": "ตัวอย่าง: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "ตัวอย่าง: http://127.0.0.1:11434", "Ollama Model": "โมเดลของ <PERSON><PERSON>ma", "Download": "ดาวน์โหลด", "Tabby API key": "คีย์ API ของ Tabby", "koboldcpp API key (optional)": "คีย์ API ของ koboldcpp (เพิ่มเติม)", "Example: http://127.0.0.1:5001": "ตัวอย่าง: http://127.0.0.1:5001", "Authorize": "อนุญาต", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "โปรดเข้าสู่ระบบเพื่อรับ OpenRouter API Token โดยใช้ OAuth Flow จากนั้นคุณจะถูกเปลี่ยนเส้นทางไปยังเว็บไซต์ของ openrouter.ai", "Bypass status check": "ข้ามการตรวจสอบสถานะ", "Chat Completion Source": "แหล่งที่มาของ Chat Completion", "Reverse Proxy": "Reverse Proxy", "Proxy Presets": "พรีเซ็ต Proxy", "Saved addresses and passwords.": "ที่อยู่และรหัสผ่านที่บันทึกไว้", "Save Proxy": "เซฟพร็อกซี", "Delete Proxy": "ลบพร็อกซี", "Proxy Name": "ชื่อพร็อกซี", "This will show up as your saved preset.": "สิ่งนี้จะปรากฏเป็นพรีเซ็ตที่คุณเซฟไว้", "Proxy Server URL": "Proxy Server URL", "Alternative server URL (leave empty to use the default value).": "URL เซิร์ฟเวอร์สำรอง (เว้นว่างไว้เพื่อใช้ค่าเริ่มต้น)", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "โปรดลบคีย์ OAI API จริงของคุณออกจากหน้า API Panel ก่อนที่จะเริ่มพิมพ์ข้อความใด ๆ ลงในช่องนี้", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "เราไม่สามารถช่วยเหลือปัญหาจากการใช้พร็อกซีของ OpenAI ที่ไม่ได้รับการรับรองอย่างเป็นทางการ", "Doesn't work? Try adding": "ไม่ทำงาน? ลองเพิ่ม", "at the end!": "ไว้ท้ายสุด!", "Proxy Password": "รหัสผ่าน Proxy", "Will be used as a password for the proxy instead of API key.": "สิ่งนี้จะถูกใช้เป็นรหัสผ่านสำหรับเข้าถึงพร็อกซี แทนที่คีย์ API", "Peek a password": "แสดงรหัสผ่าน", "OpenAI API key": "คีย์ API ของ OpenAI", "View API Usage Metrics": "ดูสถิติการใช้งาน API", "Follow": "ติดตาม", "these directions": "คำแนะนำเหล่านี้", "to get your OpenAI API key.": "เพื่อรับคีย์ API ของ OpenAI", "Use Proxy password field instead. This input will be ignored.": "ใช้ฟิลด์รหัสผ่าน Proxy แทน อินพุตนี้จะถูกเพิกเฉย", "OpenAI Model": "โมเดล OpenAI", "Bypass API status check": "ข้ามการตรวจสอบสถานะ API", "Show External models (provided by API)": "แสดงโมเดลภายนอก (ที่ API จัดให้)", "Get your key from": "รับคีย์ของคุณจาก", "Anthropic's developer console": "คอนโซลนักพัฒนาของ Anthropic", "Claude Model": "โมเดล Claude", "Window AI Model": "โมเดล Window AI", "Model Order": "ลำดับโมเดล", "Alphabetically": "เรียงตามตัวอักษร", "Price": "ราคา", "Context Size": "ขนาดของบริบท", "Group by vendors": "จัดกลุ่มตามผู้ขาย", "Group by vendors Description": "คำอธิบายการจัดกลุ่มตามผู้ขาย", "Allow fallback routes": "อนุญาตเส้นทางสำรอง", "Allow fallback routes Description": "คำอธิบายการอนุญาตเส้นทางสำรอง", "Scale API Key": "คีย์ API ของ Scale", "Clear your cookie": "ล้างคุกกี้ของคุณ", "Alt Method": "วิธีสำรอง", "AI21 API Key": "คีย์ API ของ AI21", "AI21 Model": "โมเดล AI21", "Google AI Studio API Key": "คีย์ API ของ Google AI Studio", "Google Model": "โมเดล Google", "MistralAI API Key": "คีย์ API ของ MistralAI", "MistralAI Model": "โมเดล MistralAI", "Groq API Key": "คีย์ API ของ Groq", "Groq Model": "โมเดล Groq", "Perplexity API Key": "คีย์ API ของ Perplexity", "Perplexity Model": "โมเดลของ Perplexity", "Cohere API Key": "คีย์ API ของ Cohere", "Cohere Model": "โมเดลของ Cohere", "Custom Endpoint (Base URL)": "จุดปลายทางกำหนดเอง (Base URL)", "Custom API Key": "คีย์ API แบบกำหนดเอง", "Available Models": "โมเดลที่สามารถใช้ได้", "Prompt Post-Processing": "การประมวลผลพรอมต์หลังการส่ง", "Applies additional processing to the prompt before sending it to the API.": "ใช้การประมวลผลเพิ่มเติมกับพรอมต์ก่อนส่งไปยัง API", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "ตรวจสอบการเชื่อมต่อ API โดยส่งข้อความทดสอบสั้นๆ โปรดทราบว่าคุณจะถูกเรียกเก็บเงินสำหรับการทดสอบนี้!", "Test Message": "ทดสอบการส่งข้อความ", "Auto-connect to Last Server": "เชื่อมต่อเซิฟเวอร์อัตโนมัติ", "Missing key": "โปรดใส่ API Key", "Key saved": "บันทึก API Key แล้ว", "View hidden API keys": "แสดง API Key", "AI Response Formatting": "แบบแผนการตอบกลับของ AI", "Advanced Formatting": "แบบแผนขั้นสูง", "Context Template": "เทมเพลต Context", "Auto-select this preset for Instruct Mode": "เลือกพรีเซ็ตนี้โดยอัตโนมัติสำหรับโหมดคำสั่ง", "Story String": "สตริงเรื่องราว", "Example Separator": "ตัวแยกตัวอย่าง", "Chat Start": "เริ่มต้นการแชท", "Add Chat Start and Example Separator to a list of stopping strings.": "เพิ่ม Chat Start และ Example Separator ไปยังรายการสตริงหยุด", "Use as Stop Strings": "ใช้เป็นสตริงหยุด", "Allow Jailbreak": "อนุญาต Jailbreak", "Context Order": "ลำดับ Context", "Summary": "สรุป", "Author's Note": "หมายเหตุจากผู้เขียน", "Example Dialogues": "ตัวอย่างบทสนทนา", "Hint": "คำแนะนำ", "In-Chat Position not affected": "ตำแหน่งในแชทไม่ได้รับผลกระทบ", "Instruct Mode": "โหมดคำสั่ง", "Enabled": "เปิดใช้งาน", "instruct_bind_to_context": "ผูกกับ context", "Bind to Context": "ผูกกับ Context", "Presets": "พรีเซ็ต", "Auto-select this preset on API connection": "เลือกพรีเซ็ตนี้โดยอัตโนมัติเมื่อเชื่อมต่อ API", "Activation Regex": "Regex การเปิดใช้งาน", "Wrap Sequences with Newline": "ห่อลำดับด้วยบรรทัดใหม่", "Replace Macro in Sequences": "แทนที่แมโครในลำดับ", "Skip Example Dialogues Formatting": "ข้ามการจัดรูปแบบตัวอย่างบทสนทนา", "Include Names": "รวมชื่อ", "Force for Groups and Personas": "บังคับสำหรับกลุ่มและ Persona", "System Prompt": "Prompt ของระบบ", "Instruct Mode Sequences": "ลำดับโหมดคำสั่ง", "System Prompt Wrapping": "การห่อหุ้ม System Prompt", "Inserted before a System prompt.": "แทรกก่อน System prompt", "System Prompt Prefix": "คำนำหน้า System Prompt", "Inserted after a System prompt.": "แทรกหลัง System prompt", "System Prompt Suffix": "คำต่อท้าย System Prompt", "Chat Messages Wrapping": "การห่อหุ้มข้อความแชท", "Inserted before a User message and as a last prompt line when impersonating.": "แทรกก่อนข้อความผู้ใช้และเป็นบรรทัดพรอมต์สุดท้ายเมื่อสวมบทบาท", "User Message Prefix": "คำนำหน้าข้อความผู้ใช้", "Inserted after a User message.": "แทรกหลังข้อความผู้ใช้", "User Message Suffix": "คำต่อท้ายข้อความผู้ใช้", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "แทรกก่อนข้อความผู้ช่วยและเป็นบรรทัดพรอมต์สุดท้ายเมื่อสร้างการตอบกลับ AI", "Assistant Message Prefix": "คำนำหน้าข้อความผู้ช่วย", "Inserted after an Assistant message.": "แทรกหลังข้อความผู้ช่วย", "Assistant Message Suffix": "คำต่อท้ายข้อความผู้ช่วย", "Inserted before a System (added by slash commands or extensions) message.": "แทรกก่อนข้อความระบบ (ที่เพิ่มโดยคำสั่ง slash หรือส่วนขยาย)", "System Message Prefix": "คำนำหน้าข้อความระบบ", "Inserted after a System message.": "แทรกหลังข้อความระบบ", "System Message Suffix": "คำต่อท้ายข้อความระบบ", "If enabled, System Sequences will be the same as User Sequences.": "หากเปิดใช้งาน System Sequences จะเหมือนกับ User Sequences", "System same as User": "ระบบเหมือนกับผู้ใช้", "Misc. Sequences": "ลำดับเบ็ดเตล็ด", "Inserted before the first Assistant's message.": "แทรกก่อนข้อความแรกของผู้ช่วย", "First Assistant Prefix": "คำนำหน้าผู้ช่วยแรก", "instruct_last_output_sequence": "ลำดับผลลัพธ์สุดท้ายของคำสั่ง", "Last Assistant Prefix": "คำนำหน้าผู้ช่วยสุดท้าย", "Will be inserted as a last prompt line when using system/neutral generation.": "จะถูกแทรกเป็นบรรทัดพรอมต์สุดท้ายเมื่อใช้การสร้างแบบระบบ/เป็นกลาง", "System Instruction Prefix": "คำนำหน้าคำสั่งระบบ", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "หากมีลำดับหยุดถูกสร้างขึ้น ทุกอย่างหลังจากนั้นจะถูกลบออกจากผลลัพธ์ (รวมลำดับหยุดด้วย)", "Stop Sequence": "ลำดับหยุด", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "จะถูกแทรกที่ต้นประวัติการแชทหากไม่เริ่มต้นด้วยข้อความของผู้ใช้", "User Filler Message": "ข้อความเติมของผู้ใช้", "Context Formatting": "การจัดรูปแบบ Context", "(Saved to Context Template)": "(บันทึกไปยัง Context Template)", "Always add character's name to prompt": "เพิ่มชื่อตัวละครลงในพรอมต์เสมอ", "Generate only one line per request": "สร้างเพียงหนึ่งบรรทัดต่อคำขอ", "Trim Incomplete Sentences": "ตัดประโยคที่ไม่สมบูรณ์", "Include Newline": "รวมบรรทัดใหม่", "Misc. Settings": "การตั้งค่าเบ็ดเตล็ด", "Collapse Consecutive Newlines": "ยุบบรรทัดใหม่ที่ติดต่อกัน", "Trim spaces": "ตัดช่องว่าง", "Tokenizer": "ตัวแบ่งโทเค็น", "Token Padding": "การเพิ่มโทเค็น", "Start Reply With": "เริ่มตอบด้วย", "AI reply prefix": "คำนำหน้าการตอบของ AI", "Show reply prefix in chat": "แสดงคำนำหน้าการตอบในแชท", "Non-markdown strings": "Non-markdown strings", "separate with commas w/o space between": "แยกด้วยเครื่องหมายจุลภาคโดยไม่มีช่องว่าง", "Custom Stopping Strings": "สตริงหยุดกำหนดเอง", "JSON serialized array of strings": "อาร์เรย์ของสตริงในรูปแบบ JSON", "Replace Macro in Stop Strings": "แทนที่แมโครในสตริงหยุด", "Auto-Continue": "ต่อบทอัตโนมัติ", "Allow for Chat Completion APIs": "อนุญาตสำหรับ Chat Completion APIs", "Target length (tokens)": "ความยาวเป้าหมาย (โทเค็น)", "World Info": "ข้อมูลโลก (World info)", "Locked = World Editor will stay open": "Locked = สามารถแก้ไขข้อมูลโลกได้เสมอ", "Worlds/Lorebooks": "ข้อมูลโลก/Lorebooks", "Active World(s) for all chats": "ข้อมูลโลกที่ถูกเปิดใช้งานทุกแชท", "-- World Info not found --": "-- ไม่พบข้อมูลโลก --", "Global World Info/Lorebook activation settings": "ตั้งค่าการใช้งานข้อมูลโลก/Lorebook", "Click to expand": "กดเพื่อแสดงข้อมูล", "Scan Depth": "<PERSON><PERSON>", "Context %": "Context %", "Budget Cap": "Budget Cap", "(0 = disabled)": "(0 = ปิดใช้งาน)", "Scan chronologically until reached min entries or token budget.": "สแกนตามลำดับเวลาจนกว่าจะถึงรายการขั้นต่ำหรืองบโทเค็น", "Min Activations": "การเปิดใช้งานขั้นต่ำ", "Max Depth": "ความลึกสูงสุด", "(0 = unlimited, use budget)": "(0 = ไม่จำกัด ใช้งบประมาณ)", "Insertion Strategy": "กลยุทธ์การแทรก", "Sorted Evenly": "เรียงลำดับเท่าๆ กัน", "Create a new World Info": "สร้าง Lorebook ใหม่", "Character Lore First": "เลือกใช้เนื้อหาตัวละครก่อน", "Global Lore First": "เลือกใช้เนื้อหาหลักของโลกก่อน", "Entries can activate other entries by mentioning their keywords": "สามารถเรียกใช้รายการเนื้อหาที่เกี่ยวข้องกันด้วย Keywords", "Recursive Scan": "Recursive Scan", "Lookup for the entry keys in the context will respect the case": "Lookup for the entry keys in the context will respect the case", "Case Sensitive": "แยกตัวพิมพ์ใหญ่-เล็ก", "If the entry key consists of only one word, it would not be matched as part of other words": "If the entry key consists of only one word, it would not be matched as part of other words", "Match Whole Words": "ตรงกับคำทั้งหมด", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "จะเลือกเนื้อหาที่มี keyword ตรงกันมากที่สุดเท่านั้น", "Use Group Scoring": "Use Group Scoring", "Alert if your world info is greater than the allocated budget.": "Alert if your world info is greater than the allocated budget.", "Alert On Overflow": "<PERSON>ert On Overflow", "New": "ใหม่", "or": "หรือ", "--- Pick to Edit ---": "กดเพื่อแก้ไข", "Rename World Info": "แก้ไขชื่อของข้อมูลโลก", "Open all Entries": "แสดงเนื้อหาทั้งหมด", "Close all Entries": "ปิดการแสดงเนื้อหาทั้งหมด", "New Entry": "สร้างเนื้อหาใหม่", "Fill empty Memo/Titles with Keywords": "สร้าง Keywords แทน Memo/ชื่อเรื่องที่ว่าง", "Import World Info": "นำเข้าข้อมูลโลก/Lorebook", "Export World Info": "ส่งออกข้อมูลโลก/Lorebook", "Duplicate World Info": "ทำสำเนา ข้อมูลโลก/Lorebook", "Delete World Info": "Delete World Info", "Search...": "ค้นหา...", "Search": "ค้นหา", "Priority": "ลำดับความสำคัญ", "Custom": "ปรับแต่ง", "Title A-Z": "ชื่อเรียงจาก A-Z", "Title Z-A": "ชื่อเรียงจาก Z-A", "Tokens ↗": "Tokens ↗", "Tokens ↘": "Tokens ↘", "Depth ↗": "Depth ↗", "Depth ↘": "Depth ↘", "Order ↗": "Order ↗", "Order ↘": "Order ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "Trigger% ↗", "Trigger% ↘": "Trigger% ↘", "Refresh": "รีเฟรช", "User Settings": "ตั้งค่าส่วนผู้ใช้งาน", "Simple": "Simple", "Advanced": "ขั้นสูง", "UI Language": "ภาษา UI", "Account": "บัญชีผู้ใช้งาน", "Admin Panel": "แผงควบคุมผู้ดูแล", "Logout": "ออกจากระบบ", "Search Settings": "ค้นหาการตั้งค่า", "UI Theme": "ธีม UI", "Import a theme file": "นำเข้าไฟล์ธีม", "Export a theme file": "ส่งออกไฟล์ธีม", "Delete a theme": "ลบธีม", "Update a theme file": "อัพเดทไฟล์ธีม", "Save as a new theme": "บันทึกเป็นธีมใหม่", "Avatar Style:": "รูปแบบภาพ Avatar:", "Circle": "วงกลม", "Square": "สี่เหลี่ยมผืนผ้า", "Rectangle": "ทรงเหลี่ยม", "Chat Style:": "รูปแบบหน้าต่างแชท:", "Flat": "ขอบเรียบ", "Bubbles": "บับเบิล", "Document": "เอกสาร", "Specify colors for your theme.": "เลือกเฉดสีสำหรับธีมของคุณ", "Theme Colors": "สีธีม", "Main Text": "ข้อความหลัก", "Italics Text": "อักษรตัวเอียง", "Underlined Text": "อักษรขีดเส้นใต้", "Quote Text": "ข้อความคำพูด", "Shadow Color": "เงาตัวอักษร", "Chat Background": "สีพื้นหลังแชท", "UI Background": "พื้นหลัง UI", "UI Border": "เส้นขอบ UI", "User Message Blur Tint": "สีข้อความ User", "AI Message Blur Tint": "สีข้อความ AI", "Chat Width": "ความกว้างแชท", "Width of the main chat window in % of screen width": "ความกว้างของแชทเป็น % ของหน้าจอ", "Font Scale": "ขนาดตัวอักษร", "Font size": "ขนาดของตัวอักษร", "Blur Strength": "ระดับความเบลอภาพพื้นหลัง", "Blur strength on UI panels.": "ระดับความเบลอแผง UI", "Text Shadow Width": "ขนาดของเงาอักษร", "Strength of the text shadows": "ความหนาของเงาอักษร", "Disables animations and transitions": "ปิดแอนิเมชั่นและการเปลี่ยนภาพ", "Reduced Motion": "ลดการเคลื่อนไหว", "removes blur from window backgrounds": "ลบความเบลอภาพพื้นหลัง", "No Blur Effect": "ไม่มีเอฟเฟกต์เบลอ", "Remove text shadow effect": "ลบเงาตัวอักษร", "No Text Shadows": "ไม่มีเงาข้อความ", "Reduce chat height, and put a static sprite behind the chat window": "ลดความสูงของหน้าต่างแชทและวาง sprite ตัวละครไว้ด้านหลังหน้าต่างแชท", "Waifu Mode": "โหมด W<PERSON>fu", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "แสดงบริบทการบรรยายข้อความทั้งหมดเสมอโดยไม่ใส่ไว้หลัง '....'", "Auto-Expand Message Actions": "ขยายตัวอักษรอัตโนมัติ", "Alternative UI for numeric sampling parameters with fewer steps": "Alternative UI for numeric sampling parameters with fewer steps", "Zen Sliders": "Zen Sliders", "Entirely unrestrict all numeric sampling parameters": "Entirely unrestrict all numeric sampling parameters", "Mad Lab Mode": "Mad Lab Mode", "Time the AI's message generation, and show the duration in the chat log": "แสดงเวลาที่ใช้ในการสร้างข้อความของ AI, และให้แสดงจำนวนในประวัติแชท", "Message Timer": "แสดงเวลาที่ AI ใช้ในการตอบข้อความ", "Show a timestamp for each message in the chat log": "แสดงเวลาที่ส่งข้อความตอบกลับในประวัติการแชท", "Chat Timestamps": "แสดงเวลาที่ส่งข้อความตอบกลับในแชท", "Show an icon for the API that generated the message": "แสดงไอคอนสำหรับ API ที่สร้างข้อความ", "Model Icon": "แสดงไอคอน Model AI ที่ตอบแชท", "Show sequential message numbers in the chat log": "แสดงหมายเลขลำดับข้อความในประวัติแชท", "Message IDs": "แสดงเลขจำนวนข้อความ", "Hide avatars in chat messages.": "ซ่อนภาพ avatar ในแชทข้อความ", "Hide Chat Avatars": "ซ่อนรูปโปรไฟล์แชท", "Show the number of tokens in each message in the chat log": "แสดงจำนวนโทเค็นในแต่ละข้อความในบันทึกแชท", "Show Message Token Count": "แสดงจำนวนโทเค็นที่ Ai ตอบแชท", "Single-row message input area. Mobile only, no effect on PC": "พื้นที่ป้อนข้อความแบบแถวเดียวไม่ขึ้นบรรทัดใหม่ เฉพาะในมือถือเท่านั้น", "Compact Input Area (Mobile)": "พื้นที่ป้อนข้อความขนาดกระทัดรัด (มือถือ)", "In the Character Management panel, show quick selection buttons for favorited characters": "แสดงปุ่มลัดบอทตัวละครโปรดในแถบเลือกตัวละคร", "Characters Hotswap": "การเปลี่ยนบอทตัวละครแบบรวดเร็ว", "Enable magnification for zoomed avatar display.": "อนุญาตการซูมภาพโปรไฟล์อวาตาร์", "Avatar Hover Magnification": "Avatar Hover Magnification", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "เปิดเอฟเฟกต์ Magnification เมื่อคลิกรูปตัวละครค้างไว้ หลังจากคลิกดูรูปตัวละครแบบขยายในแชท", "Show tagged character folders in the character list": "แสดงโฟลเดอร์ตัวละครตามแท็กในรายการ", "Tags as Folders": "Tags as Folders", "Tags_as_Folders_desc": "Tags_as_Folders_desc", "Character Handling": "การจัดการตัวละคร", "If set in the advanced character definitions, this field will be displayed in the characters list.": "หากตั้งค่าในคำจำกัดความตัวละครขั้นสูง ฟิลด์นี้จะแสดงในรายการตัวละคร", "Char List Subheader": "หัวข้อย่อยรายการตัวละคร", "Character Version": "เวอร์ชั่นของตัวละคร", "Created by": "สร้างโดย", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "ใช้การจับคู่แบบเลือน และค้นหาตัวละครในรายการด้วยฟิลด์ข้อมูลทั้งหมด ไม่ใช่แค่ชื่อ", "Advanced Character Search": "การค้นหาตัวละครขั้นสูง", "If checked and the character card contains a prompt override (System Prompt), use that instead": "ใช้ System Prompt ของการ์ดตัวละคร (ถ้ามี)", "Prefer Character Card Prompt": "ใช้ Prompt ของการ์ดตัวละคร (ถ้ามี)", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "หากเลือกและการ์ดตัวละครมี jailbreak override (คำแนะนำหลังประวัติ) ให้ใช้แทน", "Prefer Character Card Jailbreak": "ใช้ Jailbreak ของการ์ดตัวละคร (ถ้ามี)", "never_resize_avatars_tooltip": "คำแนะนำไม่ปรับขนาดรูปตัวแทน", "Never resize avatars": "ห้ามปรับขนาดรูปตัวแทน", "Show actual file names on the disk, in the characters list display only": "แสดงชื่อไฟล์จริงในดิสก์ แสดงเฉพาะในรายการตัวละคร", "Show avatar filenames": "แสดงชื่อไฟล์ของรูปตัวแทน", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "แจ้งให้นำเข้าแท็กที่ฝังในการ์ดเมื่อนำเข้าตัวละคร มิฉะนั้นแท็กที่ฝังจะถูกเพิกเฉย", "Import Card Tags": "นำเข้าการ์ดแท็ก", "Hide character definitions from the editor panel behind a spoiler button": "ซ่อนคำจำกัดความตัวละครจากแผงตัวแก้ไขด้วยปุ่มสปอยเลอร์", "Spoiler Free Mode": "โหมดปลอดสปอยเลอร์", "Miscellaneous": "Miscellaneous", "Reload and redraw the currently open chat": "โหลดและวาดแชทที่เปิดอยู่ใหม่", "Reload Chat": "รีโหลดแชท", "Debug Menu": "เมนูดีบัก", "Smooth Streaming": "การสตรีมแบบนุ่มนวล", "Experimental feature. May not work for all backends.": "ฟีเจอร์ทดลอง อาจไม่ทำงานกับแบ็กเอนด์ทั้งหมด", "Slow": "ช้า", "Fast": "เร็ว", "Play a sound when a message generation finishes": "เล่นเสียงเมื่อการสร้างข้อความเสร็จสิ้น", "Message Sound": "เสียงข้อความ", "Only play a sound when ST's browser tab is unfocused": "เล่นเสียงเฉพาะเมื่อแท็บเบราว์เซอร์ของ ST ไม่ได้โฟกัส", "Background Sound Only": "เฉพาะเสียงของพื้นหลังเท่านั้น", "Reduce the formatting requirements on API URLs": "ลดข้อกำหนดการจัดรูปแบบสำหรับ URL ของ API", "Relaxed API URLS": "URL API แบบผ่อนปรน", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "ถามเพื่อนำเข้า World Info/Lorebook สำหรับตัวละครใหม่ทุกตัวที่มี lorebook ฝังอยู่ หากไม่เลือก จะแสดงข้อความสั้นๆ แทน", "Lorebook Import Dialog": "กล่องโต้ตอบการนำเข้า Lorebook", "Restore unsaved user input on page refresh": "คืนค่าข้อมูลผู้ใช้ที่ไม่ได้บันทึกเมื่อรีเฟรชหน้า", "Restore User Input": "คืนค่าข้อมูลผู้ใช้", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "อนุญาตให้จัดตำแหน่งองค์ประกอบ UI บางส่วนด้วยการลาก เฉพาะ PC ไม่มีผลกับมือถือ", "Movable UI Panels": "แผง UI ที่เคลื่อนย้ายได้", "MovingUI preset. Predefined/saved draggable positions": "พรีเซ็ต MovingUI ตำแหน่งลากที่กำหนดไว้ล่วงหน้า/บันทึกไว้", "MUI Preset": "พรีเซ็ต MUI", "Save movingUI changes to a new file": "บันทึกการเปลี่ยนแปลง movingUI เป็นไฟล์ใหม่", "Reset MovingUI panel sizes/locations.": "รีเซ็ตขนาด/ตำแหน่งแผง MovingUI", "Apply a custom CSS style to all of the ST GUI": "ใช้สไตล์ CSS แบบกำหนดเองกับ GUI ของ ST ทั้งหมด", "Custom CSS": "CSS แบบกำหนดเอง", "Expand the editor": "ขยายตัวแก้ไข", "Chat/Message Handling": "การจัดการแชท/ข้อความ", "# Messages to Load": "จำนวนข้อความที่โหลด", "The number of chat history messages to load before pagination.": "จำนวนข้อความในประวัติแชทที่จะโหลดก่อนแบ่งหน้า", "(0 = All)": "(0 = ทั้งหมด)", "Streaming FPS": "อัตราเฟรมการสตรีม", "Update speed of streamed text.": "ความเร็วการอัปเดตข้อความที่สตรีม", "Example Messages Behavior": "พฤติกรรมข้อความตัวอย่าง", "Gradual push-out": "ผลักออกทีละน้อย", "Always include examples": "รวมตัวอย่างเสมอ", "Never include examples": "ไม่รวมตัวอย่าง", "Send on Enter": "ส่งเมื่อกด Enter", "Disabled": "ปิดใช้งาน", "Automatic (PC)": "อัตโนมัติ (PC)", "Press Send to continue": "กด Send เพื่อดำเนินต่อ", "Show a button in the input area to ask the AI to continue (extend) its last message": "แสดงปุ่มในพื้นที่ป้อนข้อมูลเพื่อขอให้ AI ดำเนินต่อ (ขยาย) ข้อความสุดท้าย", "Quick 'Continue' button": "ปุ่ม 'ดำเนินการต่อ' ด่วน", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "แสดงปุ่มลูกศรในข้อความสุดท้ายในแชทเพื่อสร้างการตอบกลับ AI ทางเลือก ทั้ง PC และมือถือ", "Swipes": "ปัด", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "อนุญาตให้ใช้ท่าทางปัดในข้อความสุดท้ายในแชทเพื่อเรียกใช้การสร้างแบบปัด เฉพาะมือถือ ไม่มีผลกับ PC", "Gestures": "ท่าทาง", "Auto-load Last Chat": "โหลดแชทล่าสุดอัตโนมัติ", "Auto-scroll Chat": "เลื่อนแชทอัตโนมัติ", "Save edits to messages without confirmation as you type": "บันทึกการแก้ไขข้อความโดยอัตโนมัติขณะพิมพ์โดยไม่ต้องยืนยัน", "Auto-save Message Edits": "บันทึกการแก้ไขข้อความอัตโนมัติ", "Confirm message deletion": "ยืนยันการลบข้อความ", "Auto-fix Markdown": "แก้ไข Markdown อัตโนมัติ", "Disallow embedded media from other domains in chat messages": "ไม่อนุญาตสื่อที่ฝังจากโดเมนอื่นในข้อความแชท", "Forbid External Media": "ห้ามสื่อภายนอก", "Allow {{char}}: in bot messages": "อนุญาตให้ใช้ {{char}}: ในข้อความของบอท", "Allow {{user}}: in bot messages": "อนุญาตให้ใช้ {{user}}: ในข้อความของบอท", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "ข้ามการเข้ารหัสตัวอักษร < และ > ในข้อความ อนุญาต HTML markup บางส่วนรวมถึง Markdown", "Show tags in responses": "แสดง HTML tag ในข้อความ", "Allow AI messages in groups to contain lines spoken by other group members": "อนุญาตให้ข้อความ AI ในกลุ่มมีบรรทัดที่พูดโดยสมาชิกกลุ่มอื่น", "Relax message trim in Groups": "ผ่อนปรนการตัดข้อความในกลุ่ม", "Log prompts to console": "บันทึกพรอมต์ไปยังคอนโซล", "Requests logprobs from the API for the Token Probabilities feature": "ขอ logprobs จาก API สำหรับฟีเจอร์ Token Probabilities", "Request token probabilities": "ขอความน่าจะเป็นของโทเค็น", "Automatically reject and re-generate AI message based on configurable criteria": "ปฏิเสธและสร้างข้อความ AI ใหม่โดยอัตโนมัติตามเกณฑ์ที่กำหนดได้", "Auto-swipe": "ปัดแบบอัตโนมัติ", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "เปิดการใช้งานฟังก์ชั่นปัดแบบอัตโนมัติ การตั้งค่าในส่วนนี้มีผลก็ต่อเมื่อเปิดใช้งานฟังก์ชั่นปัดแบบอัตโนมัติแล้วเท่านั้น", "Minimum generated message length": "ความยาวข้อความที่สร้างขั้นต่ำ", "If the generated message is shorter than these many characters, trigger an auto-swipe": "หากข้อความที่สร้างสั้นกว่าจำนวนตัวอักษรนี้ จะเรียกใช้การปัดอัตโนมัติ", "Blacklisted words": "คำต้องห้าม", "words you dont want generated separated by comma ','": "คำที่คุณไม่ต้องการให้สร้าง คั่นด้วยเครื่องหมายจุลภาค ','", "Blacklisted word count to swipe": "จำนวนคำต้องห้ามเพื่อปัด", "Minimum number of blacklisted words detected to trigger an auto-swipe": "จำนวนขั้นต่ำของคำต้องห้ามที่ตรวจพบเพื่อเรียกใช้การปัดอัตโนมัติ", "AutoComplete Settings": "การตั้งค่าการเติมข้อความอัตโนมัติ", "Automatically hide details": "ซ่อนรายละเอียดอัตโนมัติ", "Determines how entries are found for autocomplete.": "กำหนดวิธีการค้นหารายการสำหรับการเติมข้อความอัตโนมัติ", "Autocomplete Matching": "การจับคู่การเติมข้อความอัตโนมัติ", "Starts with": "เริ่มต้นด้วย", "Includes": "รวมถึง", "Fuzzy": "แบบเลือน", "Sets the style of the autocomplete.": "ตั้งค่าสไตล์ของการเติมข้อความอัตโนมัติ", "Autocomplete Style": "สไตล์การเติมข้อความอัตโนมัติ", "Follow Theme": "ตามธีม", "Dark": "มืด", "Sets the font size of the autocomplete.": "ตั้งค่าขนาดตัวอักษรของการเติมข้อความอัตโนมัติ", "Sets the width of the autocomplete.": "ตั้งค่าความกว้างของการเติมข้อความอัตโนมัติ", "Autocomplete Width": "ความกว้างการเติมข้อความอัตโนมัติ", "chat input box": "กล่องป้อนข้อความแชท", "entire chat width": "ความกว้างแชททั้งหมด", "full window width": "ความกว้างหน้าต่างเต็ม", "STscript Settings": "การตั้งค่า STscript", "Sets default flags for the STscript parser.": "ตั้งค่าแฟล็กเริ่มต้นสำหรับตัวแยกวิเคราะห์ STscript", "Parser Flags": "แฟล็กตัวแยกวิเคราะห์", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "เปลี่ยนเป็นการ escape ที่เข้มงวดกว่า อนุญาตให้ตัวอักษรคั่นทั้งหมดถูก escape ด้วยแบ็กสแลช และแบ็กสแลชก็สามารถ escape ได้เช่นกัน", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "แทนที่แมโคร {{getvar::}} และ {{getglobalvar::}} ทั้งหมดด้วยตัวแปรขอบเขตเพื่อหลีกเลี่ยงการแทนที่แมโครซ้ำ", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "เปลี่ยนรูปภาพพื้นหลัง", "Filter": "ตัวกรอง", "Automatically select a background based on the chat context": "เลือกพื้นหลังโดยอัตโนมัติตามบริบทของแชท", "Auto-select": "เลือกอัตโนมัติ", "System Backgrounds": "พื้นหลังของระบบ", "Chat Backgrounds": "พื้นหลังแชท", "bg_chat_hint_1": "พื้นหลังของแชทที่ถูกสร้างด้วย", "bg_chat_hint_2": "จะปรากฏที่นี่", "Extensions": "โปรแกรมส่วนขยาย", "Notify on extension updates": "แจ้งเตือนเมื่อมีการอัปเดตส่วนขยาย", "Manage extensions": "จัดการส่วนขยาย", "Import Extension From Git Repo": "นำเข้าส่วนขยายจาก <PERSON><PERSON>", "Install extension": "ติดตั้งส่วนขยาย", "Extras API:": "Extras API:", "Auto-connect": "เชื่อมต่ออัตโนมัติ", "Extras API URL": "Extras API URL", "Extras API key (optional)": "คีย์ Extras API (ไม่บังคับ)", "Persona Management": "การจัดการ Persona", "How do I use this?": "ฉันจะใช้สิ่งนี้ยังไง?", "Click for stats!": "คลิกเพื่อดูสถิติ!", "Usage Stats": "ค่าสถิติการใช้งาน", "Backup your personas to a file": "สำรองข้อมูล persona ของคุณเป็นไฟล์", "Backup": "สำรองข้อมูล", "Restore your personas from a file": "คืนค่า persona ของคุณจากไฟล์", "Restore": "คืนค่า", "Create a dummy persona": "สร้าง persona จำลอง", "Create": "สร้าง", "Toggle grid view": "เปลี่ยนการมองแบบตาราง", "No persona description": "ไม่มีข้อมูล Persona", "Name": "ชื่อ", "Enter your name": "กรอกชื่อของคุณ", "Click to set a new User Name": "คลิกเลือกเพื่อตั้งชื่อผู้ใช้ใหม่", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "กดเพื่อล็อก Persona ที่คุณเลือกไว้กับแชทปัจจุบัน หรือให้กดอีกครั้งเพื่อปลดล็อก", "Click to set user name for all messages": "คลิกเพื่อตั้งชื่อผู้ใช้สำหรับข้อความทั้งหมด", "Persona Description": "ข้อมูล Persona", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "ตัวอย่าง: [{{user}} เป็นสาวสวยน่ารักอายุ 25 ปี]", "Tokens persona description": "คำอธิบาย persona ในโทเค็น", "Position:": "ตำแหน่ง:", "In Story String / Prompt Manager": "ในสตริงเรื่องราว / ตัวจัดการพรอมต์", "Top of Author's Note": "ด้านบนของหมายเหตุผู้เขียน", "Bottom of Author's Note": "ด้านล่างของหมายเหตุผู้เขียน", "In-chat @ Depth": "ในแชท @ ความลึก", "Depth:": "ความลึก:", "Role:": "บทบาท:", "System": "ระบบ", "User": "ผู้ใช้", "Assistant": "ผู้ช่วย", "Show notifications on switching personas": "แสดงการแจ้งเตือนเมื่อเปลี่ยน persona", "Allow multiple persona connections per character": "อนุญาตการเชื่อมต่อหลาย persona ต่อ 1 ตัวละคร", "Auto-lock a chosen persona to the chat": "ล็อก persona ที่เลือกไว้กับแชทโดยอัตโนมัติ", "Character Management": "จัดการตัวละคร", "Locked = Character Management panel will stay open": "ล็อค = แผงการจัดการตัวละครจะเปิดค้างไว้", "Select/Create Characters": "เลือก/สร้างตัวละคร", "Favorite characters to add them to HotSwaps": "ใส่ตัวละครโปรดเพื่อเพิ่มลงใน HotSwaps", "Token counts may be inaccurate and provided just for reference.": "จำนวนโทเค็นอาจไม่แม่นยำและให้ไว้เพื่ออ้างอิงเท่านั้น", "Total tokens": "จำนวนโทเค็นทั้งหมด", "Calculating...": "กำลังคำนวณผล...", "Tokens": "จำนวนโทเค็น", "Permanent tokens": "โทเค็นถาวร", "Permanent": "ถาวร", "About Token 'Limits'": "เกี่ยวกับ 'ขีดจำกัด' โทเค็น", "Toggle character info panel": "เปิด/ปิดแผงข้อมูลตัวละคร", "Name this character": "ชื่อของตัวละครนี้", "extension_token_counter": "ตัวนับโทเค็นส่วนขยาย", "Click to select a new avatar for this character": "กดเพื่อเลือกรูปตัวละครรูปใหม่", "Add to Favorites": "เพิ่มไปยังรายการโปรด", "Advanced Definition": "การตั้งค่าเพิ่มเติม", "Character Lore": "Character Lore", "Chat Lore": "Chat Lore", "Export and Download": "นำออกและดาวน์โหลดการ์ดตัวละคร", "Duplicate Character": "ทำสำเนาการ์ดตัวละครนั้นๆ", "Create Character": "สร้างตัวละคร", "Delete Character": "ลบตัวละคร", "More...": "เพิ่มเติม", "Link to World Info": "ลิงค์ตัวละครกับ Lorebook", "Import Card Lore": "นำเข้า Lorebook ของการ์ดตัวละคร", "Scenario Override": "เขียนทับ Scenario ของการ์ด", "Convert to Persona": "แปลงการ์ดตัวละครไปเป็น Persona", "Rename": "เปลี่ยนชื่อ", "Link to Source": "ลิงค์ไปยัง Source", "Replace / Update": "Replace / Update", "Import Tags": "นำเข้าแท็ก", "Search / Create Tags": "ค้นหา / สร้างแท็ก", "View all tags": "ดูแท็กทั้งหมด", "Creator's Notes": "ข้อความของผู้สร้าง", "Show / Hide Description and First Message": "แสดง / ซ่อนคำอธิบายและข้อความแรก", "Character Description": "คำอธิบายตัวละคร", "Click to allow/forbid the use of external media for this character.": "คลิกเพื่ออนุญาตหรือห้ามการใช้สื่อภายนอกสำหรับตัวละครนี้", "Ext. Media": "สื่อภายนอก", "Describe your character's physical and mental traits here.": "กรอกรายละเอียดเกี่ยวกับรูปลักษณ์และบุคลิกของตัวละครของคุณที่นี่", "First message": "ข้อความเริ่มต้น", "Click to set additional greeting messages": "คลิกเพื่อกำหนดข้อความทักทายเพิ่มเติม", "Alt. Greetings": "ข้อความทักทายแบบอื่น", "This will be the first message from the character that starts every chat.": "ตัวละครจะเริ่มแชททุกครั้งด้วยข้อความนี้", "Group Controls": "การควบคุมกลุ่ม", "Chat Name (Optional)": "ชื่อแชท (ไม่บังคับ)", "Click to select a new avatar for this group": "เลือกรูปภาพใหม่สำหรับกลุ่มนี้", "Group reply strategy": "กลยุทธ์การตอบกลับของกลุ่ม", "Natural order": "ลำดับธรรมชาติ", "List order": "ลำดับรายการ", "Group generation handling mode": "โหมดการจัดการการสร้างกลุ่ม", "Swap character cards": "สลับการ์ดตัวละคร", "Join character cards (exclude muted)": "รวมการ์ดตัวละคร (ยกเว้นที่ปิดเสียง)", "Join character cards (include muted)": "รวมการ์ดตัวละคร (รวมที่ปิดเสียง)", "Inserted before each part of the joined fields.": "แทรกก่อนแต่ละส่วนของฟิลด์ที่เชื่อมต่อ", "Join Prefix": "คำนำหน้าการเชื่อมต่อ", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "เมื่อเลือก 'เชื่อมต่อการ์ดตัวละคร' ฟิลด์ทั้งหมดของตัวละครจะถูกรวมเข้าด้วยกัน หมายความว่าในสตริงเรื่องราว คำอธิบายตัวละครทั้งหมดจะถูกรวมเป็นข้อความใหญ่หนึ่งข้อความ หากคุณต้องการแยกฟิลด์เหล่านั้น คุณสามารถกำหนดคำนำหน้าหรือคำต่อท้ายได้ที่นี่ ค่านี้รองรับแมโครปกติและจะแทนที่ {{char}} ด้วยชื่อตัวละครที่เกี่ยวข้องและ <FIELDNAME> ด้วยชื่อของส่วน (เช่น: description, personality, scenario, ฯลฯ)", "Inserted after each part of the joined fields.": "แทรกหลังแต่ละส่วนของฟิลด์ที่เชื่อมต่อ", "Join Suffix": "คำต่อท้ายการเชื่อมต่อ", "Set a group chat scenario": "ตั้งค่าสถานการณ์แชทกลุ่ม", "Click to allow/forbid the use of external media for this group.": "คลิกเพื่ออนุญาต/ห้ามการใช้สื่อภายนอกสำหรับกลุ่มนี้", "Restore collage avatar": "คืนค่าอวาตาร์แบบคอลลาจ", "Allow self responses": "อนุญาตการตอบกลับตนเอง", "Auto Mode": "โหมดอัตโนมัติ", "Auto Mode delay": "ความล่าช้าของโหมดอัตโนมัติ", "Hide Muted Member Sprites": "ซ่อน Sprite ของสมาชิกที่ปิดเสียง", "Current Members": "สมาชิกปัจจุบัน", "Add Members": "เพิ่มสมาชิก", "Create New Character": "สร้างตัวละครใหม่", "Import Character from File": "นำเข้าตัวละครจากไฟล์", "Import content from external URL": "นำเข้าเนื้อหาจาก URL ภายนอก", "Create New Chat Group": "สร้างกลุ่มการสนทนาใหม่", "Characters sorting order": "การเรียงลำดับตัวละคร", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "ใหม่ที่สุด", "Oldest": "เก่าที่สุด", "Favorites": "รายการโปรด", "Recent": "ล่าสุด", "Most chats": "แชทมากที่สุด", "Least chats": "แชทน้อยที่สุด", "Most tokens": "โทเค่นมากที่สุด", "Least tokens": "โทเค่นน้อยที่สุด", "Random": "สุ่ม", "Toggle character grid view": "เปิด/ปิด การดูตัวละครแบบตาราง", "Bulk_edit_characters": "แก้ไขตัวละครทั้งหมดพร้อมกัน", "Bulk select all characters": "เลือกตัวละครทั้งหมดพร้อมกัน", "Bulk delete characters": "ลบตัวละครทั้งหมด", "popup-button-save": "บันทึก", "popup-button-yes": "ตกลง", "popup-button-no": "ไม่", "popup-button-cancel": "ยกเลิก", "popup-button-import": "นำเข้า", "Advanced Definitions": "คำจำกัดความขั้นสูง", "Prompt Overrides": "การกำหนด prompt ใหม่", "(For Chat Completion and Instruct Mode)": "(สำหรับโหมดสนทนาและโหมดคำสั่ง)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "ใส่ {{original}} ลงในกล่องข้อความใดก็ได้ เพื่อดึงข้อความพร้อมท์เริ่มต้นตามการตั้งค่าระบบมาใช้งาน", "Main Prompt": "Main Prompt", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "เนื้อหาใด ๆ ที่ใส่ที่นี่จะมาแทนที่ข้อความพร้อมท์เจลเบรกเริ่มต้นที่ใช้กับตัวละครนี้ (ตามสเปกเวอร์ชัน 2: prompt ของระบบ)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "เนื้อหาใด ๆ ที่ใส่ที่นี่จะมาแทนที่ข้อความพร้อมท์เจลเบรกเริ่มต้นที่ใช้กับตัวละครนี้ (ตามสเปกเวอร์ชัน 2: คำแนะนำหลังประวัติการสนทนา)", "Creator's Metadata (Not sent with the AI prompt)": "ข้อมูล Meta ของผู้สร้าง (จะไม่ถูกส่งพร้อมกับคำสั่ง AI)", "Creator's Metadata": "ข้อมูล Meta ของผู้สร้าง", "(Not sent with the AI Prompt)": "จะไม่ถูกส่งพร้อมกับ AI Prompt", "Everything here is optional": "ทุกอย่างที่นี่ไม่บังคับ", "(Botmaker's name / Contact Info)": "(ชื่อผู้สร้างบอท / ข้อมูลติดต่อ)", "(If you want to track character versions)": "(ถ้าคุณต้องการติดตามเวอร์ชันของตัวละคร)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "อธิบายบอท,ให้คำแนะนำการใช้งานหรือระบุรุ่นของโมเดลแชทที่ได้ทดสอบมา ข้อมูลนี้จะแสดงในรายการตัวละคร", "Tags to Embed": "แท็กที่จะใส่", "(Write a comma-separated list of tags)": "(เขียนรายการแท็กโดยคั่นด้วยเครื่องหมายจุลภาค)", "Personality summary": "ลักษณะนิสัย , ลักษณะบุคลิกภาพ", "(A brief description of the personality)": "คำบรรยายสั้น ๆ ของลักษณะนิสัย", "Scenario": "ฉาก , สถานการณ์ , เหตุการณ์", "(Circumstances and context of the interaction)": "สถานการณ์และบริบทของการโต้ตอบ", "Character's Note": "บันทึกของตัวละคร , หมายเหตุของตัวละคร", "(Text to be inserted in-chat @ designated depth and role)": "(Text to be inserted in-chat @ designated depth and role)", "@ Depth": "@ Depth", "Role": "บทบาท (Role)", "Talkativeness": "ความช่างพูด", "How often the character speaks in group chats!": "ความถี่ในการตอบแชทกลุ่มของตัวละคร!", "How often the character speaks in": "ตัวละครพูดบ่อยแค่ไหนใน", "group chats!": "แชทกลุ่ม!", "Shy": "ขี้อาย", "Normal": "ปกติ", "Chatty": "ช่างจ้อ", "Examples of dialogue": "บทสนทนาตัวอย่าง", "Important to set the character's writing style.": "\"การตั้งค่าสไตล์การพูดของตัวละครเป็นสิ่งสำคัญ\"", "(Examples of chat dialog. Begin each example with START on a new line.)": "\"(ตัวอย่างบทสนทนาให้เริ่มต้นด้วย START ตามด้วยประโยคใหม่)\"", "Save": "บันทึก", "Chat History": "ประวัติการแชท", "Import Chat": "นำเข้าไฟล์แชท", "Copy to system backgrounds": "คัดลอกไปยังพื้นหลัง", "Rename background": "เปลี่ยนชื่อพื้นหลัง", "Lock": "ล็อค", "Unlock": "ปลดล็อค", "Delete background": "ลบพื้นหลัง", "Chat Scenario Override": "แทนที่สถานการณ์ในแชท", "Remove": "ลบ", "Type here...": "\"พิมพ์ที่นี่...\"", "Chat Lorebook": "แชท Lorebook", "Chat Lorebook for": "แชท Lorebook สำหรับ", "chat_world_template_txt": "chat_world_template_txt", "Select a World Info file for": "เลือกไฟล์ข้อมูลโลกสำหรับ", "Primary Lorebook": "Lorebook หลัก", "A selected World Info will be bound to this character as its own Lorebook.": "\"ข้อมูลโลก/Lorebook ที่เลือกจะถูกผูกติดกับตัวละครเหมือนเป็นเนื้อหาของมัน\"", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "เมื่อสร้างการตอบกลับ AI จะรวมกับรายการจากตัวเลือก World Info ทั่วไป", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "เมื่อส่งออกบอทจะมี Lorebook ที่ฝังไว้ติดไปกับบอทตัวละครด้วยทุกครั้ง", "Additional Lorebooks": "Lorebook เพิ่มเติม", "Associate one or more auxillary Lorebooks with this character.": "เชื่อมโยงหนึ่งหรือหลาย Lorebook เสริมกับตัวละครนี้", "NOTE: These choices are optional and won't be preserved on character export!": "หมายเหตุ: ตัวเลือกเหล่านี้ไว้สำหรับเป็นทางเลือกเฉพาะทางเท่านั้น และจะไม่ถูกผูกไปด้วยเมื่อนำการ์ดตัวละครออก!", "Rename chat file": "เปลี่ยนชื่อไฟล์แชท", "Export JSONL chat file": "ส่งออกไฟล์แชทในรูปแบบJSONL", "Download chat as plain text document": "ดาวน์โหลดแชทเป็นไฟล์ข้อความธรรมดา", "Delete chat file": "ลบแชทไฟล์", "Use tag as folder": "ใช้แท็กเป็นโฟลเดอร์", "Hide on character card": "ซ่อนในการ์ดตัวละคร", "Delete tag": "ลบแท็ก", "Entry Title/Memo": "ชื่อเรื่อง/บันทึก", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled", "WI_Entry_Status_Constant": "Constant", "WI_Entry_Status_Normal": "Normal", "WI_Entry_Status_Vectorized": "Vectorized", "WI_Entry_Status_Disabled": "Disabled", "T_Position": "T_Position", "Before Char Defs": "Before <PERSON><PERSON>", "After Char Defs": "After <PERSON><PERSON>", "Before EM": "Before EM", "After EM": "After EM", "Before AN": "Before AN", "After AN": "After AN", "at Depth System": "at Depth System", "at Depth User": "at Depth User", "at Depth AI": "at Depth AI", "Depth": "De<PERSON><PERSON>", "Order:": "Order:", "Order": "Order", "Trigger %:": "Trigger %:", "Probability": "Probability", "Duplicate world info entry": "Duplicate world info entry", "Delete world info entry": "Delete world info entry", "Comma separated (required)": "ต้องคั่นด้วยเครื่องหมายจุลภาค , (จำเป็นต้องกรอก)", "Primary Keywords": "คีย์เวิร์ดหลัก", "Keywords or Regexes": "Keywords or Regexes", "Comma separated list": "คั่นด้วยเครื่องหมาย comma", "Switch to plaintext mode": "Switch to plaintext mode", "Logic": "Logic", "AND ANY": "AND ANY", "AND ALL": "AND ALL", "NOT ALL": "NOT ALL", "NOT ANY": "NOT ANY", "(ignored if empty)": "จะไม่ถูกนำมาพิจารณาถ้าว่างเปล่า", "Optional Filter": "Optional Filter", "Keywords or Regexes (ignored if empty)": "คำสำคัญหรือรูปแบบการค้นหา (จะไม่ถูกนำมาพิจารณาถ้าว่างเปล่า)", "Comma separated list (ignored if empty)": "Comma separated list (ignored if empty)", "Use global setting": "ใช้การตั้งค่าทั่วไป", "Case-Sensitive": "รวจสอบความแตกต่างของตัวพิมพ์ใหญ่และตัวพิมพ์เล็ก", "Yes": "ใช่", "No": "ไม่", "Can be used to automatically activate Quick Replies": "สามารถใช้เพื่อเปิดใช้งานการตอบกลับด่วนโดยอัตโนมัติ", "Automation ID": "รหัสอัตโนมัติ", "( None )": "( ไม่ระบุ )", "Content": "เนื้อหา", "Exclude from recursion": "ยกเว้นจากการทำซ้ำ", "Prevent further recursion (this entry will not activate others)": "ป้องกันการเรียกซ้ำเพิ่มเติม (รายการนี้จะไม่ทำให้รายการอื่นทำงาน)", "Delay until recursion (this entry can only be activated on recursive checking)": "หน่วงเวลาจนกว่าจะเกิดการเรียกซ้ำ (รายการนี้จะถูกเปิดใช้งานได้เฉพาะเมื่อตรวจสอบแบบเรียกซ้ำเท่านั้น)", "What this keyword should mean to the AI, sent verbatim": "เนื้อหาของ Entry นั้นๆ", "Filter to Character(s)": "กรองเฉพาะตัวละคร", "Character Exclusion": "Character Exclusion", "-- Characters not found --": "ไม่พบตัวละคร", "Inclusion Group": "Inclusion Group", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.", "Only one entry with the same label will be activated": "Only one entry with the same label will be activated", "A relative likelihood of entry activation within the group": "A relative likelihood of entry activation within the group", "Group Weight": "Group Weight", "Selective": "Selective", "Click to Edit": "คลิกเพื่อแก้ไข", "Use Probability": "Use Probability", "Add Memo": "เพิ่ม Memo", "Delete the background?": "ลบพื้นหลังหรือไม่?", "Text or token ids": "Text or token ids", "close": "ปิด", "prompt_manager_edit": "แก้ไข", "prompt_manager_name": "ชื่อ", "A name for this prompt.": "A name for this prompt.", "To whom this message will be attributed.": "To whom this message will be attributed.", "AI Assistant": "AI ผู้ช่วย", "prompt_manager_position": "position", "Next to other prompts (relative) or in-chat (absolute).": "Next to other prompts (relative) or in-chat (absolute).", "prompt_manager_relative": "relative", "prompt_manager_depth": "depth", "0 = after the last message, 1 = before the last message, etc.": "\"0 = หลังข้อความสุดท้าย, 1 = ก่อนข้อความสุดท้าย, เป็นต้น\"", "Prompt": "Prompt", "The prompt to be sent.": "The prompt to be sent.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "This prompt cannot be overridden by character cards, even if overrides are preferred.", "prompt_manager_forbid_overrides": "forbid overrides", "reset": "รีเซ็ต", "save": "บันทึก", "This message is invisible for the AI": "ข้อความนี้เอไอจะมองไม่เห็น", "Message Actions": "การดำเนินการกับข้อความ , การจัดการข้อความ", "Translate message": "แปลข้อความ", "Generate Image": "สร้างภาพ", "Narrate": "บรรยาย", "Exclude message from prompts": "ไม่รวมข้อความนี้ในพรอมต์", "Include message in prompts": "รวมข้อความนี้ในพรอมต์", "Embed file or image": "ฝังไฟล์หรือภาพ", "Create checkpoint": "สร้างจุดเช็คพอยท์", "Create Branch": "Create Branch", "Copy": "คัดลอก", "Open checkpoint chat": "เปิดแชทจากจุดบันทึก", "Edit": "แก้ไข", "Confirm": "ยืนยัน", "Copy this message": "คัดลอกข้อความนี้", "Delete this message": "ลบข้อความนี้", "Move message up": "เลื่อนข้อความขึ้น", "Move message down": "เลื่อนข้อความลง", "Enlarge": "ขยาย", "Welcome to SillyTavern!": "ยินดีต้อนรับสู่ SillyTavern!", "welcome_message_part_1": "ยินดีต้อนรับสู่ SillyTavern! แอปพลิเคชันแชทบอทสำหรับมือโปร", "welcome_message_part_2": "เพื่อเริ่มใช้งาน คุณจะต้องเชื่อมต่อกับ API", "welcome_message_part_3": "โดยใช้เมนู API Connections ที่ไอคอนรูปปลั๊กด้านบน", "welcome_message_part_4": "หากคุณเป็นมือใหม่ ให้เปิดโหมด UI แบบเบสิคด้วยการพิมพ์คำสั่ง", "welcome_message_part_5": "เพื่อเรียนรู้เรื่องคำสั่งและสัญลักษณ์มาโคร", "welcome_message_part_6": "หากมีคำถามหรือข้อสงสัย สามารถเข้าร่วมดิสคอร์ดของเราได้ที่นี่", "Discord server": "เซิฟเวอร์ดิสคอร์ด", "welcome_message_part_7": "", "SillyTavern is aimed at advanced users.": "SillyTavern เหมาะสำหรับผู้ใช้งานระดับสูง", "If you're new to this, enable the simplified UI mode below.": "หากคุณเป็นผู้ใช้ใหม่ โปรดเปิดโหมดUI แบบง่ายด้านล่าง", "Change it later in the 'User Settings' panel.": "สามารถเปลี่ยนแปลงได้ภายหลังในรายการ ‘การตั้งค่าผู้ใช้'", "Enable simple UI mode": "เปิดใช้งานโหมด UI แบบง่าย", "Looking for AI characters?": "กำลังมองหาตัวละคร AI อยู่หรือไม่?", "onboarding_import": "Import", "from supported sources or view": "จากแหล่งที่รองรับหรือดู", "Sample characters": "ตัวอย่างตัวละคร", "Your Persona": "Persona ของคุณ", "Before you get started, you must select a persona name.": "ก่อนเริ่มต้นใช้งาน คุณต้องเลือกชื่อ Persona ก่อน", "welcome_message_part_8": "ข้อมูลนี้สามารถเปลี่ยนแปลงได้ตลอดเวลาที่ไอค่อน", "welcome_message_part_9": "", "Persona Name:": "ชื่อ Persona :", "Temporarily disable automatic replies from this character": "ปิดการตอบกลับอัตโนมัติจากตัวละครนี้ชั่วคราว", "Enable automatic replies from this character": "เปิดใช้งานการตอบกลับอัตโนมัติจากตัวละครนี้", "Trigger a message from this character": "เรียกให้ตัวละครนี้ส่งข้อความ", "Move up": "เลื่อนขึ้น", "Move down": "เลื่อนลง", "View character card": "ดูการ์ดตัวละคร", "Remove from group": "ลบออกจากกลุ่ม", "Add to group": "เพิ่มเข้ากลุ่ม", "Alternate Greetings": "รูปแบบการทักทายอื่น ๆ", "Alternate_Greetings_desc": " รายละเอียดของรูปแบบการทักทายอื่น ๆ", "Alternate Greetings Hint": "Alternate Greetings Hint", "(This will be the first message from the character that starts every chat)": "(This will be the first message from the character that starts every chat)", "Forbid Media Override explanation": "Forbid Media Override explanation", "Forbid Media Override subtitle": "Forbid Media Override subtitle", "Always forbidden": "ไม่อนุญาตโดยเด็ดขาด", "Always allowed": "อนุญาตเสมอ", "View contents": "ดูเนื้อหา", "Remove the file": "ลบไฟล์", "Unique to this chat": "Unique to this chat", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "Checkpoints inherit the Note from their parent, and can be changed individually after that.", "Include in World Info Scanning": "Include in World Info Scanning", "Before Main Prompt / Story String": "Before Main Prompt / Story String", "After Main Prompt / Story String": "ข้อความหลังพรอมต์หลัก / ส่วนเริ่มต้นของเรื่อง", "as": "เช่น", "Insertion Frequency": "Insertion Frequency", "(0 = Disable, 1 = Always)": "(0 = ปิดใช้งาน, 1 = เปิดใช้งานเสมอ)", "User inputs until next insertion:": "User inputs until next insertion:", "Character Author's Note (Private)": "Character Author's Note (Private)", "Won't be shared with the character card on export.": "Won't be shared with the character card on export.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "จะถูกเพิ่มเป็นบันทึกของผู้เขียนสำหรับตัวละครนี้โดยอัตโนมัติ สามารถใช้ในกลุ่มได้ แต่ไม่สามารถแก้ไขได้เมื่อมีการเปิดแชทกลุ่มอยู่", "Use character author's note": "Use character author's note", "Replace Author's Note": "Replace Author's Note", "Default Author's Note": "Default Author's Note", "Will be automatically added as the Author's Note for all new chats.": "Will be automatically added as the Author's Note for all new chats.", "Chat CFG": "Chat CFG", "1 = disabled": "1 = ปิดใช้งาน", "write short replies, write replies using past tense": "write short replies, write replies using past tense", "Positive Prompt": "Positive Prompt", "Use character CFG scales": "Use character CFG scales", "Character CFG": "Character CFG", "Will be automatically added as the CFG for this character.": "Will be automatically added as the CFG for this character.", "Global CFG": "Global CFG", "Will be used as the default CFG options for every chat unless overridden.": "Will be used as the default CFG options for every chat unless overridden.", "CFG Prompt Cascading": "CFG Prompt Cascading", "Combine positive/negative prompts from other boxes.": "Combine positive/negative prompts from other boxes.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.", "Always Include": "Always Include", "Chat Negatives": "ข้อเสียในการแชท", "Character Negatives": "ลักษณะนิสัยด้านลบของตัวละคร", "Global Negatives": "Global Negatives", "Custom Separator:": "Custom Separator:", "Insertion Depth:": "Insertion Depth:", "Token Probabilities": "Token Probabilities", "Select a token to see alternatives considered by the AI.": "Select a token to see alternatives considered by the AI.", "Not connected to API!": "ไม่ได้เชื่อมต่อ API!", "Type a message, or /? for help": "Type a message, or /? for help", "Continue script execution": "Continue script execution", "Pause script execution": "Pause script execution", "Abort script execution": "Abort script execution", "Abort request": "Abort request", "Continue the last message": "ต่อจากข้อความล่าสุด", "Send a message": "ส่งข้อความ", "Close chat": "ปิดแชทนี้", "Toggle Panels": "เปลี่ยนไปหน้าจัดการแชท", "Back to parent chat": "กลับไปที่แชทหลัก", "Save checkpoint": "เซฟเช็คพอยต์", "Convert to group": "เปลี่ยนเป็นแชทกลุ่ม", "Start new chat": "สร้างแชทใหม่", "Manage chat files": "จัดการแชทไฟล์", "Delete messages": "ลบข้อความ", "Regenerate": "รีข้อความใหม่", "Ask AI to write your message for you": "ให้ AI ช่วยเขียนข้อความสำหรับคุณ", "Impersonate": "สวมบทบาทในมุมมองของ User", "Continue": "ต่อจากเดิม", "Bind user name to that avatar": "เชื่อมชื่อผู้ใช้กับรูปตัวแทนนั้น", "Change persona image": "เปลี่ยนรูป Persona", "Select this as default persona for the new chats.": "ตั้งค่าตัวละครหลักสำหรับแชทใหม่ทุกครั้ง", "Delete persona": "ลบ persona", "These characters are the winners of character design contests and have outstandable quality.": "ตัวละครเหล่านี้เป็นผู้ชนะการประกวดออกแบบตัวละคร", "Contest Winners": "ผู้ชนะการประกวด", "These characters are the finalists of character design contests and have remarkable quality.": "ตัวละครเหล่านี้เป็นผู้เข้ารอบสุดท้ายของการประกวดออกแบบตัวละคร ", "Featured Characters": "ตัวละครยอดนิยม", "Attach a File": "แนบไฟล์ (รูป,เอกสาร)", "Open Data Bank": "เปิด Data Bank", "Enter a URL or the ID of a Fandom wiki page to scrape:": "Enter a URL or the ID of a Fandom wiki page to scrape:", "Examples:": "ตัวอย่าง:", "Example:": "ตัวอย่าง:", "Single file": "ไฟล์เดี่ยว", "All articles will be concatenated into a single file.": "All articles will be concatenated into a single file.", "File per article": "File per article", "Each article will be saved as a separate file.": "Each article will be saved as a separate file.", "Data Bank": "Data Bank", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "These files will be available for extensions that support attachments (e.g. Vector Storage).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "Drag and drop files here to upload.", "Date (Newest First)": "เรียงจากวันที่ล่าสุดก่อน", "Date (Oldest First)": "เรียงจากวันที่เก่าสุดก่อน", "Name (A-Z)": "ชื่อ (A-Z)", "Name (Z-A)": "ชื่อ (Z-A)", "Size (Smallest First)": "ขนาด (เล็กที่สุดก่อน)", "Size (Largest First)": "ขนาด (ใหญ่ที่สุดก่อน)", "Bulk Edit": "แก้ไขกลุ่ม", "Select All": "เลือกทั้งหมด", "Select None": "ไม่เลือก", "Global Attachments": "ไฟล์แนบทั่วไป", "These files are available for all characters in all chats.": "ไฟล์เหล่านี้พร้อมใช้งานสำหรับตัวละครทั้งหมดในทุกแชท", "Character Attachments": "ไฟล์แนบตัวละคร", "These files are available the current character in all chats they are in.": "ไฟล์เหล่านี้พร้อมใช้งานสำหรับตัวละครปัจจุบันในทุกแชทที่มีส่วนร่วม", "Saved locally. Not exported.": "บันทึกในเครื่อง ไม่ส่งออก", "Chat Attachments": "ไฟล์แนบแชท", "These files are available to all characters in the current chat.": "ไฟล์เหล่านี้พร้อมใช้งานสำหรับตัวละครทั้งหมดในแชทปัจจุบัน", "Enter a base URL of the MediaWiki to scrape.": "ใส่ URL หลักของ MediaWiki ที่จะดึงข้อมูล", "Don't include the page name!": "อย่าใส่ชื่อหน้า!", "Enter web URLs to scrape (one per line):": "ใส่ URL เว็บที่จะดึงข้อมูล (หนึ่ง URL ต่อบรรทัด):", "Enter a video URL to download its transcript.": "ใส่ URL วิดีโอเพื่อดาวน์โหลดสคริปต์", "Expression API": "Expression API", "ext_sum_with": "สรุปด้วย:", "ext_sum_main_api": "API หลัก", "ext_sum_current_summary": "บทสรุปปัจจุบัน:", "ext_sum_restore_previous": "คืนค่าสถานะก่อนหน้า", "ext_sum_memory_placeholder": "บทสรุปจะถูกสร้างที่นี่...", "Trigger a summary update right now.": "สรุปทันที", "ext_sum_force_text": "สรุปทันที", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "ปิดการอัปเดตบทสรุปอัตโนมัติ ขณะหยุดชั่วคราว บทสรุปจะยังคงเดิม คุณยังสามารถบังคับให้อัปเดตได้โดยกดปุ่ม [สรุปทันที] (ใช้ได้เฉพาะกับ API หลักเท่านั้น)", "ext_sum_pause": "หยุดชั่วคราว", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "ละเว้น World Info และ Author's Note จากข้อความที่จะสรุป มีผลเฉพาะเมื่อใช้ API หลัก Extras API จะละเว้น WI/AN เสมอ", "ext_sum_no_wi_an": "ไม่มี WI/AN", "ext_sum_settings_tip": "แก้ไขพรอมต์สรุป ตำแหน่งแทรก และอื่นๆ", "ext_sum_settings": "การตั้งค่าบทสรุป", "ext_sum_prompt_builder": "ตัวสร้างพรอมต์", "ext_sum_prompt_builder_1_desc": "ส่วนขยายสร้างพรอมต์ของตัวเองโดยใช้ข้อความที่ยังไม่ได้สรุป บล็อกการแชทจนกว่าจะสร้างบทสรุปเสร็จ", "ext_sum_prompt_builder_1": "ดิบ, บล็อกกิ้ง", "ext_sum_prompt_builder_2_desc": "ส่วนขยายสร้างพรอมต์ของตัวเองโดยใช้ข้อความที่ยังไม่ได้สรุป ไม่บล็อกการแชทขณะสร้างบทสรุป แบ็กเอนด์ไม่ใช่ทั้งหมดที่รองรับโหมดนี้", "ext_sum_prompt_builder_2": "ดิบ, ไม่บล็อกกิ้ง", "ext_sum_prompt_builder_3_desc": "ส่วนขยายใช้ตัวสร้างพรอมต์หลักปกติและเพิ่มคำขอสรุปเป็นข้อความระบบสุดท้าย", "ext_sum_prompt_builder_3": "คลาสสิก, บล็อกกิ้ง", "Summary Prompt": "พรอมต์สรุป", "ext_sum_restore_default_prompt_tip": "คืนค่าพรอมต์เริ่มต้น", "ext_sum_prompt_placeholder": "พรอมต์นี้จะถูกส่งไปยัง AI เพื่อขอให้สร้างบทสรุป {{words}} จะถูกแทนที่ด้วยพารามิเตอร์ 'จำนวนคำ'", "ext_sum_target_length_1": "ความยาวบทสรุปเป้าหมาย", "ext_sum_target_length_2": "(", "ext_sum_target_length_3": "คำ)", "ext_sum_api_response_length_1": "ความยาวการตอบสนอง API", "ext_sum_api_response_length_2": "(", "ext_sum_api_response_length_3": "โทเค็น)", "ext_sum_0_default": "0 = เริ่มต้น", "ext_sum_raw_max_msg": "[ดิบ] ข้อความสูงสุดต่อคำขอ", "ext_sum_0_unlimited": "0 = ไม่จำกัด", "Update frequency": "ความถี่การอัปเดต", "ext_sum_update_every_messages_1": "อัปเดตทุก", "ext_sum_update_every_messages_2": "ข้อความ", "ext_sum_0_disable": "0 = ปิดใช้งาน", "ext_sum_auto_adjust_desc": "พยายามปรับช่วงเวลาโดยอัตโนมัติตามเมตริกของการแชท", "ext_sum_update_every_words_1": "อัปเดตทุก", "ext_sum_update_every_words_2": "คำ", "ext_sum_both_sliders": "หากทั้งสองสไลเดอร์เป็นค่าที่ไม่ใช่ศูนย์ ทั้งคู่จะทริกเกอร์การอัปเดตบทสรุปที่ช่วงเวลาของแต่ละอัน", "ext_sum_injection_template": "เทมเพลตการแทรก", "ext_sum_memory_template_placeholder": "{{summary}} จะถูกแทนที่ด้วยเนื้อหาบทสรุปปัจจุบัน", "ext_sum_injection_position": "ตำแหน่งการแทรก", "How many messages before the current end of the chat.": "จำนวนข้อความก่อนจุดสิ้นสุดปัจจุบันของการแชท", "ext_regex_title": "Regular Expression", "ext_regex_new_global_script": "+ โกลบอล", "ext_regex_new_scoped_script": "+ สโคป", "ext_regex_import_script": "นำเข้า", "ext_regex_global_scripts": "สคริปต์โกลบอล", "ext_regex_global_scripts_desc": "ใช้ได้กับตัวละครทั้งหมด บันทึกในการตั้งค่าท้องถิ่น", "ext_regex_scoped_scripts": "สคริปต์สโคป", "ext_regex_scoped_scripts_desc": "ใช้ได้เฉพาะตัวละครนี้ บันทึกในข้อมูลการ์ด", "Regex Editor": "ตัวแก้ไข Regex", "Test Mode": "โหมดทดสอบ", "ext_regex_desc": "Regex เป็นเครื่องมือสำหรับค้นหา/แทนที่สตริงโดยใช้นิพจน์ปกติ หากต้องการทราบรายละเอียด ให้คลิก [?] ข้างชื่อเรื่อง", "Input": "ป้อนข้อมูล", "ext_regex_test_input_placeholder": "พิมพ์ที่นี่...", "Output": "ผลลัพธ์", "ext_regex_output_placeholder": "ว่างเปล่า", "Script Name": "ชื่อสคริปต์", "Find Regex": "ค้นหา Regex", "Replace With": "แทนที่ด้วย", "ext_regex_replace_string_placeholder": "ใช้ {{match}} เพื่อรวมข้อความที่ตรงกันจากการค้นหา regex และใช้ $1, $2 เป็นต้น สำหรับกลุ่มที่จับได้", "Trim Out": "ตัดออก", "ext_regex_trim_placeholder": "ตัดส่วนที่ไม่ต้องการออกจากการจับคู่ regex โดยรวมก่อนการแทนที่ แยกแต่ละรายการด้วย Enter", "ext_regex_affects": "ส่งผลต่อ", "ext_regex_user_input": "ข้อมูลที่ผู้ใช้ป้อน", "ext_regex_ai_output": "ผลลัพธ์ AI", "Slash Commands": "คำสั่ง Slash", "ext_regex_min_depth_desc": "เมื่อใช้กับพรอมต์หรือการแสดงผล จะส่งผลต่อข้อความที่มีความลึกอย่างน้อย N ระดับเท่านั้น 0 = ข้อความสุดท้าย, 1 = ข้อความที่สองจากท้าย เป็นต้น นับเฉพาะรายการ WI @Depth และข้อความที่ใช้ได้ (ไม่ใช่ข้อความที่ซ่อนหรือระบบ)", "Min Depth": "ความลึกขั้นต่ำ", "ext_regex_min_depth_placeholder": "ไม่จำกัด", "ext_regex_max_depth_desc": "เมื่อใช้กับพรอมต์หรือการแสดงผล จะส่งผลต่อข้อความที่มีความลึกไม่เกิน N ระดับเท่านั้น 0 = ข้อความสุดท้าย, 1 = ข้อความที่สองจากท้าย เป็นต้น นับเฉพาะรายการ WI @Depth และข้อความที่ใช้ได้ (ไม่ใช่ข้อความที่ซ่อนหรือระบบ)", "ext_regex_other_options": "ตัวเลือกอื่นๆ", "Only Format Display": "จัดรูปแบบการแสดงผลเท่านั้น", "ext_regex_only_format_prompt_desc": "ประวัติการแชทจะไม่เปลี่ยนแปลง มีการเปลี่ยนแปลงเฉพาะพรอมต์เมื่อส่งคำขอ (ในระหว่างการสร้าง)", "Only Format Prompt (?)": "จัดรูปแบบพรอมต์เท่านั้น (?)", "Run On Edit": "ทำงานเมื่อแก้ไข", "ext_regex_substitute_regex_desc": "แทนที่ {{macros}} ใน Find Regex ก่อนการทำงาน", "Substitute Regex": "แทนที่ Regex", "ext_regex_import_target": "นำเข้าไปยัง:", "ext_regex_disable_script": "ปิดใช้งานสคริปต์", "ext_regex_enable_script": "เปิดใช้งานสคริปต์", "ext_regex_edit_script": "แก้ไขสคริปต์", "ext_regex_move_to_global": "ย้ายไปยังสคริปต์โกลบอล", "ext_regex_move_to_scoped": "ย้ายไปยังสคริปต์สโคป", "ext_regex_export_script": "ส่งออกสคริปต์", "ext_regex_delete_script": "ลบสคริปต์", "Trigger Stable Diffusion": "เรียกใช้ Stable Diffusion", "sd_Yourself": "ตัวคุณเอง", "sd_Your_Face": "ใบหน้าของคุณ", "sd_Me": "ฉัน", "sd_The_Whole_Story": "เรื่องราวทั้งหมด", "sd_The_Last_Message": "ข้อความสุดท้าย", "sd_Raw_Last_Message": "ข้อความสุดท้ายดิบ", "sd_Background": "พื้นหลัง", "Image Generation": "การสร้างภาพ", "Stop Image Generation": "หยุดการสร้างภาพ", "Generate Caption": "สร้างคำบรรยาย", "sd_refine_mode": "อนุญาตให้แก้ไขพรอมต์ด้วยตนเองก่อนส่งไปยัง API การสร้าง", "sd_refine_mode_txt": "แก้ไขพรอมต์ก่อนสร้าง", "sd_interactive_mode": "สร้างภาพโดยอัตโนมัติเมื่อส่งข้อความแบบ \"โปรดส่งรูปแมว\"", "sd_interactive_mode_txt": "โหมดโต้ตอบ", "sd_multimodal_captioning": "ใช้คำบรรยายมัลติโมดัลเพื่อสร้างพรอมต์สำหรับภาพพอร์ตเทรตผู้ใช้และตัวละครตามอวาตาร์", "sd_multimodal_captioning_txt": "ใช้คำบรรยายมัลติโมดัลสำหรับภาพพอร์ตเทรต", "sd_expand": "ขยายพรอมต์โดยอัตโนมัติโดยใช้โมเดลการสร้างข้อความ", "sd_expand_txt": "พรอมต์ปรับปรุงอัตโนมัติ", "sd_snap": "จัดคำขอการสร้างให้ตรงกับความละเอียดที่รู้จักที่ใกล้เคียงที่สุดด้วยอัตราส่วนที่บังคับ (พอร์ตเทรต, พื้นหลัง) โดยรักษาจำนวนพิกเซลสัมบูรณ์ (แนะนำสำหรับ SDXL)", "sd_snap_txt": "จัดความละเอียดอัตโนมัติ", "Source": "แหล่งที่มา", "sd_auto_url": "ตัวอย่าง: {{auto_url}}", "Authentication (optional)": "การยืนยันตัวตน (ไม่บังคับ)", "Example: username:password": "ตัวอย่าง: ชื่อผู้ใช้:รหัสผ่าน", "Important:": "สำคัญ:", "sd_auto_auth_warning_1": "ทำงาน SD Web UI ด้วย", "sd_auto_auth_warning_2": "แฟล็ก! เซิร์ฟเวอร์ต้องสามารถเข้าถึงได้จากเครื่อง SillyTavern โฮสต์", "sd_drawthings_url": "ตัวอย่าง: {{drawthings_url}}", "sd_drawthings_auth_txt": "ทำงานแอป DrawThings โดยเปิดใช้งานสวิตช์ HTTP API ใน UI เซิร์ฟเวอร์ต้องสามารถเข้าถึงได้จากเครื่อง SillyTavern โฮสต์", "sd_vlad_url": "ตัวอย่าง: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "The server must be accessible from the SillyTavern host machine.", "Hint: Save an API key in AI Horde API settings to use it here.": "Hint: Save an API key in AI Horde API settings to use it here.", "Allow NSFW images from Horde": "อนุญาตภาพที่ไม่เหมาะสมจาก Horde", "Sanitize prompts (recommended)": "Sanitize prompts (recommended)", "Automatically adjust generation parameters to ensure free image generations.": "Automatically adjust generation parameters to ensure free image generations.", "Avoid spending Anlas": "Avoid spending Anlas", "Opus tier": "Opus tier", "View my Anlas": "View my Anlas", "These settings only apply to DALL-E 3": "These settings only apply to DALL-E 3", "Image Style": "รูปแบบของรูปภาพ", "Image Quality": "คุณภาพของรูปภาพ", "Standard": "มาตรฐาน", "HD": "HD", "sd_comfy_url": "ตัวอย่าง: {{comfy_url}}", "Open workflow editor": "Open workflow editor", "Create new workflow": "Create new workflow", "Delete workflow": "Delete workflow", "Enhance": "<PERSON><PERSON>ce", "Refine": "Refine", "Decrisper": "<PERSON><PERSON><PERSON>", "Sampling steps": "Sampling steps", "Width": "ความกว้าง", "Height": "ความสูง", "Resolution": "ความละเอียดของรูปภาพ", "Model": "Model", "Sampling method": "Sampling method", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (not all samplers supported)", "SMEA versions of samplers are modified to perform better at high resolution.": "SMEA versions of samplers are modified to perform better at high resolution.", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.", "DYN": "DYN", "Scheduler": "Scheduler", "Restore Faces": "Restore Faces", "Hires. Fix": "Hires. Fix", "Upscaler": "Upscaler", "Upscale by": "Upscale by", "Denoising strength": "Denoising strength", "Hires steps (2nd pass)": "<PERSON><PERSON> steps (2nd pass)", "Preset for prompt prefix and negative prompt": "Preset for prompt prefix and negative prompt", "Style": "รูปแบบ", "Save style": "เซฟรูปแบบ", "Delete style": "ลบรูปแบบ", "Common prompt prefix": "Common prompt prefix", "sd_prompt_prefix_placeholder": "ใช้ {prompt} เพื่อระบุตำแหน่งที่จะแทรกพรอมต์ที่สร้างขึ้น", "Negative common prompt prefix": "Negative common prompt prefix", "Character-specific prompt prefix": "Character-specific prompt prefix", "Won't be used in groups.": "Won't be used in groups.", "sd_character_prompt_placeholder": "ลักษณะเฉพาะที่อธิบายตัวละครที่เลือกอยู่ จะถูกเพิ่มหลังจาก common prompt prefix\nตัวอย่าง: ผู้หญิง, ตาสีเขียว, ผมสีน้ำตาล, เสื้อสีชมพู", "Character-specific negative prompt prefix": "Character-specific negative prompt prefix", "sd_character_negative_prompt_placeholder": "ลักษณะที่ไม่ควรปรากฏในตัวละครที่เลือก จะถูกเพิ่มหลังจาก common negative prompt prefix\nตัวอย่าง: เครื่องประดับ, รองเท้า, แว่นตา", "Shareable": "Shareable", "Image Prompt Templates": "Image Prompt Templates", "Vectors Model Warning": "Vectors Model Warning", "Translate files into English before processing": "แปลไฟล์เป็นภาษาอังกฤษก่อนดำเนินการ", "Manager Users": "จัดการผู้ใช้", "Enter a name for this persona:": "ใส่ชื่อสำหรับ Persona นี้", "New User": "ผู้ใช้ใหม่", "Status:": "สถานะ:", "Created:": "สร้าง:", "Display Name:": "ชื่อที่แสดง:", "User Handle:": "ตัวจัดการผู้ใช้:", "Password:": "รหัสผ่าน:", "Confirm Password:": "ยืนยันรหัสผ่าน:", "This will create a new subfolder...": "การกระทำนี้จะสร้างโฟลเดอร์ย่อยใหม่...", "Current Password:": "รหัสผ่านปัจจุบัน:", "New Password:": "รหัสผ่านใหม่:", "Confirm New Password:": "ยืนยันรหัสผ่านใหม่:", "Debug Warning": "Debug Warning", "Execute": "ดำเนินการ", "Are you sure you want to delete this user?": "คุณแน่ใจแล้วใช่ไหมที่ต้องการลบผู้ใช้นี้?", "Deleting:": "กำลังลบ...", "Also wipe user data.": "ลบข้อมูลผู้ใช้ด้วย", "Warning:": "คำเตือน", "This action is irreversible.": "การกระทำนี้ไม่สามารถย้อนกลับได้.", "Type the user's handle below to confirm:": "Type the user's handle below to confirm:", "Import Characters": "นำเข้าตัวละคร", "Enter the URL of the content to import": "ใส่ URL ของเนื้อหาที่จะนำเข้า", "Supported sources:": "แหล่งที่รองรับ:", "char_import_1": "การ์ดตัวละคร Chub (ลิงก์โดยตรงหรือ ID)", "char_import_example": "ตัวอย่าง:", "char_import_2": "Lorebook ของ Chub (ลิงก์โดยตรงหรือ ID)", "char_import_3": "การ์ดตัวละคร JanitorAI (ลิงก์โดยตรงหรือ UUID)", "char_import_4": "การ์ดตัวละคร Pygmalion.chat (ลิงก์โดยตรงหรือ UUID)", "char_import_5": "การ์ดตัวละคร AICharacterCards.com (ลิงก์โดยตรงหรือ ID)", "char_import_6": "ลิงก์ PNG โดยตรง (ดู", "char_import_7": "โฮสต์ที่อนุญาต)", "char_import_8": "การ์ดตัวละคร R<PERSON>uR<PERSON>m (ลิงก์โดยตรง)", "char_import_10": "การ์ดตัวละคร Perchance (ลิงก์โดยตรงหรือ UUID + .gz)", "Supports importing multiple characters.": "รองรับการนำเข้าตัวละครหลายตัว", "Write each URL or ID into a new line.": "เขียน URL หรือ ID แต่ละอันในบรรทัดใหม่", "Export for character": "ส่งออกการ์ดตัวละคร", "Export prompts for this character, including their order.": "ส่งออกข้อความสำหรับตัวละครนี้ พร้อมเรียงลำดับข้อความ", "Export all": "ส่งออกทั้งหมด", "Export all your prompts to a file": "ส่งออก Prompts ทั้งหมดในรูปแบบไฟล์", "Insert prompt": "แทรก prompt", "Delete prompt": "ลบ prompt", "Import a prompt list": "นำเข้ารายการ prompt", "Export this prompt list": "ส่งออกรายการ prompt", "Reset current character": "รีเซ็ตตัวละครปัจจุบัน", "New prompt": "New prompt", "Prompts": "Prompts", "Total Tokens:": "จำนวนโทเค็นทั้งหมด:", "prompt_manager_tokens": "โทเค็น", "Are you sure you want to reset your settings to factory defaults?": "คุณแน่ใจไหมว่าต้องการคืนค่าการตั้งค่าเป็นค่าเริ่มต้น?", "Don't forget to save a snapshot of your settings before proceeding.": "อย่าลืมบันทึกสแนปช็อตการตั้งค่าของคุณก่อนดำเนินการ", "Settings Snapshots": "สแนปช็อตการตั้งค่า", "Record a snapshot of your current settings.": "บันทึกสแนปช็อตของการตั้งค่าปัจจุบันของคุณ", "Make a Snapshot": "สร้างสแนปช็อต", "Restore this snapshot": "คืนค่าสแนปช็อตนี้", "Hi,": "สวัสดี,", "To enable multi-account features, restart the SillyTavern server with": "เพื่อเปิดระบบ Multiple Account ให้รีสตาร์ทเซิร์ฟเวอร์ SillyTavern อีกครั้ง", "Account Info": "รายละเอียดของบัญชี", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "เพื่อเปลี่ยนรูปโปรไฟล์ของคุณ ให้ใช้ปุ่มด้านล่างหรือเลือก Persona ในเมนูการจัดการตัวละคร", "Set your custom avatar.": "ตั้งค่ารูปโปรไฟล์ของคุณ", "Remove your custom avatar.": "ลบรูปโปรไฟล์ของคุณ", "Handle:": "Handle:", "This account is password protected.": "บัญชีนี้มีการป้องกันด้วยรหัสผ่าน", "This account is not password protected.": "บัญชีนี้ไม่มีการป้องกันด้วยรหัสผ่าน", "Account Actions": "การดำเนินการบัญชี", "Change Password": "เปลี่ยนรหัสผ่าน", "Manage your settings snapshots.": "จัดการการตั้งค่า Snapshot ของคุณ", "Download a complete backup of your user data.": "ดาวน์โหลดการสำรองข้อมูลทั้งหมดของข้อมูลผู้ใช้", "Download Backup": "ดาวน์โหลดการสำรองข้อมูล", "Danger Zone": "Danger Zone", "Reset your settings to factory defaults.": "รีเซ็ตการตั้งค่าเป็นค่าเริ่มต้น", "Reset Settings": "รีเซ็ตการตั้งค่า", "Wipe all user data and reset your account to factory settings.": "ลบข้อมูลผู้ใช้ทั้งหมดและรีเซ็ตบัญชีของคุณเป็นการตั้งค่าเริ่มต้น", "Reset Everything": "คืนค่าทุกอย่างเป็นค่าเริ่มต้น", "Reset Code:": "Reset Code:", "Want to update?": "ต้องการอัพเดตไหม?", "How to start chatting?": "จะเริ่มการสนทนาอย่างไร?", "Click _space": "Click space", "and select a": "และเลือก", "Chat API": "Chat API", "and pick a character.": "และเลือกตัวละคร", "You can browse a list of bundled characters in the": "คุณสามารถเรียกดูรายการตัวละครที่มีมาให้ใน", "Download Extensions & Assets": "ดาวน์โหลดส่วนขยายและทรัพยากร", "Load a custom asset list or select": "โหลดรายการทรัพยากรที่กำหนดเองหรือเลือกจาก", "to install 3rd party extensions.": "เพื่อติดตั้งส่วนขยายจาก 3rd party", "menu within": "เมนูใน", "Confused or lost?": "สับสนหรือหลงทาง?", "click these icons!": "คลิกไอคอนเหล่านี้!", "in the chat bar": "ในแถบแชท", "SillyTavern Documentation Site": "เว็บไซต์เอกสาร Si<PERSON><PERSON><PERSON>n", "Extras Installation Guide": "คู่มือการติดตั้ง Extras", "Still have questions?": "ยังคงมีคำถามอยู่ใช่ไหม?", "Join the SillyTavern Discord": "เข้าร่วมดิสคอร์ด Si<PERSON><PERSON><PERSON>n ", "Post a GitHub issue": "Post a GitHub issue", "Contact the developers": "ติดต่อผู้พัฒนา", "Stop Inspecting": "Stop Inspecting", "Inspect Prompts": "ตรวจสอบ Prompt", "Toggle prompt inspection": "สลับการตรวจสอบ Prompt"}