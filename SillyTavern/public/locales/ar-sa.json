{"Favorite": "مفضل", "Tag": "بطاقة شعار", "Duplicate": "ين<PERSON><PERSON>", "Persona": "شخصية", "Delete": "<PERSON><PERSON><PERSON>", "AI Response Configuration": "تكوين الرد الذكاء الاصطناعي", "AI Configuration panel will stay open": "لوحة تكوين الذكاء الاصطناعي ستبقى مفتوحة", "clickslidertips": "انقر  لإدخال القيم يدويًا.", "MAD LAB MODE ON": "وضع MAD LAB قيد التشغيل", "Documentation on sampling parameters": "وثائق حول معلمات العينات", "kobldpresets": "الإعدادات المسبقة لـ Kobold", "guikoboldaisettings": "إعدادات واجهة KoboldAI", "Update current preset": "تحديث الإعداد الحالي", "Save preset as": "ح<PERSON><PERSON> الضبط المسبق باسم", "Import preset": "استيراد الإعداد", "Export preset": "تصدير الإعداد", "Restore current preset": "استعادة الضبط الحالي", "Delete the preset": "<PERSON><PERSON><PERSON> الإعداد", "novelaipresets": "الإعدادات المسبقة لـ NovelAI", "Default": "افتراضي", "openaipresets": "الإعدادات المسبقة لـ OpenAI", "Text Completion presets": "الإعدادات  لإكمال النص", "AI Module": "وحدة الذكاء الصناعي", "Changes the style of the generated text.": "تغيير نمط النص المولد.", "No Module": "لا توجد وحدة", "Instruct": "إرشاد", "Prose Augmenter": "مُعزز النثر", "Text Adventure": "مغامرة النص", "response legth(tokens)": "طول الاستجابة (بعدد الاحرف او الرموز)", "Streaming": "البث المباشر ل", "Streaming_desc": "عرض الاستجابة لحظيا كما يتم إنشاؤها.", "context size(tokens)": "حجم الاحرف (بعد<PERSON> الاحرف او الرموز)", "unlocked": "مفتوح", "Only enable this if your model supports context sizes greater than 8192 tokens": "قم بتمكين هذا فقط إذا كانت نموذجك يدعم مقاطع السياق بأحجام أكبر من 8192 رمزًا.", "Max prompt cost:": "أقصى تكلفة فورية:", "Display the response bit by bit as it is generated.": "عرض الاستجابة بتدريج كما يتم إنشاؤها.", "When this is off, responses will be displayed all at once when they are complete.": "عند إيقا<PERSON> هذا الخيار، سيتم عرض الردود جميعها دفعة واحدة عند اكتمالها.", "Temperature": "درجة الحرارة", "rep.pen": "عقوبة الاعادة", "Rep. Pen. Range.": "مدى عقوبة التكرار", "Rep. Pen. Slope": "ميل العقوبة التكرار", "Rep. Pen. Freq.": "تكرار عقوبة التكرار", "Rep. Pen. Presence": "وجود عقوبة التكرار", "TFS": "TFS", "Phrase Repetition Penalty": "عقوبة تكرار العبارات", "Off": "إيقا<PERSON>", "Very light": "خفيف جداً", "Light": "خ<PERSON><PERSON><PERSON>", "Medium": "متوسط", "Aggressive": "عدواني", "Very aggressive": "عدواني للغاية", "Unlocked Context Size": "حجم السياق غير المقفل", "Unrestricted maximum value for the context slider": "قيمة قصوى غير مقيدة  السياق", "Context Size (tokens)": "حجم السياق (الرموزاو الحروف)", "Max Response Length (tokens)": "الح<PERSON> الأقصى لطول الاستجابة (الرموز,الحرف)", "Multiple swipes per generation": "الضربات الشديدة المتعددة لكل جيل", "Enable OpenAI completion streaming": "تمكين بث الاكتمال من OpenAI", "Frequency Penalty": "عقوبة التكرار", "Presence Penalty": "عقوبة الوجود", "Count Penalty": "عد <PERSON>ر<PERSON>ة جزاء", "Top K": "أعلى K", "Top P": "أعلى P", "Repetition Penalty": "عقوبة التكرار", "Min P": "مين ص", "Top A": "أعلى A", "Quick Prompts Edit": "تحرير التلميحات بسرعة", "Main": "الرئيسية", "NSFW": "NSFW", "Jailbreak": "كسر الحجز", "Utility Prompts": "تلميحات الأدوات", "Impersonation prompt": "تعليمات التنكر", "Restore default prompt": "استعادة التعليمة الافتراضية", "Prompt that is used for Impersonation function": "التعليمات التي يتم استخدامها لوظيفة التنكر", "World Info Format Template": "قالب تنسيق معلومات العالم", "Restore default format": "استعادة التنسيق الافتراضي", "Wraps activated World Info entries before inserting into the prompt.": "يلتف إدخالات معلومات العالم المنشَّطة قبل إدراجها في الموجه.", "scenario_format_template_part_1": "يستخدم", "scenario_format_template_part_2": "لتحديد المكان الذي يتم فيه إدراج المحتوى.", "Scenario Format Template": "قالب تنسيق السيناريو", "Personality Format Template": "قالب تنسيق الشخصية", "Group Nudge Prompt Template": "قالب المطالبة بدفعة المجموعة", "Sent at the end of the group chat history to force reply from a specific character.": "يتم إرساله في نهاية سجل الدردشة الجماعية لفرض الرد من شخصية معينة.", "New Chat": "دردشة جديدة", "Restore new chat prompt": "استعادة موجه الدردشة الجديد", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "قم بتعيينه في بداية سجل الدردشة للإشارة إلى أن محادثة جديدة على وشك البدء.", "New Group Chat": "دردشة جماعية جديدة", "Restore new group chat prompt": "استعادة المطالبة الافتراضية", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "قم بالتعيين في بداية سجل الدردشة للإشارة إلى أن محادثة جماعية جديدة على وشك البدء.", "New Example Chat": "مثال جديد للدردشة", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "قم بتعيين أمثلة في بداية الحوار للإشارة إلى أن مثالًا جديدًا للدردشة على وشك البدء.", "Continue nudge": "استمر في الدفع", "Set at the end of the chat history when the continue button is pressed.": "يتم تعيينه في نهاية سجل الدردشة عند الضغط على زر المتابعة.", "Replace empty message": "استبدال الرسالة الفارغة", "Send this text instead of nothing when the text box is empty.": "إرسال هذا النص بدلاً من عدم وجود شيء عندما يكون مربع النص فارغًا.", "Seed": "بذرة", "Set to get deterministic results. Use -1 for random seed.": "اضبط للحصول على نتائج حتمية. استخدم -1 للبذور العشوائية.", "Temperature controls the randomness in token selection": "درجة الحرارة تتحكم في العشوائية في اختيار الحروف:\n- درجة حرارة منخفضة (<1.0) تؤدي إلى نص أكثر ذكاءا، مع إعطاء الأولوية(للعبارات والكلمات) للرموز ذات الاحتمالية العالية.\n- درجة حرارة مرتفعة (>1.0) تزيد من الإبداع وتنوع الإخراج، مع منح الرموز(العبارات والكلمات) ذات الاحتمالية المنخفضة فرصًا أكبر.\nقم بتعيين القيمة 1.0 للاحتماليات الأصلية.", "Top_K_desc": "القيمة العليا K تحدد الحد الأقصى لعدد الرموز العلوية التي يمكن اختيارها.", "Top_P_desc": "القيمة العلوية P (المعروفة أيضًا باسم عينة النواة) تجمع بين جميع الرموز العلوية اللازمة لتحقيق نسبة مئوية معينة.\nبمعنى آخر، إذا كانت الرموز العلوية 2 تمثل 25٪، وكانت Top-P تساوي 0.50، يُعتبر فقط هذان الرمزان العلويان.\nقم بتعيين القيمة 1.0 للتعطيل.", "Typical P": "قيمة P النموذجية", "Typical_P_desc": "عينة القيمة النموذجية P تُعطي أولوية للرموز استنادًا إلى انحرافها عن الانحدار المتوسط للمجموعة.\nيتم الاحتفاظ بالرموز التي تكون احتماليتها التراكمية قريبة من العتبة المحددة (على سبيل المثال، 0.5)، مما يميز تلك التي تحتوي على متوسط معلوماتي.\nقم بتعيين القيمة 1.0 للتعطيل.", "Min_P_desc": "القيمة الدنيا P تحدد الحد الأدنى الأساسي للإحتمال. يتم تحسينها استنادًا إلى إحتمالية الرمز العلوي.\nإذا كانت إحتمالية الرمز العلوي 80٪، وكانت القيمة الدنيا P - 0.1، فسيتم النظر في الرموز فقط بإحتمالية أعلى من 8٪.\nقم بتعيين القيمة 0 للتعطيل.", "Top_A_desc": "القيمة العلوية A تحدد عتبة لاختيار الرموز استنادًا إلى مربع إحتمالية الرمز الأعلى.\nإذا كانت القيمة العلوية A تساوي 0.2، وكانت إحتمالية الرمز العلوي تساوي 50٪، فسيتم استبعاد الرموز بإحتمالية أقل من 5٪ (0.2 * 0.5^2).\nقم بتعيين القيمة 0 للتعطيل.", "Tail_Free_Sampling_desc": "عينة خالية من الذيل (TFS) تبحث عن ذيل الرموز ذات الاحتمالية الصغيرة في التوزيع،\n من خلال تحليل معدل تغير إحتماليات الرموز باستخدام الإشتقاقات. يتم الاحتفاظ بالرموز حتى الحد (على سبيل المثال، 0.3)، استنادًا إلى المشتق الثاني الموحد.\nكلما اقترب من 0، زاد عدد الرموز المرفوضة. قم بتعيين القيمة 1.0 للتعطيل.", "rep.pen range": "نطاق عقوبة الاعادة.", "Mirostat": "ميروستات", "Mode": "وضع", "Mirostat_Mode_desc": "قيمة 0 تعطل Mirostat بالكامل. 1 مخصص لميروستات 1.0، و2 مخصص لميروستات 2.0", "Tau": "تاو", "Mirostat_Tau_desc": "يتحكم في تقلب مخرجات Mirostat", "Eta": "إيتا", "Mirostat_Eta_desc": "يتحكم في معدل التعلم من Mirostat", "Ban EOS Token": "حظر رمز EOS", "Ban_EOS_Token_desc": "حظر رمز نهاية التسلسل (EOS) باستخدام KoboldCpp (وربما أيضًا الرموز المميزة الأخرى مع KoboldAI).\rجيد لكتابة القصة، ولكن لا ينبغي استخدامه في وضع الدردشة والإرشاد.", "GBNF Grammar": "قواعد اللغة الباكوسية", "Type in the desired custom grammar": "اكتب القواعد اللغوية المخصصة المطلوبة", "Samplers Order": "ترتيب الأمثلة", "Samplers will be applied in a top-down order. Use with caution.": "سيتم تطبيق الأمثلة بترتيب من الأعلى إلى الأسفل. استخدم بحذر.", "Tail Free Sampling": "عينة خالية من الذيل", "Load koboldcpp order": "تحميل أ<PERSON><PERSON> kob<PERSON>", "Preamble": "مقدمة", "Use style tags to modify the writing style of the output.": "استخدم علامات النمط لتعديل نمط الكتابة النهائية.", "Banned Tokens": "الرموز المحظورة", "Sequences you don't want to appear in the output. One per line.": "تسلسلات لا تريد ظهورها في النتيجة. واحدة لكل سطر.", "Logit Bias": "الانحياز في اللوجيت", "Add": "إضافة", "Helps to ban or reenforce the usage of certain words": "يساعد في حظر أو تعزيز استخدام بعض الكلمات", "CFG Scale": "مقياس CFG", "Negative Prompt": "استفسار سلبي", "Add text here that would make the AI generate things you don't want in your outputs.": "أضف النص هنا الذي سيجعل الذكاء الصناعي يولد أشياء لا ترغب فيها في اخراجها.", "Used if CFG Scale is unset globally, per chat or character": "يتم استخدامه إذا لم يتم تعيين مقياس CFG على نطاق عالمي، لكل محادثة أو شخصية.", "Mirostat Tau": "تاو Mirostat", "Mirostat LR": "ميروستات LR", "Min Length": "الح<PERSON> الأدنى للطول", "Top K Sampling": "عينة أعلى K", "Nucleus Sampling": "عينة النواة", "Top A Sampling": "عينة أعلى A", "CFG": "CFG", "Neutralize Samplers": "تعطيل المحاكيات", "Set all samplers to their neutral/disabled state.": "ضبط جميع المحاكيات على حالتها الطبيعية/معطلة.", "Sampler Select": "تحديد العينات", "Customize displayed samplers or add custom samplers.": "تخصيص عينات المعروضة أو إضافة عينات مخصصة.", "Epsilon Cutoff": "قطع إبسيلون", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "القيمة العلوية الإبسيلون تعيين الحد الأدنى للإحتمالية حيث تستبعد الرموز أدناه من العينة.\nبالوحدات 1e-4؛ القيمة المناسبة هي 3.\nقم بتعيين 0 للتعطيل.", "Eta Cutoff": "قطع إيتا", "Eta_Cutoff_desc": "قيمة القطع Eta هي المعلمة الرئيسية لتقنية عينة إيتا الخاصة. &#13; بوحدات 1e-4 ؛ القيمة المعقولة هي 3. &#13; قم بتعيينها على 0 لتعطيلها. &#13; انظر ورقة بحث عينة الانقطاع كما يمكن تسميتها باسم تلميع نموذج اللغة من قبل هيويت وآخرين (2022) للحصول على تفاصيل.", "rep.pen decay": "ممثل تسوس القلم", "Encoder Rep. Pen.": "عقوبة تكرار المشفر", "No Repeat Ngram Size": "<PERSON><PERSON><PERSON> <PERSON><PERSON> بدون تكرار", "Skew": "انحراف", "Max Tokens Second": "أق<PERSON>ى عدد من الرموز(الحروف) / الثانية", "Smooth Sampling": "أخذ العينات على نحو سلس", "Smooth_Sampling_desc": "يسمح لك باستخدام التحويلات التربيعية/المكعبية لضبط التوزيع. ستكون قيم عامل التجانس الأقل أكثر إبداعًا، وعادة ما تكون بين 0.2-0.3 هي النقطة المثالية (بافتراض أن المنحنى = 1). ستؤدي قيم منحنى التجانس الأعلى إلى جعل المنحنى أكثر انحدارًا، مما سيؤدي إلى معاقبة الاختيارات ذات الاحتمالية المنخفضة بشكل أكثر قوة. منحنى 1.0 يعادل استخدام عامل التجانس فقط.", "Smoothing Factor": "عامل التنعيم", "Smoothing Curve": "منحنى التنعيم", "DRY_Repetition_Penalty_desc": "يعاقب DRY الرموز المميزة التي من شأنها تمديد نهاية الإدخال إلى تسلسل حدث مسبقًا في الإدخال. اضبط المضاعف على 0 لتعطيله.", "DRY Repetition Penalty": "عقوبة التكرار الجاف", "DRY_Multiplier_desc": "اضبط على القيمة > 0 لتمكين DRY. يتحكم في حجم العقوبة لأقصر تسلسل معاقب عليه.", "Multiplier": "المضاعف", "DRY_Base_desc": "يتحكم في مدى سرعة نمو العقوبة مع زيادة طول التسلسل.", "Base": "قاعدة", "DRY_Allowed_Length_desc": "أطول تسلسل يمكن تكراره دون عقوبة.", "Allowed Length": "الطول المسموح به", "Penalty Range": "نطاق العقوبة", "DRY_Sequence_Breakers_desc": "الرموز المميزة التي لا تستمر مطابقة التسلسل عبرها. تم تحديده كقائمة مفصولة بفواصل من السلاسل المقتبسة.", "Sequence Breakers": "قواطع التسلسل", "JSON-serialized array of strings.": "مجموعة سلاسل JSON متسلسلة.", "Dynamic Temperature": "درجة الحرارة الديناميكية", "Scale Temperature dynamically per token, based on the variation of probabilities": "قيمة درجة الحرارة يتم تحديدها ديناميكيًا لكل رمز، استنادًا إلى التغيير في الإحتمالات.", "Minimum Temp": "أقل درجة حرارة", "Maximum Temp": "أعلى درجة حرارة", "Exponent": "الأس", "Mirostat (mode=1 is only for llama.cpp)": "(فقط عند استخدام llama.cpp)ميروستات", "Mirostat_desc": "ميروستات هو جهاز ترموستات لصعوبة الإخراج. يعد ميروستات آلية لضبط صعوبة الإخراج لتحقيق الانسجام بين الإدخال والإخراج.", "Mirostat Mode": "وضعية Mirostat", "Variability parameter for Mirostat outputs": "معلمة التباين لإخراج Mirostat.", "Mirostat Eta": "إيتا Mirostat", "Learning rate of Mirostat": "معدل التعلم لـ Mirostat.", "Beam search": "بحث الشعاع", "Helpful tip coming soon.": "نصيحة مفيدة قريبا.", "Number of Beams": "ع<PERSON><PERSON> الشعاع", "Length Penalty": "عقوبة الطول", "Early Stopping": "التوق<PERSON> المب<PERSON>ر", "Contrastive search": "البحث المتقابل", "Penalty Alpha": "ألفا العقوبة", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "قوة شرط التنظيم للبحث التناقضي( بعد تعيين العينات المعززة بقوة إلى المجموعات من خلال تسمياتها الزائفة، يقوم التنظيم المتباين الخاص بنا بتحديث النموذج بحيث تقوم الميزات ذات التسميات الزائفة الواثقة بتجميع الميزات في نفس المجموعة، مع دفع الميزات في مجموعات مختلفة بعيدًا). قم بتعيين القيمة إلى 0 لتعطيل CS.", "Do Sample": "عينة", "Add BOS Token": "إضافة رمز BOS", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "إضافة bos_token  إلى بداية الجمل. يمكن أن يجعل تعطيل هذا الردود أكثر إبداعًا.", "Ban the eos_token. This forces the model to never end the generation prematurely": "حظر رمز EOS. هذا يجبر النموذج على عدم إنهاء الاستجابة مبكرًا أبدًا", "Ignore EOS Token": "تجاهل رمز EOS", "Ignore the EOS Token even if it generates.": "تجاهل رمز EOS حتى لو تم إنشاؤه.", "Skip Special Tokens": "تخطي الرموز الخاصة", "Temperature Last": "درجة الحرارة الأخيرة", "Temperature_Last_desc": "استخدم مُخرج درجة الحرارة في النهاية. هذا عادة ما يكون منطقياً.\nعند التشغيل: يتم أولاً اختيار مجموعة من الرموز المحتملة، ثم يتم تطبيق درجة الحرارة لتصحيح احتمالياتها النسبية (تقنيًا، اللوجيتات).\nعند التعطيل: يتم تطبيق درجة الحرارة أولاً لتصحيح الاحتماليات النسبية لكل الرموز، ثم يتم اختيار مجموعة من الرموز المحتملة من بينها.\nتعطيل درجة الحرارة في النهاية يزيد من احتماليات الرموز في ذيل التوزيع، مما يزيد من فرص الحصول على إجابات غير متناسقة.", "Speculative Ngram": "نغرام المضاربة", "Use a different speculative decoding method without a draft model": "استخدم طريقة مختلفة لفك التشفير التخميني بدون نموذج أولي.\rيفضل استخدام نموذج مسودة. ngram المضاربة ليست فعالة.", "Spaces Between Special Tokens": "المسافات بين الرموز الخاصة", "LLaMA / Mistral / Yi models only": "فقط لنماذج LLaMA / Mistral / Yi. تأكد من تحديد المحلل المناسب أولاً.\nسلاسل تود أن لا تظهر في النتائج.\nسلسلة واحدة في كل سطر. نص أو [معرفات الحروف].\nالعديد من الرموز يبدأ بفراغ. استخدم عداد الرموز إذا كنت غير متأكد.", "Example: some text [42, 69, 1337]": "مثال:\nبعض النص\n[42، 69، 1337]", "Classifier Free Guidance. More helpful tip coming soon": "إرشادات خالية  . نصائح أكثر فائدة قريباً.", "Scale": "مقياس", "JSON Schema": "م<PERSON><PERSON><PERSON> جيسون", "Type in the desired JSON schema": "اكتب مخطط JSON المطلوب", "Grammar String": "سلسلة القواعد", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "يعتمد GBNF أو EBNF على الواجهة الخلفية المستخدمة. إذا كنت تستخدم هذا يجب أن تعرف أي.", "Top P & Min P": "أعلى ع وأدنى ص", "Load default order": "تحميل الترتيب الافتراضي", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "llama.cpp فقط. تحديد ترتيب أخذ العينات. إذا لم يكن وضع Mirostat 0، فسيتم تجاهل ترتيب أخذ العينات.", "Sampler Priority": "أولوية العينات", "Ooba only. Determines the order of samplers.": "Ooba فقط في حالة استخدام ل. يحدد ترتيب العينات.", "Character Names Behavior": "سلوك أسماء الشخصيات", "Helps the model to associate messages with characters.": "يساعد النموذج على ربط الرسائل بالأحرف.", "None": "لا شيء", "character_names_default": "باستثناء المجموعات والشخصيات السابقة. بخلاف ذلك، تأكد من تقديم الأسماء في المطالبة.", "Don't add character names.": "لا تضيف أسماء الشخصيات.", "Completion": "كائن الإكمال", "character_names_completion": "تنطبق القيود: فقط الحروف الأبجدية اللاتينية والأرقام والشرطات السفلية. لا يعمل مع جميع المصادر، ولا سيما: Claude وMistralAI وGoogle.", "Add character names to completion objects.": "أض<PERSON> أسماء الشخصيات إلى كائنات الإكمال.", "Message Content": "محتوى الرسالة", "Prepend character names to message contents.": "قم بإرفاق أسماء الأحرف بمحتويات الرسالة.", "Continue Postfix": "متابعة بوستفيكس", "The next chunk of the continued message will be appended using this as a separator.": "سيتم إلحاق الجزء التالي من الرسالة المستمرة باستخدام هذا كفاصل.", "Space": "فضاء", "Newline": "<PERSON><PERSON> جديد", "Double Newline": "الخط الجديد مزدوج", "Wrap user messages in quotes before sending": "لف رسائل المستخدمين في علامات اقتباس قبل الإرسال", "Wrap in Quotes": "وضع النص بين علامات اقتباس", "Wrap entire user message in quotes before sending.": "ضع الرسالة بأكملها بين علامات اقتباس قبل الإرسال.", "Leave off if you use quotes manually for speech.": "اتركها على وضع الايقاف إذا كنت تستخدم الاقتباسات يدويًا للكلام.", "Continue prefill": "متابعة التعبئة المسبقة", "Continue sends the last message as assistant role instead of system message with instruction.": "المتابعة ترسل الرسالة الأخيرة بدور المساعد بدلاً من رسالة النظام مع التعليمات.", "Squash system messages": "ضغط رسائل النظام", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "يجمع الرسائل المتتالية للنظام في رسالة واحدة (باستثناء الحوارات المثالية). قد يحسن التتابع لبعض النماذج.", "Enable function calling": "تمكين استدعاء الوظيفة", "Send inline images": "إرسال الصور المضمنة", "image_inlining_hint_1": "يرسل الصور في المطالبات إذا كان النموذج يدعمها .\n                                                استخدم ال", "image_inlining_hint_2": "الإجراء على أي رسالة أو", "image_inlining_hint_3": "القائمة لإرفاق ملف صورة للدردشة.", "Inline Image Quality": "جودة الصورة المضمنة", "openai_inline_image_quality_auto": "آلي", "openai_inline_image_quality_low": "قليل", "openai_inline_image_quality_high": "عالي", "Use AI21 Tokenizer": "استخدم رمز AI21", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "استخدم الرمز المميز المناسب للنماذج الجوراسية، وهو أكثر كفاءة من GPT.", "Use Google Tokenizer": "استخدم محلل النحوي من Google", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "استخدم المحلل النحوي المناسب لنماذج Google عبر واجهة برمجة التطبيقات الخاصة بهم. معالجة الإشارات الأولية بطيئة، ولكنها تقدم عداد رمز دقيق جدًا.", "Use system prompt": "استخدام موجه النظام", "(Gemini 1.5 Pro/Flash only)": "(جيميني 1.5 برو/فلاش فقط)", "Merges_all_system_messages_desc_1": "يدمج كافة رسائل النظام حتى الرسالة الأولى ذات دور غير النظام، ويرسلها في ملف", "Merges_all_system_messages_desc_2": "مجال.", "Assistant Prefill": "تعبئة مسبقة للمساعد", "Start Claude's answer with...": "ابدأ إجابة كلود بـ...", "Assistant Impersonation Prefill": "مساعد انتحال الشخصية المسبقة", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "إرسال التعليمة النظامية للنماذج المدعومة. إذا تم تعطيلها، يتم إضافة رسالة المستخدم إلى بداية التعليمة.", "User first message": "الرسالة الأولى للمستخدم", "Restore User first message": "استعادة الرسالة الأولى للمستخدم", "Human message": "رسالة إنسانية وتعليمات وما إلى ذلك.\nلا يضيف شيئًا عندما يكون فارغًا، أي يتطلب موجهًا جديدًا بالدور \"مستخدم\".", "New preset": "<PERSON>عد<PERSON> جديد", "Delete preset": "<PERSON><PERSON><PERSON> الإعداد", "View / Edit bias preset": "عرض/تحرير الضبط المسبق للانحياز", "Add bias entry": "إضافة إدخال للانحياز", "Most tokens have a leading space.": "تحتوي معظم الرموز المميزة على مسافة بادئة.", "API Connections": "اتصالات واجهة برمجة التطبيقات", "Text Completion": "اكتمال النص", "Chat Completion": "إكمال الدردشة", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "تجنب إرسال معلومات حساسة إلى الجماعة.", "Review the Privacy statement": "مراجعة بيان الخصوصية", "Register a Horde account for faster queue times": "سجّل حساب Horde لزمن انتظار أسرع في الطابور", "Learn how to contribute your idle GPU cycles to the Horde": "تعلم كيفية المساهمة بدورات معالجة الرسومات الخاملة الخاصة بك في الهورد", "Adjust context size to worker capabilities": "ضبط حجم السياق وفقًا لقدرات العاملين", "Adjust response length to worker capabilities": "ضبط طول الاستجابة وفقًا لقدرات العاملين", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "يمكن المساعدة في الردود السيئة عن طريق وضع العمال الموافق عليهم فقط في قائمة الانتظار. قد يؤدي إلى بطء وقت الاستجابة.", "Trusted workers only": "العاملون الموثوق بهم فقط", "API key": "مفتاح API", "Get it here:": "احصل عليه هنا:", "Register": "سجّل", "View my Kudos": "عرض  (Kudos)", "Enter": "<PERSON><PERSON><PERSON>ل", "to use anonymous mode.": "لاستخدام الوضع المتخفي.", "Clear your API key": "مسح مفتاح واجهة برمجة التطبيقات الخاص بك", "For privacy reasons, your API key will be hidden after you reload the page.": "لأسباب خصوصية، سيتم إخفاء مفتاح API الخاص بك بعد إعادة تحميل الصفحة.", "Models": "النماذج", "Refresh models": "تحديث النماذج", "-- Horde models not loaded --": "-- نماذج الجماعة غير محملة --", "Not connected...": "غير متصل...", "API url": "رابط API", "Example: http://127.0.0.1:5000/api ": "مثال: http://127.0.0.1:5000/api", "Connect": "الاتصال", "Cancel": "إلغاء", "Novel API key": "مفتاح API لـ NovelAI", "Get your NovelAI API Key": "احصل على مفتاح API NovelAI الخاص بك", "Enter it in the box below": "أدخله في المربع أدناه", "Novel AI Model": "نموذج NovelAI", "No connection...": "لا يوجد اتصال...", "API Type": "نوع واجهة برمجة التطبيقات", "Default (completions compatible)": "الافتراضي [متوافق مع OpenAI/الإكمالات: oobabooga، LM Studio، إلخ.]", "TogetherAI API Key": "مفتاح API لـ TogetherAI", "TogetherAI Model": "نموذج TogetherAI", "-- Connect to the API --": "-- الاتصال بواجهة برمجة التطبيقات --", "OpenRouter API Key": "مفتاح API لـ OpenRouter", "Click Authorize below or get the key from": "انقر فوق تفويض أدناه أو احصل على المفتاح من", "View Remaining Credits": "عرض الر<PERSON>ي<PERSON> المتبقي", "OpenRouter Model": "نموذج OpenRouter", "Model Providers": "مقدمو النماذج", "InfermaticAI API Key": "مفتاح واجهة برمجة تطبيقات InfermaticAI", "InfermaticAI Model": "نموذج الذكاء الاصطناعي Infermatic", "DreamGen API key": "مفتاح DreamGen API", "DreamGen Model": "نموذج دريم جين", "Mancer API key": "مفتاح API لـ Mancer", "Mancer Model": "نموذج مانسر", "Make sure you run it with": "تأكد من تشغيله مع", "flag": "وضع علامة", "API key (optional)": "م<PERSON><PERSON><PERSON><PERSON> (اختياري)", "Server url": "راب<PERSON> الخادم", "Electron Hub API Key": "مفتاح API لـ Electron Hub", "Electron Hub Model": "نموذج Electron Hub", "Example: http://127.0.0.1:5000": "مثال: http://127.0.0.1:5000", "Custom model (optional)": "نموذج مخصص (اختياري)", "vllm-project/vllm": "vllm-project/vllm (وضع غلاف OpenAI API)", "vLLM API key": "مفتاح واجهة برمجة التطبيقات vLLM", "Example: http://127.0.0.1:8000": "مثال: http://127.0.0.1:8000", "vLLM Model": "نموذج vLLM", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (وضع التغليف لواجهة برمجة التطبيقات OpenAI)", "Aphrodite API key": "مفتاح واجهة برمجة التطبيقات Aphrodite", "Aphrodite Model": "نموذج أفروديت", "ggerganov/llama.cpp": "ggerganov/llama.cpp (خاد<PERSON> إخراج)", "Example: http://127.0.0.1:8080": "مثال: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "مثال: http://127.0.0.1:11434", "Ollama Model": "نموذج Ollama", "Download": "تحميل", "Tabby API key": "مفتاح API لـ Tabby", "koboldcpp API key (optional)": "مفت<PERSON><PERSON> koboldcpp API (اختياري)", "Example: http://127.0.0.1:5001": "مثال: http://127.0.0.1:5001", "Authorize": "تفويض", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "احصل على رمز واجهة برمجة التطبيقات الخاص بك لموزع الاتصالات باستخدام تدفق OAuth. سيتم توجيهك إلى openrouter.ai", "Bypass status check": "تجاوز فحص الحالة", "Chat Completion Source": "مصدر استكمال الدردشة", "Reverse Proxy": "الوكيل العكسي", "Proxy Presets": "إعدادات الوكيل المسبقة", "Saved addresses and passwords.": "العناوين وكلمات المرور المحفوظة.", "Save Proxy": "<PERSON><PERSON><PERSON> الوكيل", "Delete Proxy": "<PERSON><PERSON><PERSON> الو<PERSON>يل", "Proxy Name": "اسم الوكيل", "This will show up as your saved preset.": "سيظهر هذا كإعداد مسبق محفوظ لديك.", "Proxy Server URL": "عنوان URL للخادم الوكيل", "Alternative server URL (leave empty to use the default value).": "عنوان URL الخادم البديل (اتركه فارغًا لاستخدام القيمة الافتراضية).", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "قم بإزالة مفتاح API الخاص بـ OAI الحقيقي من لوحة الواجهة البرمجية قبل كتابة أي شيء في هذا المربع", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "لا يمكننا تقديم الدعم للمشاكل التي تواجهك أثناء استخدام بروكسي OpenAI غير الرسمي", "Doesn't work? Try adding": "لا يعمل؟ حاول إضافة", "at the end!": "في نهايةالمطاف!", "Proxy Password": "كلمة مرور الوكيل", "Will be used as a password for the proxy instead of API key.": "سيتم استخدامها ككلمة مرور للوكيل بدلاً من مفتاح API.", "Peek a password": "نظرة خاطفة على كلمة المرور", "OpenAI API key": "مفتاح API لـ OpenAI", "View API Usage Metrics": "عرض مقاييس استخدام واجهة برمجة التطبيقات", "Follow": "اتبع", "these directions": "هذه التوجيهات", "to get your OpenAI API key.": "للحصول على مفتاح API لـ OpenAI.", "Use Proxy password field instead. This input will be ignored.": "استخدم حقل \"كلمة مرور الوكيل\" بدلاً من ذلك. سيتم تجاهل هذا الإدخال.", "OpenAI Model": "نموذج OpenAI", "Bypass API status check": "تجاوز فحص حالة واجهة برمجة التطبيقات", "Show External models (provided by API)": "عرض النماذج الخارجية (المقدمة من قبل واجهة برمجة التطبيقات)", "Get your key from": "احصل على مفتاحك من", "Anthropic's developer console": "وحدة تحكم المطور في Anthropic", "Claude Model": "نموذج Claude", "Window AI Model": "نموذج Window AI", "Model Order": "فرز نموذج OpenRouter", "Alphabetically": "أبجديا", "Price": "السعر (الأرخص)", "Context Size": "حجم السياق", "Group by vendors": "المجموعة حسب البائعين", "Group by vendors Description": "ضع نماذج OpenAI في مجموعة واحدة، والنماذج الإنسانية في مجموعة أخرى، وما إلى ذلك. ويمكن دمجها مع الفرز.", "Allow fallback routes": "السماح بمسارات الاحتياط", "Allow fallback routes Description": "يختار النموذج البديل تلقائيًا إذا كان النموذج المحدد غير قادر على تلبية طلبك.", "AI21 API Key": "مفتاح API لـ AI21", "AI21 Model": "نموذج AI21", "Google AI Studio API Key": "مفتاح واجهة برمجة تطبيقات Google AI Studio", "Google Model": "نموذج جوجل", "MistralAI API Key": "مفتاح واجهة برمجة التطبيقات MistralAI", "MistralAI Model": "نموذج ميسترال آي آي", "Groq API Key": "مفت<PERSON><PERSON> Groq <PERSON>", "Groq Model": "نموذج جروك", "Perplexity API Key": "مفتاح واجهة برمجة تطبيقات الحيرة", "Perplexity Model": "نموذج الحيرة", "Cohere API Key": "مفتاح واجهة برمجة التطبيقات Cohere", "Cohere Model": "نموذج التماسك", "Custom Endpoint (Base URL)": "نقطة النهاية المخصصة (عنوان URL الأساسي)", "Custom API Key": "مفتاح واجهة برمجة التطبيقات المخصص", "Available Models": "النماذج المتاحة", "Prompt Post-Processing": "موجه بعد المعالجة", "Applies additional processing to the prompt before sending it to the API.": "يطبق معالجة إضافية على الموجه قبل إرساله إلى واجهة برمجة التطبيقات.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "تتحقق من اتصالك بواجهة برمجة التطبيقات من خلال إرسال رسالة اختبار قصيرة. كن على علم بأنك ستحصل على الفضل في ذلك!", "Test Message": "رسالة اختبار", "Auto-connect to Last Server": "الاتصال التلقائي بآخر خادم", "Missing key": "❌المفتاح مفقود", "Key saved": "✔️ تم حفظ المفتاح", "View hidden API keys": "عرض مفاتيح واجهة برمجة التطبيقات المخفية", "AI Response Formatting": "تنسيق رد الذكاء الاصطناعي", "Advanced Formatting": "تنسيق متقدم", "Context Template": "قالب السياق", "Auto-select this preset for Instruct Mode": "تحديد هذا الإعداد تلقائياً لوضع التعليم", "Story String": "سلسلة القصة", "Example Separator": "فاصل المثال", "Chat Start": "بداية الدردشة", "Add Chat Start and Example Separator to a list of stopping strings.": "أضف بداية الدردشة وفاصل الأمثلة إلى قائمة سلاسل التوقف.", "Use as Stop Strings": "استخدم كسلاسل التوقف", "Allow Jailbreak": "السماح بالجيلبريك", "Context Order": "ترتيب السياق", "Summary": "ملخص", "Author's Note": "مذكرة المؤلف", "Example Dialogues": "أمثلة للحوارات", "Hint": "تَلمِيح:", "In-Chat Position not affected": "تتأثر أوامر الملخص وملاحظات المؤلف فقط عندما لا يكون لديهم موضع محدد داخل الدردشة.", "Instruct Mode": "وضع التعليم", "Enabled": "م<PERSON>عل", "instruct_bind_to_context": "في حالة التمكين، سيتم تحديد قوالب السياق تلقائيًا بناءً على اسم قالب التعليمات المحدد أو حسب التفضيل.", "Bind to Context": "ربط بالسياق", "Presets": "الإعدادات المسبقة", "Auto-select this preset on API connection": "تحديد هذا الإعداد تلقائياً عند الاتصال بواجهة برمجة التطبيقات", "Activation Regex": "تنشيط Regex", "Wrap Sequences with Newline": "لف السلاسل بسطر جديد", "Replace Macro in Sequences": "استبدال الماكرو في التسلسلات", "Skip Example Dialogues Formatting": "تخطي مثال تنسيق الحوار", "Include Names": "تضمين الأسماء", "Force for Groups and Personas": "فرض للمجموعات والشخصيات", "System Prompt": "دعوة النظام", "Instruct Mode Sequences": "سلاسل وضع التعليم", "System Prompt Wrapping": "التفاف النظام الفوري", "Inserted before a System prompt.": "تم إدراجه قبل مطالبة النظام.", "System Prompt Prefix": "بادئة موجه النظام", "Inserted after a System prompt.": "تم إدراجه بعد مطالبة النظام.", "System Prompt Suffix": "لاحقة موجه النظام", "Chat Messages Wrapping": "تغليف رسائل الدردشة", "Inserted before a User message and as a last prompt line when impersonating.": "يتم إدراجه قبل رسالة المستخدم وكسطر مطالبة أخير عند انتحال الشخصية.", "User Message Prefix": "بادئة رسالة المستخدم", "Inserted after a User message.": "تم إدراجه بعد رسالة المستخدم.", "User Message Suffix": "لاحقة رسالة المستخدم", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "يتم إدراجه قبل رسالة المساعد وكوسطر مطالبة أخير عند إنشاء رد الذكاء الاصطناعي.", "Assistant Message Prefix": "بادئة رسالة المساعد", "Inserted after an Assistant message.": "تم إدراجه بعد رسالة مساعد.", "Assistant Message Suffix": "لاحقة رسالة المساعد", "Inserted before a System (added by slash commands or extensions) message.": "تم إدراجه قبل رسالة النظام (المضافة بواسطة أوامر أو ملحقات الشرطة المائلة).", "System Message Prefix": "بادئة رسالة النظام", "Inserted after a System message.": "تم إدراجه بعد رسالة النظام.", "System Message Suffix": "لاحقة رسالة النظام", "If enabled, System Sequences will be the same as User Sequences.": "في حالة التمكين، ستكون تسلسلات النظام هي نفس تسلسلات المستخدم.", "System same as User": "النظام نفس المستخدم", "Misc. Sequences": "تسلسلات متنوعة", "Inserted before the first Assistant's message.": "تم إدراجه قبل رسالة المساعد الأول.", "First Assistant Prefix": "بادئة المساعد الأول", "instruct_last_output_sequence": "يتم إدراجه قبل رسالة المساعد الأخيرة أو كسطر مطالبة أخير عند إنشاء رد AI (باستثناء دور محايد/نظام).", "Last Assistant Prefix": "بادئة المساعد الأخيرة", "Will be inserted as a last prompt line when using system/neutral generation.": "سيتم إدراجه كسطر مطالبة أخير عند استخدام إنشاء النظام/المحايد.", "System Instruction Prefix": "بادئة تعليمات النظام", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "إذا تم إنشاء تسلسل توقف، فسيتم إزالة كل شيء بعده من الإخراج (شاملًا).", "Stop Sequence": "توقف السلسلة", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "سيتم إدراجه في بداية سجل الدردشة إذا لم يبدأ برسالة مستخدم.", "User Filler Message": "رسالة حشو المستخدم", "Context Formatting": "تنسيق السياق", "(Saved to Context Template)": "(يتم حفظه في قالب السياق)", "Always add character's name to prompt": "إضافة دائمًا اسم الشخصية إلى الحديث", "Generate only one line per request": "توليد سطر واحد فقط لكل طلب", "Trim Incomplete Sentences": "تقليم الجمل غير المكتملة", "Include Newline": "تضمين سطر جديد", "Misc. Settings": "إعدادات متنوعة", "Collapse Consecutive Newlines": "طي الأسطر الجديدة المتتالية", "Trim spaces": "تقليم الفراغات", "Tokenizer": "فاصل النصوص", "Token Padding": "امتداد الرموز", "Start Reply With": "بد<PERSON> الرد مع", "AI reply prefix": "بادئة رد الذكاء الاصطناعي", "Show reply prefix in chat": "إظهار بادئة الرد في الدردشة", "Non-markdown strings": "سلاسل غير Markdown", "separate with commas w/o space between": "فصل بفواصل دون مسافة بينها", "Custom Stopping Strings": "سلاسل توقف مخصصة", "JSON serialized array of strings": "مصفوفة سلسلة JSON متسلسلة", "Replace Macro in Stop Strings": "استبدال الماكرو في سلاسل التوقف المخصصة", "Auto-Continue": "المتابعة التلقائية", "Allow for Chat Completion APIs": "السماح بواجهات برمجة التطبيقات لإكمال الدردشة", "Target length (tokens)": "الطول المستهدف (رمو<PERSON>)", "World Info": "معلومات العالم", "Locked = World Editor will stay open": "مقفول = محرر العالم سيبقى مفتوحاً", "Worlds/Lorebooks": "العوالم/Lorebook-ات", "Active World(s) for all chats": "العالم(أو العوالم) النشط(ة) لجميع الدردشات", "-- World Info not found --": "-- معلومات العالم غير موجودة --", "Global World Info/Lorebook activation settings": "إعدادات تفعيل معلومات العالم العالمي/Lorebook", "Click to expand": "انقر فوق لتوسيع", "Scan Depth": "<PERSON><PERSON><PERSON> المسح", "Context %": "نسبة السياق", "Budget Cap": "<PERSON><PERSON> الميزانية", "(0 = disabled)": "(0 = معطل)", "Scan chronologically until reached min entries or token budget.": "قم بالمسح بشكل زمني حتى الوصول إلى الحد الأدنى من الإدخالات أو ميزانية الرمز المميز.", "Min Activations": "الح<PERSON> الأدنى للتفعيلات", "Max Depth": "<PERSON><PERSON><PERSON><PERSON> عمق", "(0 = unlimited, use budget)": "(0 = غير محدود، استخدم الميزانية)", "Insertion Strategy": "استراتيجية الإدراج", "Sorted Evenly": "ترتيب متساوي", "Character Lore First": "سرد الشخصية أولاً", "Global Lore First": "سرد العالم أولاً", "Entries can activate other entries by mentioning their keywords": "يمكن للإدخالات تنشيط إدخالات أخرى عن طريق ذكر كلماتهم الرئيسية", "Recursive Scan": "فحص متكرر", "Lookup for the entry keys in the context will respect the case": "سيحترم البحث عن مفاتيح الإدخال في السياق الحالة", "Case Sensitive": "حساس لحالة الأحرف", "If the entry key consists of only one word, it would not be matched as part of other words": "إذا كانت مفتاح الإدخال يتكون من كلمة واحدة فلن يتم مطابقتها كجزء من كلمات أخرى", "Match Whole Words": "مطابقة الكلمات الكاملة", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "سيتم تحديد الإدخالات التي تحتوي على أكبر عدد من تطابقات المفاتيح فقط لتصفية مجموعة التضمين", "Use Group Scoring": "استخدم تسجيل المجموعة", "Alert if your world info is greater than the allocated budget.": "تنبيه إذا كانت معلومات العالم الخاصة بك أكبر من الميزانية المخصصة.", "Alert On Overflow": "تنبيه عند التجاوز", "New": "جديد", "or": "أو", "--- Pick to Edit ---": "--- اخت<PERSON> للتحرير ---", "Rename World Info": "إعادة تسمية معلومات العالم", "Open all Entries": "فتح جميع الإدخالات", "Close all Entries": "إغلاق جميع الإدخالات", "New Entry": "إد<PERSON><PERSON><PERSON> جديد", "Fill empty Memo/Titles with Keywords": "املأ الملاحظات / العناوين الفارغة بالكلمات الرئيسية", "Import World Info": "استيراد معلومات العالم", "Export World Info": "تصدير معلومات العالم", "Duplicate World Info": "تكرار معلومات العالم", "Delete World Info": "حذ<PERSON> معلومات العالم", "Search...": "بحث...", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Priority": "أولوية", "Custom": "مخصص", "Title A-Z": "العنوان من أ-ي", "Title Z-A": "العنوان من ي-أ", "Tokens ↗": "الرموز ↗", "Tokens ↘": "الرموز ↘", "Depth ↗": "العمق ↗", "Depth ↘": "العمق ↘", "Order ↗": "الترتيب ↗", "Order ↘": "الترتيب ↘", "UID ↗": "معرف فريد ↗", "UID ↘": "معرف فريد ↘", "Trigger% ↗": "مشغل% ↗", "Trigger% ↘": "مشغل% ↘", "Refresh": "تحديث", "User Settings": "إعدادات المستخدم", "Simple": "بسيط", "Advanced": "متقدم", "UI Language": "لغة", "Account": "ح<PERSON><PERSON><PERSON>", "Admin Panel": "لوحة الادارة", "Logout": "تسجيل خروج", "Search Settings": "إعدادات البحث", "UI Theme": "مظهر واجهة المستخدم", "Import a theme file": "استيراد ملف السمة", "Export a theme file": "تصدير ملف موضوع", "Delete a theme": "حذ<PERSON> موضوع", "Update a theme file": "تحديث ملف السمة", "Save as a new theme": "حفظ كسمة جديدة", "Avatar Style:": "نمط الصورة الرمزية", "Circle": "دائرة", "Square": "مربع", "Rectangle": "مستطيل", "Chat Style:": "نمط الدردشة:", "Flat": "مستوي\nفقاعات\nوثيقة", "Bubbles": "فقاعات", "Document": "وثيقة", "Specify colors for your theme.": "تحديد الألوان للموضوع الخاص بك.", "Theme Colors": "ألوان الموضوع", "Main Text": "النص الرئيسي", "Italics Text": "نص مائل", "Underlined Text": "نص تحته خط", "Quote Text": "نص الاقتباس", "Shadow Color": "لون الظل", "Chat Background": "خلفية الدردشة", "UI Background": "خلفية الواجهة الرسومية", "UI Border": "حدود الواجهة الرسومية", "User Message Blur Tint": "تظليل رسالة المستخدم", "AI Message Blur Tint": "تظليل رسالة الذكاء الاصطناعي", "Chat Width": "عرض الدردشة", "Width of the main chat window in % of screen width": "عرض نافذة الدردشة الرئيسية بنسبة % من عرض الشاشة", "Font Scale": "مقيا<PERSON> الخط", "Font size": "حج<PERSON> الخط", "Blur Strength": "قوة التمويه", "Blur strength on UI panels.": "قوة التمويه على لوحات واجهة المستخدم.", "Text Shadow Width": "عرض <PERSON>ل النص", "Strength of the text shadows": "قوة ظلال النص", "Disables animations and transitions": "تعطيل الرسوم المتحركة والانتقالات", "Reduced Motion": "تقليل الحركة", "removes blur from window backgrounds": "إزالة الضبابية من خلفيات النوافذ لتسريع التقديم", "No Blur Effect": "عدم وجود تأثير ضبابية", "Remove text shadow effect": "إزالة تأثير الظل النصي", "No Text Shadows": "عدم وجود ظلال للنص", "Reduce chat height, and put a static sprite behind the chat window": "تقليل ارتفاع الدردشة ووضع صورة ثابتة خلف نافذة الدردشة", "Waifu Mode": "وضع الوايفو", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "إظهار دائمًا القائمة الكاملة لبنود سياق إجراءات الرسائل لرسائل الدردشة بدلاً من إخفائها خلف '...' ", "Auto-Expand Message Actions": "توسيع إجراءات الرسالة تلقائيًا", "Alternative UI for numeric sampling parameters with fewer steps": "واجهة مستخدم بديلة لمعلمات العينات الرقمية بعدد أقل من الخطوات", "Zen Sliders": "منزلقات الزن", "Entirely unrestrict all numeric sampling parameters": "رفع القيود عن جميع المعلمات الرقمية للعينات بالكامل", "Mad Lab Mode": "وضع المختبر المجنون", "Time the AI's message generation, and show the duration in the chat log": "توقيت إنتاج رسائل الذكاء الاصطناعي، وعرض المدة في سجل الدردشة", "Message Timer": "مؤقت الرسالة", "Show a timestamp for each message in the chat log": "إظهار الطابع الزمني لكل رسالة في سجل الدردشة", "Chat Timestamps": "طوابع الزمن في الدردشة", "Show an icon for the API that generated the message": "إظهار أيقونة للواجهة البرمجية التطبيقية التي أنشأت الرسالة", "Model Icon": "أيقونة النموذج", "Show sequential message numbers in the chat log": "إظهار أرقام الرسائل التسلسلية في سجل الدردشة", "Message IDs": "معرفات الرسائل", "Hide avatars in chat messages.": "إخفاء الصور الرمزية في رسائل الدردشة.", "Hide Chat Avatars": "إخفاء الصور الرمزية للدردشة", "Show the number of tokens in each message in the chat log": "عرض عدد الرموز في كل رسالة في سجل الدردشة", "Show Message Token Count": "عرض عدد الرموز في الرسالة", "Single-row message input area. Mobile only, no effect on PC": "منطقة إدخال رسالة بصف واحد. فقط للهواتف المحمولة، لا تؤثر على الكمبيوتر الشخصي", "Compact Input Area (Mobile)": "منطقة إدخال مضغوطة (الهات<PERSON> المحمول)", "In the Character Management panel, show quick selection buttons for favorited characters": "في لوحة إدارة الشخصيات، إظهار أزرار الاختيار السريع للشخصيات المفضلة", "Characters Hotswap": "تبديل سريع للشخصيات", "Enable magnification for zoomed avatar display.": "تمكين التكبير لعرض الصورة الرمزية المكبرة.", "Avatar Hover Magnification": "الصورة الرمزية تحوم التكبير", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "لتمكين تأثير التكبير عند التمرير عند عرض الصورة الرمزية التي تم تكبيرها بعد النقر على صورة الصورة الرمزية في الدردشة.", "Show tagged character folders in the character list": "عرض مجلدات الشخصيات الموسومة في قائمة الشخصيات", "Tags as Folders": "الوسوم كمجلدات", "Tags_as_Folders_desc": "التغيير الأخير: يجب وضع علامة على العلامات كمجلدات في قائمة إدارة العلامات لتظهر على هذا النحو. انقر هنا لعرضه.", "Character Handling": "معالجة الشخصية", "If set in the advanced character definitions, this field will be displayed in the characters list.": "إذا تم تعيينه في تعريفات الشخصيات المتقدمة، سيتم عرض هذا الحقل في قائمة الشخصيات.", "Char List Subheader": "العنوان الفرعي لقائمة الأحرف", "Character Version": "إصدار الشخصية", "Created by": "تم إنشاؤه بواسطة", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "استخدام مطابقة غامضة، والبحث في الشخصيات في القائمة حسب جميع حقول البيانات، ليس فقط بواسطة جزء من الاسم", "Advanced Character Search": "بح<PERSON> متقدم عن الشخصيات", "If checked and the character card contains a prompt override (System Prompt), use that instead": "إذا تم التحقق وكانت بطاقة الشخصية تحتوي على تجاوز للمطالبة (المطالبة النظامية)، استخدم ذلك بدلاً من ذلك", "Prefer Character Card Prompt": "تفضيل التعليمات من بطاقة الشخصية", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "إذا تم التحقق وكانت بطاقة الشخصية تحتوي على تجاوز للكسر (تعليمات تاريخ المشاركة)، استخدم ذلك بدلاً من ذلك", "Prefer Character Card Jailbreak": "تفضيل كسر الحصار من بطاقة الشخصية", "never_resize_avatars_tooltip": "تجنب اقتصاص صور الأحرف المستوردة وتغيير حجمها. عند إيقاف التشغيل، قم بالقص/تغيير الحجم إلى 512 × 768.", "Never resize avatars": "لا تغيير حجم الصور الرمزية أبدًا", "Show actual file names on the disk, in the characters list display only": "عرض الأسماء الفعلية للملفات على القرص، في عرض قائمة الشخصيات فقط", "Show avatar filenames": "عرض أسماء ملفات الصور الرمزية", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "طلب استيراد العلامات المضمنة في البطاقة عند استيراد الشخصية. في غير ذلك، تتم تجاهل العلامات المضمنة", "Import Card Tags": "استيراد وسوم البطاقة", "Hide character definitions from the editor panel behind a spoiler button": "إخفاء تعريفات الشخصيات من لوحة التحرير وراء زر التلميح", "Spoiler Free Mode": "وضع خالٍ من حرق الاحداث", "Miscellaneous": "متنوع", "Reload and redraw the currently open chat": "إعادة تحميل وإعادة رسم الدردشة المفتوحة حاليًا", "Reload Chat": "إعادة تحميل الدردشة", "Debug Menu": "قائمة التصحيح", "Smooth Streaming": "التد<PERSON>ق السلس", "Experimental feature. May not work for all backends.": "الميزة التجريبية. قد لا تعمل مع جميع الواجهات الخلفية.", "Slow": "بطيء", "Fast": "سريع", "Play a sound when a message generation finishes": "تشغيل صوت عند الانتهاء من إنشاء الرسالة", "Message Sound": "صوت الرسالة", "Only play a sound when ST's browser tab is unfocused": "تشغيل الصوت فقط عندما تكون علامة تبويب متصفح ST غير مركزة", "Background Sound Only": "صوت الخلفية فقط", "Reduce the formatting requirements on API URLs": "تقليل متطلبات التنسيق على عناوين URL الخاصة بالواجهة البرمجية التطبيقية", "Relaxed API URLS": "عناوين URL لواجهة برمجة التطبيقات المرنة", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "اسأل لاستيراد معلومات العالم/دفتر السرد لكل شخصية جديدة مع دفتر سرد مضمن. إذا لم يتم التحقق، سيتم عرض رسالة موجزة بدلاً من ذلك", "Lorebook Import Dialog": "مربع حوار استيراد كتاب المعرفة", "Restore unsaved user input on page refresh": "استعادة الإدخالات غير المحفوظة للمستخدم عند تحديث الصفحة", "Restore User Input": "استعادة إدخال المستخدم", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "السماح بإعادة تحديد موقع بعض عناصر واجهة المستخدم بسحبها. فقط للكمبيوتر، لا تؤثر على الهواتف المحمولة", "Movable UI Panels": "لوحات واجهة المستخدم القابلة للتحريك", "MovingUI preset. Predefined/saved draggable positions": "إعداد مسبق لواجهة المستخدم المتحركة. مواقع قابلة للسحب محددة/محفوظة مسبقًا", "MUI Preset": "الإعداد المسبق لـ MUI:", "Save movingUI changes to a new file": "حفظ التغييرات في واجهة المستخدم المتحركة في ملف جديد", "Reset MovingUI panel sizes/locations.": "إعادة تعيين أحجام/مواقع لوحة MovingUI.", "Apply a custom CSS style to all of the ST GUI": "تطبيق نمط CSS مخصص على كل واجهة ST", "Custom CSS": "CSS مخصص", "Expand the editor": "قم بتوسيع المحرر", "Chat/Message Handling": "معالجة الدردشة/الرسائل", "# Messages to Load": "# رسالة. للتحميل", "The number of chat history messages to load before pagination.": "عدد رسائل سجل الدردشة التي يجب تحميلها قبل الترقيم الصفحي.", "(0 = All)": "(0 = الكل)", "Streaming FPS": "معدل الإطارات في البث", "Update speed of streamed text.": "تحديث سرعة النص المتدفق.", "Example Messages Behavior": "سلوك الرسائل المثالية", "Gradual push-out": "طرد تدريجي", "Always include examples": "تضمين الأمثلة دائمًا", "Never include examples": "عدم تضمين الأمثلة أبدًا", "Send on Enter": "إرسال عند الضغط على Enter", "Disabled": "معطل", "Automatic (PC)": "تلقائي (الكمبيوتر الشخصي)", "Press Send to continue": "اضغط على 'إرسال' للمتابعة", "Show a button in the input area to ask the AI to continue (extend) its last message": "عرض زر في منطقة الإدخال لطلب من الذكاء الاصطناعي المتابعة (تمديد) رسالته الأخيرة", "Quick 'Continue' button": "زر 'متابعة' السريع", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "عرض أزرار السهم على آخر رسالة في الدردشة لإنشاء ردود ذكاء اصطناعي بديلة. على الكمبيوتر الشخصي والهاتف المحمول", "Swipes": "انزلاقات", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "السماح باستخدام إيماءات السحب على آخر رسالة في الدردشة لتشغيل إنشاء السحب. فقط على الهاتف المحمول، لا تؤثر على الكمبيوتر الشخصي", "Gestures": "الإيماءات", "Auto-load Last Chat": "تحميل الدردشة الأخيرة تلقائيًا", "Auto-scroll Chat": "التمرير التلقائي للدردشة", "Save edits to messages without confirmation as you type": "حفظ التحريرات في الرسائل دون تأكيد أثناء الكتابة", "Auto-save Message Edits": "حفظ التعديلات التلقائي للرسائل", "Confirm message deletion": "تأ<PERSON>يد حذف الرسالة", "Auto-fix Markdown": "إصلاح Markdown تلقائيًا", "Disallow embedded media from other domains in chat messages": "عدم السماح بالوسائط المضمنة من المجالات الأخرى في رسائل الدردشة.", "Forbid External Media": "منع وسائط خارجية", "Allow {{char}}: in bot messages": "السماح بـ {{char}}: في رسائل البوت", "Allow {{user}}: in bot messages": "السماح بـ {{user}}: في رسائل البوت", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "تخطي الترميز للأحرف < و > في نص الرسالة، مما يسمح بمجموعة فرعية من العلامات التنسيقية HTML وكذلك Markdown", "Show tags in responses": "عرض الوسوم في الردود", "Allow AI messages in groups to contain lines spoken by other group members": "السماح لرسائل الذكاء الاصطناعي في المجموعات بأن تحتوي على أسطر يتحدثها أعضاء المجموعة الآخرين", "Relax message trim in Groups": "تخفيف تقليم الرسائل في المجموعات", "Log prompts to console": "تسجيل التعليمات إلى وحدة التحكم", "Requests logprobs from the API for the Token Probabilities feature": "يطلب logprobs من الواجهة البرمجية التطبيقية لميزة الرموز الاحتمالية", "Request token probabilities": "احتماليات طلب الرمز", "Automatically reject and re-generate AI message based on configurable criteria": "رفض الرسالة التي تم إنشاؤها تلقائيًا وإعادة إنشاءها بناءً على معايير قابلة للتكوين", "Auto-swipe": "السح<PERSON> التلقائي", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "تمكين وظيفة السحب التلقائي. الإعدادات في هذا القسم تؤثر فقط عند تمكين السحب التلقائي", "Minimum generated message length": "الح<PERSON> الأدنى لطول الرسالة المولدة", "If the generated message is shorter than these many characters, trigger an auto-swipe": "إذا كانت الرسالة المولدة أقصر من هذا، فتحريض السحب التلقائي", "Blacklisted words": "الكلمات الممنوعة", "words you dont want generated separated by comma ','": "الكلمات التي لا تريد توليدها مفصولة بفاصلة ','", "Blacklisted word count to swipe": "ع<PERSON><PERSON> الكلمات الممنوعة للسحب", "Minimum number of blacklisted words detected to trigger an auto-swipe": "الح<PERSON> الأ<PERSON><PERSON><PERSON> لعدد الكلمات في القائمة السوداء المكتشفة لتشغيل السحب التلقائي.", "AutoComplete Settings": "إعدادات الإكمال التلقائي", "Automatically hide details": "إخفاء التفاصيل تلقائيًا", "Determines how entries are found for autocomplete.": "تحديد كيفية العثور على الإدخالات للإكمال التلقائي.", "Autocomplete Matching": "مطابقة", "Starts with": "ابدا ب", "Includes": "يشمل", "Fuzzy": "<PERSON><PERSON><PERSON><PERSON>", "Sets the style of the autocomplete.": "يضبط نمط الإكمال التلقائي.", "Autocomplete Style": "أسلوب", "Follow Theme": "اتبع الموضوع", "Dark": "مظلم", "Sets the font size of the autocomplete.": "يضبط حجم الخط للإكمال التلقائي.", "Sets the width of the autocomplete.": "يضبط عرض الإكمال التلقائي.", "Autocomplete Width": "<PERSON><PERSON><PERSON>", "chat input box": "مربع إدخال الدردشة", "entire chat width": "عرض الدردشة بالكامل", "full window width": "عرض النافذة بالكامل", "STscript Settings": "إعدادات ستسكريبت", "Sets default flags for the STscript parser.": "يقوم بتعيين العلامات الافتراضية لمحلل STscript.", "Parser Flags": "أعلا<PERSON> المحلل اللغوي", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "قم بالتبديل إلى الهروب الأكثر صرامة، مما يسمح بالهروب من جميع الأحرف المحددة باستخدام شرطة مائلة عكسية، كما يمكن الهروب من الخطوط المائلة العكسية أيضًا.", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "استبدل كافة وحدات الماكرو {{getvar::}} و{{getglobalvar::}} بمتغيرات محددة النطاق لتجنب استبدال الماكرو المزدوج.", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "تغيير صورة الخلفية", "Filter": "تصفية", "Automatically select a background based on the chat context": "تحديد خلفية تلقائيًا استنادًا إلى سياق الدردشة", "Auto-select": "التحديد التلقائي", "System Backgrounds": "خلفيات النظام", "Chat Backgrounds": "خلفيات الدردشة", "bg_chat_hint_1": "خلفيات الدردشة التي تم إنشاؤها باستخدام", "bg_chat_hint_2": "سوف يظهر الامتداد هنا", "Extensions": "امتدادات", "Notify on extension updates": "الإخطار بالتحديثات الإضافية", "Manage extensions": "إدارة الامتدادات", "Import Extension From Git Repo": "استيراد الامتداد من مستودع Git", "Install extension": "تثبيت الامتداد", "Extras API:": "واجهة برمجة التطبيقات الإضافية:", "Auto-connect": "الاتصال التلقائي", "Extras API URL": "عنوان URL لواجهة برمجة التطبيقات الإضافية", "Extras API key (optional)": "م<PERSON><PERSON><PERSON><PERSON> API الإضافي (اختياري)", "Persona Management": "إدارة الشخصية", "How do I use this?": "كيف يمكنني استخدام هذا؟", "Click for stats!": "انقر للحصول على الإحصائيات!", "Usage Stats": "إحصائيات الاستخدام", "Backup your personas to a file": "انسخ نسخة احتياطية من شخصياتك إلى ملف", "Backup": "نسخة احتياطية", "Restore your personas from a file": "استعادة شخصياتك من ملف", "Restore": "استعادة", "Create a dummy persona": "إنشاء شخصية وهمية", "Create": "إنشاء", "Toggle grid view": "تبديل عرض الشبكة", "No persona description": "[بدون وصف]", "Name": "الاسم", "Enter your name": "أ<PERSON><PERSON>ل اسمك", "Click to set a new User Name": "انقر لتعيين اسم مستخدم جديد", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "انقر لتأمين الشخصية المحددة إلى الدردشة الحالية. انقر مرة أخرى لإزالة القفل.", "Click to set user name for all messages": "انقر لتعيين اسم المستخدم لجميع الرسائل", "Persona Description": "وصف الشخصية", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "مثال: [{{user}} هي فتاة  على شكل قطة رومانية  عمرها 28 عامًا.]", "Tokens persona description": "الرموز", "Position:": "الموضع:", "In Story String / Prompt Manager": "في سلسلة القصة / إدارة التعليمات", "Top of Author's Note": "أعلى ملاحظة المؤلف", "Bottom of Author's Note": "أسفل ملاحظة المؤلف", "In-chat @ Depth": "داخل الدردشة @ العمق", "Depth:": "العمق:", "Role:": "دور:", "System": "نظام", "User": "مستخدم", "Assistant": "مسا<PERSON>د", "Show notifications on switching personas": "إظهار الإشعارات عند التبديل بين الشخصيات", "Character Management": "إدارة الشخصيات", "Locked = Character Management panel will stay open": "مقفل = ستبقى لوحة إدارة الشخصيات مفتوحة", "Select/Create Characters": "اختر / إنشاء الشخصيات", "Favorite characters to add them to HotSwaps": "اختر الشخصيات المفضلة لإضافتها إلى HotSwaps", "Token counts may be inaccurate and provided just for reference.": "قد تكون عدادات الرموز غير دقيقة وتُقدم فقط للإشارة.", "Total tokens": "مجموع الرموز", "Calculating...": "جارٍ الحساب...", "Tokens": "الرموز", "Permanent tokens": "الرموز الدائمة", "Permanent": "دائم", "About Token 'Limits'": "حول \"حدود\" ال<PERSON><PERSON><PERSON> المميز", "Toggle character info panel": "تبديل لوحة معلومات الشخصية", "Name this character": "اسم هذا الشخصية", "extension_token_counter": "الرموز:", "Click to select a new avatar for this character": "انقر لتحديد صورة رمزية جديدة لهذه الشخصية", "Add to Favorites": "أض<PERSON> <PERSON><PERSON> المفضلة", "Advanced Definition": "تعريف متقدم", "Character Lore": "قصة الشخصية", "Chat Lore": "تقاليد الدردشة", "Export and Download": "تصدير وتنزيل", "Duplicate Character": "تكرار الشخصية", "Create Character": "إنشاء شخصية", "Delete Character": "حذ<PERSON> الشخصية", "More...": "المزيد...", "Link to World Info": "رابط لمعلومات العالم", "Import Card Lore": "استيراد البطاقة السردية", "Scenario Override": "تجاوز السيناريو", "Convert to Persona": "تحويل إلى شخصية", "Rename": "إعادة التسمية", "Link to Source": "راب<PERSON> إلى المصدر", "Replace / Update": "استبدال / تحديث", "Import Tags": "استيراد العلامات", "Search / Create Tags": "البحث / إنشاء العلامات", "View all tags": "عرض جميع العلامات", "Creator's Notes": "ملاحظات الصانع", "Show / Hide Description and First Message": "إظهار / إخفاء الوصف والرسالة الأولى", "Character Description": "وصف الشخصية", "Click to allow/forbid the use of external media for this character.": "انقر للسماح/منع استخدام الوسائط الخارجية لهذه الشخصية.", "Ext. Media": "تحويلة. وسائط", "Describe your character's physical and mental traits here.": "صف صفات شخصيتك الجسدية والعقلية هنا.", "First message": "الرسالة الأولى", "Click to set additional greeting messages": "انقر لتعيين رسائل تحية إضافية", "Alt. Greetings": "بديل. تحيات", "This will be the first message from the character that starts every chat.": "سيكون هذا أول رسالة من الشخصية التي تبدأ كل دردشة.", "Group Controls": "ضوابط المجموعة", "Chat Name (Optional)": "اسم المحادثة (اختياري)", "Click to select a new avatar for this group": "انقر لتحديد صورة رمزية جديدة لهذه المجموعة", "Group reply strategy": "استراتيجية الرد في المجموعة", "Natural order": "الترتيب الطبيعي", "List order": "ترتيب القائمة", "Group generation handling mode": "وضع التعامل مع إنشاء المجموعة", "Swap character cards": "مبادلة بطاقات الشخصية", "Join character cards (exclude muted)": "الانضمام إلى بطاقات الشخصيات (استبعاد كتم الصوت)", "Join character cards (include muted)": "انضم إلى بطاقات الشخصيات (بما في ذلك البطاقات الصامتة)", "Inserted before each part of the joined fields.": "تم إدراجه قبل كل جزء من الحقول المرتبطة.", "Join Prefix": "الانضمام إلى البادئة", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "عند تحديد \"الانضمام إلى بطاقات الأحرف\"، يتم ضم كافة الحقول الخاصة بالأحرف معًا.\rوهذا يعني أنه في سلسلة القصة على سبيل المثال، سيتم دمج جميع أوصاف الشخصيات في نص واحد كبير.\rإذا كنت تريد فصل هذه الحقول، فيمكنك تحديد بادئة أو لاحقة هنا.\r\rتدعم هذه القيمة وحدات الماكرو العادية وستستبدل أيضًا {{char}} باسم الحرف ذي الصلة و<FIELDNAME> باسم الجزء (على سبيل المثال: الوصف، والشخصية، والسيناريو، وما إلى ذلك)", "Inserted after each part of the joined fields.": "يتم إدراجه بعد كل جزء من الحقول المرتبطة.", "Join Suffix": "انضم إلى لاحقة", "Set a group chat scenario": "تعيين سيناريو للمحادثة الجماعية", "Click to allow/forbid the use of external media for this group.": "انقر للسماح/منع استخدام الوسائط الخارجية لهذه المجموعة.", "Restore collage avatar": "استعادة الصورة الرمزية للشخصية", "Allow self responses": "السماح با<PERSON><PERSON>د على الذات", "Auto Mode": "الوضع التلقائي", "Auto Mode delay": "تأخير الوضع التلقائي", "Hide Muted Member Sprites": "إخفاء أعضاء Sprites المكتومة", "Current Members": "الأعضاء الحاليين", "Add Members": "إضافة أعضاء", "Create New Character": "إنشاء شخصية جديدة", "Import Character from File": "استيراد شخصية من ملف", "Import content from external URL": "استيراد المحتوى من عنوان URL الخارجي", "Create New Chat Group": "إنشاء مجموعة دردشة جديدة", "Characters sorting order": "ترتيب فرز الشخصيات", "A-Z": "أ-ي", "Z-A": "ي-أ", "Newest": "الأحدث", "Oldest": "الأقدم", "Favorites": "المفضلة", "Recent": "الأخيرة", "Most chats": "معظم الدردشات", "Least chats": "أقل الدردشات", "Most tokens": "معظم الرموز", "Least tokens": "أقل الرموز", "Random": "عشوائي", "Toggle character grid view": "تبديل طريقة عرض الى شبكة للشخصيات", "Bulk_edit_characters": "تحرير الشخصيات جميعها", "Bulk select all characters": "تحديد كافة الشخصيات بالجملة", "Bulk delete characters": "حذف الشخصيات جميعها", "popup-button-save": "<PERSON><PERSON><PERSON><PERSON>", "popup-button-yes": "نعم", "popup-button-no": "لا", "popup-button-cancel": "يلغي", "popup-button-import": "يستورد", "Advanced Definitions": "تعريفات متقدمة", "Prompt Overrides": "التجاوزات السريعة", "(For Chat Completion and Instruct Mode)": "(لاستكمال الدردشة ووضع التعليمات)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "أدخل {{original}} في أي مربع لتضمين التعليمات الافتراضية المعنية من إعدادات النظام.", "Main Prompt": "التعليمات الرئيسية", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "سيحل أي محتوى هنا محل التعليمة الرئيسية الافتراضية المستخدمة لهذا الشخصية.", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "سيحل أي محتوى هنا محل التعليمة الافتراضية لكسر الحجز المستخدمة لهذا الشخصية.", "Creator's Metadata (Not sent with the AI prompt)": "بيانات التعريف للمنشئ (لا يتم إرسالها مع التعليمات الذكية)", "Creator's Metadata": "البيانات الوصفية للمنشئ", "(Not sent with the AI Prompt)": "(لم يتم إرساله مع موجه الذكاء الاصطناعي)", "Everything here is optional": "كل ما هو هنا اختياري", "(Botmaker's name / Contact Info)": "اسم المطور / معلومات الاتصال", "(If you want to track character versions)": "(إذا كنت ترغب في تتبع إصدارات الشخصية)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(صف الروبوت، وأعطي استخدام النصائح، أو قائمة النماذج الدردشة التي تم اختبارها. سيتم عرض هذا في قائمة الشخصيات.)", "Tags to Embed": "العلامات المضمنة", "(Write a comma-separated list of tags)": "(اكتب قائمة بفواصل مفصولة للعلامات)", "Personality summary": "ملخص الشخصية", "(A brief description of the personality)": "(وصف موجز للشخصية)", "Scenario": "السيناريو", "(Circumstances and context of the interaction)": "(الظروف والسياق للتفاعل)", "Character's Note": "ملاحظة الشخصية", "(Text to be inserted in-chat @ designated depth and role)": "(النص الذي سيتم إدراجه في الدردشة عند العمق والدور المحددين)", "@ Depth": "@ العمق", "Role": "دور", "Talkativeness": "الكلام", "How often the character speaks in group chats!": "كيفية تحدث الشخصية في محادثات المجموعة!", "How often the character speaks in": "كم مرة تتحدث الشخصية في", "group chats!": "الدردشات الجماعية!", "Shy": "خجول", "Normal": "عادي", "Chatty": "<PERSON><PERSON><PERSON><PERSON>", "Examples of dialogue": "أمثلة على الحوار", "Important to set the character's writing style.": "مهم لتحديد نمط كتابة الشخصية.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(أمثلة على حوار الدردشة. ابدأ كل مثال بـ START في سطر جديد.)", "Save": "<PERSON><PERSON><PERSON>", "Chat History": "تاريخ الدردشة", "Import Chat": "استيراد الدردشة", "Copy to system backgrounds": "نسخ إلى خلفيات النظام", "Rename background": "إعادة تسمية الخلفية", "Lock": "قفل", "Unlock": "الغاء القفل", "Delete background": "حذ<PERSON> الخلفية", "Chat Scenario Override": "تجاوز سيناريو الدردشة", "Remove": "إزالة", "Type here...": "اكتب هنا...", "Chat Lorebook": "دردشة Lorebook ل", "Chat Lorebook for": "دردشة Lorebook ل", "chat_world_template_txt": "سيتم ربط معلومات العالم المحددة بهذه الدردشة. عند إنشاء رد AI،\n                    سيتم دمجه مع الإدخالات من كتب التراث العالمية والشخصية.", "Select a World Info file for": "حدد ملف معلومات العالم لـ", "Primary Lorebook": "سجل قصة أساسي", "A selected World Info will be bound to this character as its own Lorebook.": "ستُرتبط معلومات العالم المحددة بهذه الشخصية كسجل قصة خاص بها.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "عند إنشاء رد من الذكاء الاصطناعي، سيتم دمجه مع الإدخالات من محدد معلومات العالم العالمي.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "سيقوم تصدير الشخصية أيضًا بتصدير ملف سجل القصة المحدد المضمن في البيانات JSON.", "Additional Lorebooks": "سجلات قصص إضافية", "Associate one or more auxillary Lorebooks with this character.": "ربط سجلات قصص إضافية واحدة أو أكثر مع هذه الشخصية.", "NOTE: These choices are optional and won't be preserved on character export!": "ملاحظة: هذه الاختيارات اختيارية ولن تتم الاحتفاظ بها عند تصدير الشخصية!", "Rename chat file": "إعادة تسمية ملف الدردشة", "Export JSONL chat file": "تصدير ملف الدردشة بتنسيق JSONL", "Download chat as plain text document": "تنزيل الدردشة كمستند نصي عادي", "Delete chat file": "حذ<PERSON> ملف الدردشة", "Use tag as folder": "وضع علامة كمجلد", "Hide on character card": "إخفاء في بطاقة الشخصية", "Delete tag": "<PERSON><PERSON><PERSON> العلامة", "Entry Title/Memo": "عنوان الإدخال/المذكرة", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "حالة دخول وي:\r🔵 ثابت\r🟢 عادي\r🔗 ناقل\r❌ معطل", "WI_Entry_Status_Constant": "ثابت", "WI_Entry_Status_Normal": "طبيعي", "WI_Entry_Status_Vectorized": "متجه", "WI_Entry_Status_Disabled": "<PERSON><PERSON><PERSON><PERSON>", "T_Position": "↑Char: قبل تحديدات الشخصية\n↓Char: بعد تحديدات الشخصية\n↑AN: قبل الملاحظات الخاصة بالمؤلف\n↓AN: بعد الملاحظات الخاصة بالمؤلف\n@D: على عمق", "Before Char Defs": "↑تحديد الشخصية", "After Char Defs": "↓تحديد الشخصية", "Before EM": "↑ م", "After EM": "↓م", "Before AN": "↑AN", "After AN": "↓AN", "at Depth System": "@د ⚙️", "at Depth User": "@د 👤", "at Depth AI": "@د 🤖", "Depth": "<PERSON><PERSON><PERSON>", "Order:": "الترتيب:", "Order": "الترتيب:", "Trigger %:": "مشغل ٪:", "Probability": "احتمالا", "Duplicate world info entry": "إدخال معلومات العالم المكررة", "Delete world info entry": "حذ<PERSON> إ<PERSON><PERSON>ا<PERSON> معلومات العالم", "Comma separated (required)": "فاصلة مفصولة (مطلوب)", "Primary Keywords": "الكلمات الأساسية", "Keywords or Regexes": "الكلمات الرئيسية أو Regexes", "Comma separated list": "قائمة مفصولة بفواصل", "Switch to plaintext mode": "التبديل إلى وضع النص العادي", "Logic": "منطق", "AND ANY": "و ,أي شيء", "AND ALL": "و,الجميع", "NOT ALL": "ليس كل", "NOT ANY": " لا , لأي شيء", "(ignored if empty)": "(يتم تجاهله إذا كان فارغًا)", "Optional Filter": "تصفية اختيارية", "Keywords or Regexes (ignored if empty)": "الكلمات الرئيسية أو التعابير العادية (يتم تجاهلها إذا كانت فارغة)", "Comma separated list (ignored if empty)": "قائمة مفصولة بفواصل (يتم تجاهلها إذا كانت فارغة)", "Use global setting": "استخدام الإعداد العام", "Case-Sensitive": "حساس لحالة الأحرف", "Yes": "نعم", "No": "لا", "Can be used to automatically activate Quick Replies": "يمكن استخدامه لتنشيط الردود السريعة تلقائيًا", "Automation ID": "معرف الأتمتة", "( None )": "( لا أحد )", "Content": "المحتوى", "Exclude from recursion": "استبعاد من التكرار", "Prevent further recursion": "منع المزيد من التكرار", "Delay until recursion": "التأخير حتى العودية", "What this keyword should mean to the AI, sent verbatim": "ما يجب أن يعني هذا الكلمة الرئيسية للذكاء الاصطناعي، ترسل حرفيًا", "Filter to Character(s)": "تصفية إلى الشخصيات", "Character Exclusion": "استبعاد الشخصيات", "-- Characters not found --": "-- الشخصيات غير موجودة --", "Inclusion Group": "مجموعة الإدراج", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "تضمن مجموعات التضمين تنشيط إدخال واحد فقط من المجموعة في المرة الواحدة، إذا تم تشغيل عدة إدخالات.\rيدعم مجموعات متعددة مفصولة بفواصل.\r\rالتوثيق: معلومات العالم - مجموعة الإدماج", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "إعطاء الأولوية لهذا الإدخال: عند تحديده، يتم إعطاء الأولوية لهذا الإدخال من بين جميع التحديدات.\rإذا تم تحديد الأولوية لأكثر من مجموعة، فسيتم اختيار \"الترتيب\" الأعلى.", "Only one entry with the same label will be activated": "سيتم تنشيط مدخل واحد فقط بنفس العلامة", "A relative likelihood of entry activation within the group": "احتمال نسبي لتفعيل الدخول داخل المجموعة", "Group Weight": "وزن المجموعة", "Selective": "انتقائي", "Use Probability": "استخدم الاحتمالية", "Add Memo": "إضافة مذكرة", "Text or token ids": "نص أو [معرفات الرمز المميز]", "close": "يغلق", "prompt_manager_edit": "ي<PERSON><PERSON><PERSON>", "prompt_manager_name": "اسم", "A name for this prompt.": "اسم لهذه المطالبة.", "To whom this message will be attributed.": "لمن ستنسب هذه الرسالة؟", "AI Assistant": "مساعد الذكاء الاصطناعي", "prompt_manager_position": "موضع", "Next to other prompts (relative) or in-chat (absolute).": "موضع الحقن. بجوار المطالبات الأخرى (نسبية) أو داخل الدردشة (مطلقة).", "prompt_manager_relative": "نسبي", "prompt_manager_depth": "<PERSON><PERSON><PERSON>", "0 = after the last message, 1 = before the last message, etc.": "عمق الحقن. 0 = بعد الرسالة الأخيرة، 1 = قبل الرسالة الأخيرة، الخ.", "Prompt": "موضوع", "The prompt to be sent.": "المطالبة ليتم إرسالها.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "لا يمكن تجاوز هذه المطالبة بواسطة بطاقات الأحرف، حتى إذا كان التجاوزات مفضلاً.", "prompt_manager_forbid_overrides": "منع التجاوزات", "reset": "إعادة ضبط", "save": "<PERSON><PERSON><PERSON><PERSON>", "This message is invisible for the AI": "هذه الرسالة غير مرئية للذكاء الاصطناعي", "Message Actions": "إجراءات الرسالة", "Translate message": "ترجمة الرسالة", "Generate Image": "إنشاء صورة", "Narrate": "سرد", "Exclude message from prompts": "استثناء الرسالة من التلميحات", "Include message in prompts": "تضمين الرسالة في التلميحات", "Embed file or image": "تضمين ملف أو صورة", "Create checkpoint": "إنشاء نقطة تفتيش", "Create Branch": "إنشاء فرع", "Copy": "نسخ", "Open checkpoint chat": "فتح دردشة نقطة التفتيش", "Edit": "تعديل", "Confirm": "تأكيد", "Copy this message": "نسخ هذه الرسالة", "Delete this message": "حذف هذه الرسالة", "Move message up": "نقل الرسالة لأعلى", "Move message down": "نقل الرسالة لأسفل", "Enlarge": "تكبير", "Welcome to SillyTavern!": "مرحبا بكم في SillyTavern!", "welcome_message_part_1": "إقر<PERSON> ال", "welcome_message_part_2": "التوثيق الرسمي", "welcome_message_part_3": null, "welcome_message_part_4": "يكتب", "welcome_message_part_5": "في الدردشة للأوامر ووحدات الماكرو.", "welcome_message_part_6": "انضم الي", "Discord server": "<PERSON><PERSON><PERSON> الخلاف", "welcome_message_part_7": "للحصول على المعلومات والإعلانات.", "SillyTavern is aimed at advanced users.": "يستهد<PERSON> المستخدمين المتقدمين.", "If you're new to this, enable the simplified UI mode below.": "إذا كنت جديدًا على هذا، فقم بتمكين وضع واجهة المستخدم المبسطة أدناه.", "Change it later in the 'User Settings' panel.": "قم بتغييره لاحقًا في لوحة \"إعدادات المستخدم\".", "Enable simple UI mode": "تمكين وضع واجهة المستخدم البسيطة", "Looking for AI characters?": "هل تبحث عن شخصيات الذكاء الاصطناعي؟", "onboarding_import": "يستورد", "from supported sources or view": "من المصادر المدعومة أو العرض", "Sample characters": "شخصيات عينة", "Your Persona": "شخصيتك", "Before you get started, you must select a persona name.": "قبل البدء، يجب عليك تحديد اسم شخصي.", "welcome_message_part_8": "يمكن تغيير هذا في أي وقت عبر", "welcome_message_part_9": "أيقونة.", "Persona Name:": "اسم الشخصية:", "Temporarily disable automatic replies from this character": "تعطيل الردود التلقائية مؤقتًا من هذه الشخصية", "Enable automatic replies from this character": "تمكين الردود التلقائية من هذه الشخصية", "Trigger a message from this character": "تفعيل رسالة من هذه الشخصية", "Move up": "نقل لأعلى", "Move down": "نقل لأسفل", "View character card": "عرض بطاقة الشخصية", "Remove from group": "إزالة من المجموعة", "Add to group": "إضافة إلى المجموعة", "Alternate Greetings": "تحية بديلة", "Alternate_Greetings_desc": "سيتم عرضها كتمريرات سريعة على الرسالة الأولى عند بدء محادثة جديدة.\n                يمكن لأعضاء المجموعة اختيار واحد منهم لبدء المحادثة.", "Alternate Greetings Hint": "انقر فوق الزر للبدء!", "(This will be the first message from the character that starts every chat)": "(سيكون هذا أول رسالة من الشخصية التي تبدأ كل دردشة)", "Forbid Media Override explanation": "قدرة الشخصية/المجموعة الحالية على استخدام الوسائط الخارجية في المحادثات.", "Forbid Media Override subtitle": "الوسائط: الصور ومقاطع الفيديو والصوت. خارجي: غير مستضاف على الخادم المحلي.", "Always forbidden": "ممنوع دائما", "Always allowed": "دائما مسموح", "View contents": "عرض المحتويات", "Remove the file": "قم بإزالة الملف", "Unique to this chat": "فريدة من نوعها لهذه الدردشة", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "ترث نقاط التفتيش الملاحظة من أصلها، ويمكن تغييرها بشكل فردي بعد ذلك.", "Include in World Info Scanning": "تضمين في مسح المعلومات العالمية", "Before Main Prompt / Story String": "قبل الموجه الرئيسي/سلسلة القصة", "After Main Prompt / Story String": "بعد الموجه الرئيسي/سلسلة القصة", "as": "مثل", "Insertion Frequency": "تردد الإدراج", "(0 = Disable, 1 = Always)": "(0 = تعطيل، 1 = دائمًا)", "User inputs until next insertion:": "مدخلات المستخدم حتى الإدراج التالي:", "Character Author's Note (Private)": "ملاحظة مؤلف الشخصية (خاصة)", "Won't be shared with the character card on export.": "لن تتم مشاركتها مع بطاقة الشخصية عند التصدير.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "ستتم إضافتها تلقائيًا كملاحظة المؤلف لهذه الشخصية. سيتم استخدامها في مجموعات، ولكن\n                            لا يمكن تعديلها عندما تكون الدردشة الجماعية مفتوحة.", "Use character author's note": "استخدم ملاحظة مؤلف الشخصية", "Replace Author's Note": "استبدال ملاحظة المؤلف", "Default Author's Note": "ملاحظة المؤلف الافتراضية", "Will be automatically added as the Author's Note for all new chats.": "سيتم إضافتها تلقائيًا كملاحظة المؤلف لجميع الدردشات الجديدة.", "Chat CFG": "الدردشة سي إف جي", "1 = disabled": "1 = معطل", "write short replies, write replies using past tense": "كتابة ردود قصيرة، وكتابة الردود باستخدام الزمن الماضي", "Positive Prompt": "موجه إيجابي", "Use character CFG scales": "استخدم مقاييس الأحرف CFG", "Character CFG": "حرف CFG", "Will be automatically added as the CFG for this character.": "ستتم إضافتها تلقائيًا كـ CFG لهذه الشخصية.", "Global CFG": "سي إف جي العالمية", "Will be used as the default CFG options for every chat unless overridden.": "سيتم استخدامه كخيارات CFG الافتراضية لكل دردشة ما لم يتم تجاوزه.", "CFG Prompt Cascading": "CFG المتتالية السريعة", "Combine positive/negative prompts from other boxes.": "اجمع بين المطالبات الإيجابية والسلبية من الصناديق الأخرى.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "على سبيل المثال، يؤدي تحديد مربعات الدردشة والعمومية والأحرف إلى دمج كافة المطالبات السلبية في سلسلة مفصولة بفواصل.", "Always Include": "تضمين دائما", "Chat Negatives": "سلبيات الدردشة", "Character Negatives": "سلبيات الشخصية", "Global Negatives": "السلبيات العالمية", "Custom Separator:": "فاصل مخصص:", "Insertion Depth:": "عمق الإدخال:", "Token Probabilities": "احتمالات الرمز", "Select a token to see alternatives considered by the AI.": "حدد رمزًا مميزًا لرؤية البدائل التي ينظر فيها الذكاء الاصطناعي.", "Not connected to API!": "غير متصل بواجهة برمجة التطبيقات!", "Type a message, or /? for help": "اكتب رسالة، أو /؟ للمساعدة", "Continue script execution": "متابعة تنفيذ البرنامج النصي", "Pause script execution": "إيقاف تن<PERSON>يذ البرنامج النصي مؤقتًا", "Abort script execution": "إحباط تنفيذ البرنامج النصي", "Abort request": "إلغاء الطلب", "Continue the last message": "متابعة الرسالة الأخيرة", "Send a message": "إرسال رسالة", "Close chat": "إغلاق الدردشة", "Toggle Panels": "تبديل اللوحات", "Back to parent chat": "العودة إلى الدردشة الأصلية", "Save checkpoint": "حفظ نقطة التفتيش", "Convert to group": "تحويل إلى مجموعة", "Start new chat": "بدء دردشة جديدة", "Manage chat files": "إدارة ملفات الدردشة", "Delete messages": "حذ<PERSON> الرسائل", "Regenerate": "إعادة توليد", "Ask AI to write your message for you": "طلب من الذكاء الاصطناعي كتابة رسالتك لك", "Impersonate": "تقمص الشخصية", "Continue": "استمر", "Bind user name to that avatar": "ربط اسم المستخدم بتلك الصورة الرمزية", "Change persona image": "تغيير صورة الشخصية", "Select this as default persona for the new chats.": "تحديد هذا كشخصية افتراضية للمحادثات الجديدة.", "Delete persona": "حذ<PERSON> الشخصية", "These characters are the winners of character design contests and have outstandable quality.": "هذه الشخصيات هي الفائزة في مسابقات تصميم الشخصيات وتتمتع بجودة رائعة.", "Contest Winners": "الفائزين في المسابقة", "These characters are the finalists of character design contests and have remarkable quality.": "هذه الشخصيات هي المتأهلين للتصفيات النهائية في مسابقات تصميم الشخصيات وتتمتع بجودة رائعة.", "Featured Characters": "شخصيات مميزة", "Attach a File": "إرفاق ملف", "Open Data Bank": "بنك البيانات المفتوح", "Enter a URL or the ID of a Fandom wiki page to scrape:": "أدخل عنوان URL أو معرف صفحة ويكي Fandom لجمعها:", "Examples:": "أمثلة:", "Example:": "مثال:", "Single file": "مل<PERSON> واحد", "All articles will be concatenated into a single file.": "سيتم تجميع كافة المقالات في ملف واحد.", "File per article": "ملف لكل مقالة", "Each article will be saved as a separate file.": "لا ينصح. سيتم حفظ كل مقالة كملف منفصل.", "Data Bank": "بنك المعلومات", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "ستكون هذه الملفات متاحة للملحقات التي تدعم المرفقات (مثل تخزين المتجهات).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "أنواع الملفات المدعومة: نص عادي، PDF، Markdown، HTML، EPUB.", "Drag and drop files here to upload.": "قم بسحب وإسقاط الملفات هنا للتحميل.", "Date (Newest First)": "التاريخ (الأحدث أولاً)", "Date (Oldest First)": "التاريخ (الأقدم أولاً)", "Name (A-Z)": "الاسم (أ-ي)", "Name (Z-A)": "الاسم (ي-أ)", "Size (Smallest First)": "الحجم (الأصغر أولاً)", "Size (Largest First)": "الحجم (الأكبر أولاً)", "Bulk Edit": "تحرير بالجملة", "Select All": "اختر الكل", "Select None": "لا تختر شيء", "Global Attachments": "المرفقات العالمية", "These files are available for all characters in all chats.": "هذه الملفات متاحة لجميع الشخصيات في جميع الدردشات.", "Character Attachments": "مرفقات الشخصيات", "These files are available the current character in all chats they are in.": "تتوفر هذه الملفات بالحرف الحالي في جميع الدردشات الموجودة فيها.", "Saved locally. Not exported.": "تم الحفظ محليًا. لم يتم تصديرها.", "Chat Attachments": "مرفقات الدردشة", "These files are available to all characters in the current chat.": "هذه الملفات متاحة لجميع الشخصيات في الدردشة الحالية.", "Enter a base URL of the MediaWiki to scrape.": "أدخل عنوان URL الأساسي لـ MediaWiki المراد استخراجه.", "Don't include the page name!": "لا تقم بتضمين اسم الصفحة!", "Enter web URLs to scrape (one per line):": "أدخل عناوين URL للويب المراد استخراجها (واحد لكل سطر):", "Enter a video URL to download its transcript.": "أدخل عنوان URL أو معرف الفيديو لتنزيل النص الخاص به.", "Expression API": "محلي\nإضافات\nماجستير", "ext_sum_with": "تلخيص مع:", "ext_sum_main_api": "واجهة برمجة التطبيقات الرئيسية", "ext_sum_current_summary": "الملخص الحالي:", "ext_sum_restore_previous": "استعادة ما تقدم", "ext_sum_memory_placeholder": "سيتم إنشاء الملخص هنا...", "Trigger a summary update right now.": "تلخيص الآن", "ext_sum_force_text": "تلخيص الآن", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "تعطيل التحديثات الموجزة التلقائية. أثناء الإيقاف المؤقت، يظل الملخص كما هو. لا يزال بإمكانك فرض التحديث بالضغط على زر تلخيص الآن (والذي يتوفر فقط مع واجهة برمجة التطبيقات الرئيسية).", "ext_sum_pause": "يوقف", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "حذف معلومات العالم وملاحظة المؤلف من النص المراد تلخيصه. يكون له تأثير فقط عند استخدام واجهة برمجة التطبيقات الرئيسية. تتجاهل واجهة برمجة تطبيقات Extras دائمًا شبكة WI/AN.", "ext_sum_no_wi_an": "لا يوجد واي فاي/AN", "ext_sum_settings_tip": "تحرير موجه التلخيص، وموضع الإدراج، وما إلى ذلك.", "ext_sum_settings": "إعدادات الملخص", "ext_sum_prompt_builder": "منشئ سريع", "ext_sum_prompt_builder_1_desc": "سيقوم الامتداد ببناء المطالبة الخاصة به باستخدام الرسائل التي لم يتم تلخيصها بعد. حظر الدردشة حتى يتم إنشاء الملخص.", "ext_sum_prompt_builder_1": "خام، حجب", "ext_sum_prompt_builder_2_desc": "سيقوم الامتداد ببناء المطالبة الخاصة به باستخدام الرسائل التي لم يتم تلخيصها بعد. لا يمنع الدردشة أثناء إنشاء الملخص. لا تدعم جميع الواجهات الخلفية هذا الوضع.", "ext_sum_prompt_builder_2": "خام وغير مانع", "ext_sum_prompt_builder_3_desc": "سوف يستخدم الامتداد منشئ المطالبة الرئيسي العادي ويضيف طلب الملخص إليه باعتباره رسالة النظام الأخيرة.", "ext_sum_prompt_builder_3": "كلاسيكي، مانع", "Summary Prompt": "ملخص موجه", "ext_sum_restore_default_prompt_tip": "استعادة المطالبة الافتراضية", "ext_sum_prompt_placeholder": "سيتم إرسال هذا المطالبة إلى الذكاء الاصطناعي لطلب إنشاء الملخص. سيتم حل {{words}} إلى معلمة \"عدد الكلمات\".", "ext_sum_target_length_1": "طول الملخص المستهدف", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "كلمات)", "ext_sum_api_response_length_1": "طول استجابة واجهة برمجة التطبيقات", "ext_sum_api_response_length_2": null, "ext_sum_api_response_length_3": "الرموز)", "ext_sum_0_default": "0 = افتراضي", "ext_sum_raw_max_msg": "[خام] الحد الأقصى للرسائل لكل طلب", "ext_sum_0_unlimited": "0 = غير محدود", "Update frequency": "تردد التحديث", "ext_sum_update_every_messages_1": "تحديث كل", "ext_sum_update_every_messages_2": "رسائل", "ext_sum_0_disable": "0 = تعطيل", "ext_sum_auto_adjust_desc": "حاول ضبط الفاصل الزمني تلقائيًا بناءً على مقاييس الدردشة.", "ext_sum_update_every_words_1": "تحديث كل", "ext_sum_update_every_words_2": "كلمات", "ext_sum_both_sliders": "إذا لم يكن كلا شريطي التمرير صفرًا، فسيقوم كلاهما بتشغيل تحديثات موجزة على فترات زمنية خاصة بهما.", "ext_sum_injection_template": "قالب الحقن", "ext_sum_memory_template_placeholder": "{{summary}} سيتوافق مع محتويات الملخص الحالي.", "ext_sum_injection_position": "موض<PERSON> الحقن", "How many messages before the current end of the chat.": "كم عدد الرسائل قبل النهاية الحالية للدردشة.", "ext_regex_title": "التعبيرات العادية", "ext_regex_new_global_script": "+ العالمية", "ext_regex_new_scoped_script": "+ نطاق", "ext_regex_import_script": "يستورد", "ext_regex_global_scripts": "البرامج النصية العالمية", "ext_regex_global_scripts_desc": "متاح لجميع الشخصيات. تم الحفظ في الإعدادات المحلية.", "ext_regex_scoped_scripts": "البرامج النصية ذات النطاق", "ext_regex_scoped_scripts_desc": "متاح فقط لهذه الشخصية. تم الحفظ في بيانات البطاقة.", "Regex Editor": "مح<PERSON><PERSON> ريجكس", "Test Mode": "وضع الاختبار", "ext_regex_desc": "Regex هي أداة للبحث عن/استبدال السلاسل باستخدام التعبيرات العادية. إذا كنت تريد معرفة المزيد، فانقر فوق علامة الاستفهام بجوار العنوان.", "Input": "مد<PERSON>ل", "ext_regex_test_input_placeholder": "أكتب هنا...", "Output": "انتاج |", "ext_regex_output_placeholder": "فارغ", "Script Name": "اسم البرنامج النصي", "Find Regex": "ابحث عن Regex", "Replace With": "استبدل ب", "ext_regex_replace_string_placeholder": "استخدم {{match}} لتضمين النص المطابق من Find Regex أو $1، $2، وما إلى ذلك لمجموعات الالتقاط.", "Trim Out": "تقليم خارج", "ext_regex_trim_placeholder": "يقوم عالميًا بقص أي أجزاء غير مرغوب فيها من تطابق التعبير العادي قبل الاستبدال. افصل كل عنصر بإدخال.", "ext_regex_affects": "يؤثر", "ext_regex_user_input": "إدخال المستخدم", "ext_regex_ai_output": "مخرجات الذكاء الاصطناعي", "Slash Commands": "أوامر القطع", "ext_regex_min_depth_desc": "عند تطبيقه على المطالبات أو العرض، فإنه يؤثر فقط على الرسائل التي لا يقل عمقها عن N من المستويات. 0 = الرسالة الأخيرة، 1 = الرسالة قبل الأخيرة، وما إلى ذلك. يحسب فقط إدخالات WI@Depth والرسائل القابلة للاستخدام، أي غير المخفية أو النظام.", "Min Depth": "الح<PERSON> الأدنى للعمق", "ext_regex_min_depth_placeholder": "<PERSON>ير محدود", "ext_regex_max_depth_desc": "عند تطبيقه على المطالبات أو العرض، فإنه يؤثر فقط على الرسائل التي لا يزيد عمقها عن مستويات N. 0 = الرسالة الأخيرة، 1 = الرسالة قبل الأخيرة، وما إلى ذلك. يحسب فقط إدخالات WI@Depth والرسائل القابلة للاستخدام، أي غير المخفية أو النظام.", "ext_regex_other_options": "خيا<PERSON><PERSON><PERSON> أخرى", "Only Format Display": "عرض التنسيق فقط", "ext_regex_only_format_prompt_desc": "لن يتغير سجل الدردشة، فقط المطالبة التي يتم إرسالها عند الطلب (عند الإنشاء).", "Only Format Prompt (?)": "موجه التنسيق فقط", "Run On Edit": "تش<PERSON>يل عند التحرير", "ext_regex_substitute_regex_desc": "استبدل {{وحدات الماكرو}} في Find Regex قبل تشغيله", "Substitute Regex": "استبدال Regex", "ext_regex_import_target": "استيراد إلى:", "ext_regex_disable_script": "تعطيل البرنامج النصي", "ext_regex_enable_script": "تمكين البرنامج النصي", "ext_regex_edit_script": "تحرير البرنامج النصي", "ext_regex_move_to_global": "الانتقال إلى البرامج النصية العالمية", "ext_regex_move_to_scoped": "الانتقال إلى البرامج النصية ذات النطاق", "ext_regex_export_script": "تصدير البرنامج النصي", "ext_regex_delete_script": "حذ<PERSON> الب<PERSON>نا<PERSON>ج النصي", "Trigger Stable Diffusion": "الزناد نشر مستقر", "sd_Yourself": "نفسك", "sd_Your_Face": "وجهك", "sd_Me": "أنا", "sd_The_Whole_Story": "القصة الكاملة", "sd_The_Last_Message": "الرسالة الاخيرة", "sd_Raw_Last_Message": "الرسالة الأخيرة الخام", "sd_Background": "خلفية", "Image Generation": "توليد الصور", "sd_refine_mode": "السماح بتحرير المطالبات يدويًا قبل إرسالها إلى واجهة برمجة تطبيقات الإنشاء", "sd_refine_mode_txt": "تعديل المطالبات قبل التوليد", "sd_interactive_mode": "قم بإنشاء الصور تلقائيًا عند إرسال رسائل مثل \"أرسل لي صورة قطة\".", "sd_interactive_mode_txt": "الوضع التفاعلي", "sd_multimodal_captioning": "استخدم التسميات التوضيحية متعددة الوسائط لإنشاء مطالبات لصور المستخدمين والشخصيات استنادًا إلى الصور الرمزية الخاصة بهم.", "sd_multimodal_captioning_txt": "استخدم التسميات التوضيحية متعددة الوسائط للصور", "sd_expand": "تمديد المطالبات تلقائيًا باستخدام نموذج إنشاء النص", "sd_expand_txt": "مطالبات التحسين التلقائي", "sd_snap": "طلبات إنشاء اللقطات باستخدام نسبة العرض إلى الارتفاع القسرية (الصور الشخصية والخلفيات) إلى أقرب دقة معروفة، مع محاولة الحفاظ على عدد البكسل المطلق (موصى به لـ SDXL).", "sd_snap_txt": "التقاط الدقة المعدلة تلقائيًا", "Source": "مصدر", "sd_auto_url": "مثال: {{auto_url}}", "Authentication (optional)": "المصادقة (اختياري)", "Example: username:password": "مثال: اسم المستخدم: كلمة المرور", "Important:": "مهم:", "sd_auto_auth_warning_1": "قم بتشغيل SD Web UI باستخدام", "sd_auto_auth_warning_2": "علَم! يجب أن يكون الخادم قابلاً للوصول من الجهاز المضيف SillyTavern.", "sd_drawthings_url": "مثال: {{drawthings_url}}", "sd_drawthings_auth_txt": "قم بتشغيل تطبيق DrawThings مع تمكين مفتاح HTTP API في واجهة المستخدم! يجب أن يكون الخادم قابلاً للوصول من الجهاز المضيف SillyTavern.", "sd_vlad_url": "مثال: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "يجب أن يكون الخادم قابلاً للوصول من الجهاز المضيف SillyTavern.", "Hint: Save an API key in AI Horde API settings to use it here.": "تلميح: احفظ مفتاح API في إعدادات AI Horde API لاستخدامه هنا.", "Allow NSFW images from Horde": "السماح بصور NSFW من Horde", "Sanitize prompts (recommended)": "مطالبات التعقيم (مستحسن)", "Automatically adjust generation parameters to ensure free image generations.": "قم بضبط معلمات الإنشاء تلقائيًا لضمان إنشاء صور مجانية.", "Avoid spending Anlas": "تجنب إن<PERSON><PERSON><PERSON>", "Opus tier": "(طبقة التأليف)", "View my Anlas": "عرض أنلاس الخاص بي", "These settings only apply to DALL-E 3": "تنطبق هذه الإعدادات فقط على DALL-E 3", "Image Style": "نمط الصورة", "Image Quality": "جودة الصورة", "Standard": "معيار", "HD": "عالية الدقة", "sd_comfy_url": "مثال: {{comfy_url}}", "Open workflow editor": "افتح محرر سير العمل", "Create new workflow": "إنشاء سير عمل جديد", "Delete workflow": "حذف سير العمل", "Enhance": "<PERSON><PERSON><PERSON><PERSON>", "Refine": "صقل", "Decrisper": "ديكريسبر", "Sampling steps": "خطوات أخذ العينات ()", "Width": "عرض ()", "Height": "ارتفاع ()", "Resolution": "دقة", "Model": "نموذج", "Sampling method": "طريقة أخذ العينات", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (ليست جميع أجهزة أخذ العينات مدعومة)", "SMEA versions of samplers are modified to perform better at high resolution.": "يتم تعديل إصدارات SMEA من أجهزة أخذ العينات لتقديم أداء أفضل بدقة عالية.", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "غالبًا ما تؤدي متغيرات DYN الخاصة بأجهزة أخذ العينات SMEA إلى إنتاج أكثر تنوعًا، ولكنها قد تفشل في الدقة العالية جدًا.", "DYN": "دين", "Scheduler": "مجدول", "Restore Faces": "استعادة الوجوه", "Hires. Fix": "يستأجر. يصلح", "Upscaler": "الراقي", "Upscale by": "الراقي بواسطة", "Denoising strength": "قوة إزالة الضوضاء", "Hires steps (2nd pass)": "خطوات الاستئجار (الممر الثاني)", "Preset for prompt prefix and negative prompt": "الإعداد المسبق للبادئة السريعة والموجه السلبي", "Style": "أسلوب", "Save style": "<PERSON><PERSON><PERSON> النمط", "Delete style": "<PERSON><PERSON><PERSON> النمط", "Common prompt prefix": "بادئة المطالبة الشائعة", "sd_prompt_prefix_placeholder": "استخدم {prompt} لتحديد المكان الذي سيتم فيه إدراج المطالبة التي تم إنشاؤها", "Negative common prompt prefix": "بادئة المطالبة المشتركة السلبية", "Character-specific prompt prefix": "بادئة المطالبة الخاصة بالحرف", "Won't be used in groups.": "لن يتم استخدامها في مجموعات.", "sd_character_prompt_placeholder": "أي خصائص تصف الشخصية المحددة حاليًا. ستتم إضافتها بعد بادئة المطالبة الشائعة.\nمثال: أنثى، عيون خضراء، شعر بني، قميص وردي", "Character-specific negative prompt prefix": "بادئة موجه سلبية خاصة بالحرف", "sd_character_negative_prompt_placeholder": "أي خصائص يجب ألا تظهر للشخصية المحددة. ستتم إضافتها بعد بادئة المطالبة العامة السلبية.\nمثال: المجوهرات والأحذية والنظارات", "Shareable": "قابلة للمشاركة", "Image Prompt Templates": "قوالب الصور السريعة", "Vectors Model Warning": "يوصى بتطهير المتجهات عند تغيير النموذج أثناء الدردشة. وإلا فإنه سيؤدي إلى نتائج دون المستوى.", "Translate files into English before processing": "ترجمة الملفات إلى اللغة الإنجليزية قبل المعالجة", "Manager Users": "ادارة المستخدمين", "New User": "مستخدم جديد", "Status:": "حالة:", "Created:": "مخلوق:", "Display Name:": "اسم العرض:", "User Handle:": "مقبض المستخدم:", "Password:": "كلمة المرور:", "Confirm Password:": "تأكيد كلمة المرور:", "This will create a new subfolder...": "سيؤدي هذا إلى إنشاء مجلد فرعي جديد في الدليل /data/ مع مقبض المستخدم كاسم المجلد.", "Current Password:": "كلمة السر الحالية:", "New Password:": "كلمة المرور الجديدة:", "Confirm New Password:": "تأكيد كلمة المرور الجديدة:", "Debug Warning": "الوظائف في هذه الفئة مخصصة للمستخدمين المتقدمين فقط. لا تنقر على أي شيء إذا لم تكن متأكدًا من العواقب.", "Execute": "ينفذ", "Are you sure you want to delete this user?": "هل أنت متأكد أنك تريد حذف هذا المستخدم؟", "Deleting:": "حذف:", "Also wipe user data.": "امسح بيانات المستخدم أيضًا.", "Warning:": "تحذير:", "This action is irreversible.": "هذا الإجراء لا رجعة فيه.", "Type the user's handle below to confirm:": "اكتب مقبض المستخدم أدناه للتأكيد:", "Import Characters": "استيراد الشخصيات", "Enter the URL of the content to import": "أدخل عنوان URL للمحتوى المراد استيراده", "Supported sources:": "المصادر المدعومة:", "char_import_1": "<PERSON>ر<PERSON> (رابط مبا<PERSON>ر أو معرف)", "char_import_example": "مثال:", "char_import_2": "<PERSON><PERSON> (رابط مباشر أو معرف)", "char_import_3": "حرف Janitor<PERSON> (رابط مباشر أو UUID)", "char_import_4": "حرف Pygmalion.chat (رابط مباشر أو UUID)", "char_import_5": "حرف AICharacterCards.com (رابط مباشر أو معرف)", "char_import_6": "رابط PNG المباشر (راجع", "char_import_7": "للمضيفين المسموح بهم)", "char_import_8": "شخصية RisuRealm (رابط مباشر)", "char_import_10": "شخصية Perchance (رابط مباشر أو UUID + .gz)", "Supports importing multiple characters.": "يدعم استيراد أحرف متعددة.", "Write each URL or ID into a new line.": "اكتب كل عنوان URL أو معرف في سطر جديد.", "Export for character": "تصدير للشخصية", "Export prompts for this character, including their order.": "تصدير المطالبات لهذه الشخصية، بما في ذلك ترتيبها.", "Export all": "تصدير الكل", "Export all your prompts to a file": "تصدير كافة المطالبات الخاصة بك إلى ملف", "Insert prompt": "إدراج تلميح", "Delete prompt": "<PERSON><PERSON><PERSON> التلميح", "Import a prompt list": "استيراد قائمة التلميحات", "Export this prompt list": "تصدير هذه القائمة من التلميحات", "Reset current character": "إعادة تعيين الشخصية الحالية", "New prompt": "تل<PERSON><PERSON><PERSON> جديد", "Prompts": "تلميحات", "Total Tokens:": "مجموع الرموز:", "prompt_manager_tokens": "الرموز", "Are you sure you want to reset your settings to factory defaults?": "هل أنت متأكد أنك تريد إعادة ضبط إعداداتك على إعدادات المصنع الافتراضية؟", "Don't forget to save a snapshot of your settings before proceeding.": "لا تنس حفظ لقطة من إعداداتك قبل المتابعة.", "Settings Snapshots": "لقطات الإعدادات", "Record a snapshot of your current settings.": "سجل لقطة لإعداداتك الحالية.", "Make a Snapshot": "قم بعمل لقطة", "Restore this snapshot": "استعادة هذه اللقطة", "Hi,": "أهلاً،", "To enable multi-account features, restart the SillyTavern server with": "لتمكين ميزات الحسابات المتعددة، أعد تشغيل خادم SillyTavern باستخدام", "set to true in the config.yaml file.": "تم ضبطه على true في ملف config.yaml.", "Account Info": "معلومات الحساب", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "لتغيير الصورة الرمزية للمستخدم، استخدم الأزرار أدناه أو حدد شخصية افتراضية في قائمة إدارة الشخصية.", "Set your custom avatar.": "قم بتعيين الصورة الرمزية المخصصة لك.", "Remove your custom avatar.": "قم بإزالة الصورة الرمزية المخصصة الخاصة بك.", "Handle:": "مقبض:", "This account is password protected.": "هذا الحساب محمي بكلمة مرور.", "This account is not password protected.": "هذا الحساب غير محمي بكلمة مرور.", "Account Actions": "إجراءات الحساب", "Change Password": "تغيير كلمة المرور", "Manage your settings snapshots.": "إدارة لقطات الإعدادات الخاصة بك.", "Download a complete backup of your user data.": "قم بتنزيل نسخة احتياطية كاملة لبيانات المستخدم الخاصة بك.", "Download Backup": "تحميل النسخ الاحتياطي", "Danger Zone": "منطقة الخطر", "Reset your settings to factory defaults.": "إعادة تعيين إعداداتك إلى إعدادات المصنع الافتراضية.", "Reset Settings": "اعادة الضبط", "Wipe all user data and reset your account to factory settings.": "امسح جميع بيانات المستخدم وأعد ضبط حسابك على إعدادات المصنع.", "Reset Everything": "إعادة ضبط كل شيء", "Reset Code:": "إعادة تعيين الرمز:", "Want to update?": "هل ترغب في التحديث؟", "How to start chatting?": "كيف تبدأ في المحادثة؟", "Click _space": "انقر", "and select a": "و<PERSON><PERSON><PERSON> أ", "Chat API": "واجهة برمجة تطبيقات الدردشة", "and pick a character.": "واختر شخصية.", "You can browse a list of bundled characters in the": "يمكنك تصفح قائمة الأحرف المجمعة في ملف", "Download Extensions & Assets": "تنزيل الإضافات والأصول", "menu within": "القائمة داخل", "Confused or lost?": "هل أنت مرتبك أو ضائع؟", "click these icons!": "انقر على هذه الأيقونات!", "in the chat bar": "في شريط الدردشة", "SillyTavern Documentation Site": "موقع وثائق SillyTavern", "Extras Installation Guide": "دليل تثبيت الإضافات", "Still have questions?": "هل ما زالت لديك اسئلة؟", "Join the SillyTavern Discord": "انضم إلى ديسكورد SillyTavern", "Post a GitHub issue": "نشر مشكلة على GitHub", "Contact the developers": "الاتصال بالمطورين"}