{"Favorite": "<PERSON><PERSON><PERSON>ch", "Tag": "<PERSON><PERSON> khóa", "Duplicate": "<PERSON><PERSON><PERSON> đôi", "Persona": "<PERSON><PERSON><PERSON> c<PERSON>ch", "Delete": "Xóa", "AI Response Configuration": "Setting <PERSON><PERSON><PERSON> h<PERSON> c<PERSON> AI", "AI Configuration panel will stay open": "Bảng Setting AI sẽ luôn được mở", "clickslidertips": "<PERSON><PERSON><PERSON><PERSON> vào thanh trư<PERSON>t để nhập giá trị bằng tay.", "MAD LAB MODE ON": "CHẾ ĐỘ PHÒNG THÍ NGHIỆM ĐIÊN RỒ ĐANG BẬT", "Documentation on sampling parameters": "<PERSON><PERSON><PERSON> li<PERSON>u về các tham số lấy mẫu", "kobldpresets": "Cài đặt trước Kobold", "guikoboldaisettings": "Cài đặt giao diện KoboldAI", "Update current preset": "<PERSON><PERSON><PERSON> nhật thiết lập hiện tại", "Save preset as": "<PERSON><PERSON><PERSON> cài đặt trước dưới dạng", "Import preset": "<PERSON><PERSON><PERSON><PERSON> preset", "Export preset": "<PERSON><PERSON><PERSON> preset", "Restore current preset": "<PERSON><PERSON><PERSON><PERSON> phục cài đặt hiện tại", "Delete the preset": "<PERSON><PERSON>a preset", "novelaipresets": "NovelAI presets", "Default": "Mặc định", "openaipresets": "OpenAI presets", "Text Completion presets": "Preset cho Text Completion", "AI Module": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Changes the style of the generated text.": "Thay đổi kiểu của văn bản đ<PERSON><PERSON><PERSON> t<PERSON>o.", "No Module": "<PERSON><PERSON><PERSON><PERSON> có mô-đun", "Instruct": "Instruct", "Prose Augmenter": "Gia tố văn xuôi", "Text Adventure": "<PERSON><PERSON><PERSON> l<PERSON> v<PERSON><PERSON> bản", "response legth(tokens)": "<PERSON><PERSON> dài của văn phản hồi (trong các token)", "Streaming": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> ti<PERSON>", "Streaming_desc": "<PERSON><PERSON>n thị văn phản hồi kí tự từng chút một khi nó được tạo.", "context size(tokens)": "<PERSON><PERSON>ch cỡ Context (token)", "unlocked": "Đã mở", "Only enable this if your model supports context sizes greater than 4096 tokens": "Chỉ bật t<PERSON>h năng này nếu AI model củ<PERSON> bạn hỗ trợ Context size lớn hơn 4096 token.", "Max prompt cost:": "Chi phí cho prompt tối đa:", "Display the response bit by bit as it is generated.": "<PERSON><PERSON><PERSON> thị phản hồi từ từ khi nó đư<PERSON>c tạo ra.", "When this is off, responses will be displayed all at once when they are complete.": "<PERSON><PERSON> tắt t<PERSON>h năng nà<PERSON>, các phản hồi sẽ được hiển thị cùng lúc khi chúng hoàn thành.", "Temperature": "<PERSON>hiệ<PERSON> (Temperature)", "rep.pen": "rep.pen", "Rep. Pen. Range.": "Rep. Pen. <PERSON>.", "Rep. Pen. Slope": "<PERSON><PERSON> <PERSON><PERSON>", "Rep. Pen. Freq.": "Rep. <PERSON><PERSON>.", "Rep. Pen. Presence": "Rep. <PERSON><PERSON>", "TFS": "TFS", "Phrase Repetition Penalty": "Phrase Repetition Penalty", "Off": "Tắt", "Very light": "Rất nhẹ", "Light": "Nhẹ", "Medium": "<PERSON>rung bình", "Aggressive": "<PERSON><PERSON><PERSON><PERSON>", "Very aggressive": "<PERSON><PERSON><PERSON> quyế<PERSON> đo<PERSON>", "Unlocked Context Size": "<PERSON><PERSON><PERSON> thước context đã mở khóa", "Unrestricted maximum value for the context slider": "Gi<PERSON> trị tối đa không giới hạn cho thanh trượt context", "Context Size (tokens)": "<PERSON><PERSON><PERSON> th<PERSON> context (token)", "Max Response Length (tokens)": "<PERSON><PERSON> dài phản hồi tối đa (token)", "Multiple swipes per generation": "<PERSON><PERSON><PERSON><PERSON> nhiều lần trong một lần tạo", "Enable OpenAI completion streaming": "Bật streaming của OpenAI", "Frequency Penalty": "Frequency Penalty", "Presence Penalty": "Presence Penalty", "Count Penalty": "Count Penalty", "Top K": "Top K", "Top P": "Top P", "Repetition Penalty": "Repetition Penalty", "Min P": "<PERSON>", "Top A": "Top A", "Quick Prompts Edit": "Chỉnh sửa n<PERSON>h prompt", "Main": "<PERSON><PERSON><PERSON>", "NSFW": "NSFW", "Jailbreak": "Bẻ khóa (Jailbreak)", "Utility Prompts": "<PERSON><PERSON><PERSON><PERSON> <PERSON>ch cho prompt", "Impersonation prompt": "<PERSON><PERSON><PERSON> danh văn tho<PERSON>i", "Restore default prompt": "<PERSON><PERSON><PERSON><PERSON> phục lại prompt mặc định", "Prompt that is used for Impersonation function": "Prompt đ<PERSON><PERSON><PERSON> sử dụng cho tính năng <PERSON> danh", "World Info Format Template": "Mẫu định dạng cho World Info", "Restore default format": "<PERSON><PERSON><PERSON><PERSON> phục định dạng mặc định", "Wraps activated World Info entries before inserting into the prompt.": "<PERSON><PERSON> <PERSON><PERSON><PERSON> c<PERSON>c mục World Info đã kích hoạt trước khi chèn vào Prompt.", "scenario_format_template_part_1": "Sử dụng", "scenario_format_template_part_2": "đ<PERSON> đ<PERSON>h dấu nơi chèn nội dung.", "Scenario Format Template": "Mẫu định dạng kịch bản", "Personality Format Template": "Mẫu cho định dạng <PERSON> cách", "Group Nudge Prompt Template": "Mẫu cho Prompt c<PERSON><PERSON>", "Sent at the end of the group chat history to force reply from a specific character.": "<PERSON><PERSON><PERSON><PERSON> chèn vào cuối lịch sử trò chuyện nhóm để buộc một nhân vật cụ thể phải trả đáp.", "New Chat": "<PERSON><PERSON><PERSON><PERSON> trò tru<PERSON>ện mới", "Restore new chat prompt": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> Prompt cho trò chuyện mới", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "Đ<PERSON>ợc đặt ở đầu lịch sử trò chuyện để cho biết một cuộc trò chuyện mới sắp bắt đầu.", "New Group Chat": "<PERSON><PERSON><PERSON> chuy<PERSON>n nhóm mới", "Restore new group chat prompt": "<PERSON><PERSON><PERSON><PERSON> phụ<PERSON> Prompt mặc định", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "Đặt ở đầu lịch sử trò chuyện để cho biết cuộc trò chuyện nhóm mới sắp bắt đầu.", "New Example Chat": "Mẫu trò chuyện mới", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "Đặt ở đầu Ví dụ hội thoại để cho biết rằng một cuộc trò chuyện mẫu mới sắp bắt đầu.", "Continue nudge": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> thúc gi<PERSON>c", "Set at the end of the chat history when the continue button is pressed.": "Đặt ở cuối lịch sử trò chuyện khi nhấn nút tiếp tục.", "Replace empty message": "<PERSON>hay thế văn bản trống", "Send this text instead of nothing when the text box is empty.": "<PERSON><PERSON><PERSON> văn bản này thay vì không gì khi ô văn bản đang bị trống.", "Seed": "<PERSON><PERSON><PERSON> (Seed)", "Set to get deterministic results. Use -1 for random seed.": "Đặt để có được kết quả xác định. Sử dụng -1 cho hạt giống (seed) ngẫu nhiên.", "Temperature controls the randomness in token selection": "Nhiệt độ (Temp) điều chỉnh sự ngẫu nhiên trong việc chọn token:\n- Nhiệt độ thấp (<1.0) dẫn đến văn bản dự đoán hơn, với ưu tiên cho các token có xác suất cao.\n- Nhiệt độ cao (>1.0) tăng tính sáng tạo và đa dạng của đầu ra, với nhiều cơ hội cho các token có xác suất thấp hơn.\nThiết lập giá trị 1.0 cho xác suất gốc.", "Top_K_desc": "Top K đặt một giá trị tối đa cho số lượng token hàng đầu có thể được chọn từ đó.", "Top_P_desc": "Top P (còn được gọi là mẫu hạt nhân) kết hợp tất cả các token hàng đầu cần thiết để đạt được một phần trăm nhất định.\n<PERSON><PERSON><PERSON> cách kh<PERSON>c, nế<PERSON> các token hàng đầu 2 đại diện cho 25%, và Top-P bằng 0.50, chỉ có hai token hàng đầu này được xem xét.\nThiết lập giá trị 1.0 để vô hiệu hóa.", "Typical P": "Typical P", "Typical_P_desc": "Mẫu P điển hình ưu tiên các token dựa trên sự sai lệch của chúng so với năng lượng cân bằng trung bình của tập hợp.\nC<PERSON><PERSON> token có xác suất tích lũy gần với ngưỡng được chỉ định (ví dụ: 0.5) được giữ lại, phân biệt chúng khỏi những token có thông tin trung bình.\nThiết lập giá trị 1.0 để vô hiệu hóa.", "Min_P_desc": "Min P đặt một xác suất tối thiểu cơ bản. <PERSON><PERSON> được tinh chỉnh dựa trên xác suất token hàng đầu.\nNếu xác suất của token hàng đầu là 80%, và Min P là 0.1, chỉ có token với xác suất cao hơn 8% được xem xét.\nThiết lập giá trị 0 để vô hiệu hóa.", "Top_A_desc": "Top A đặt một ngưỡng cho việc chọn token dựa trên bình phương của xác suất token cao nhất.\nNếu Top A là 0.2, và xác suất của token hàng đầu là 50%, các token có xác suất dưới 5% sẽ bị loại bỏ (0.2 * 0.5^2).\nThiết lập giá trị 0 để vô hiệu hóa.", "Tail_Free_Sampling_desc": "Mẫu không đuôi (TFS) tìm kiếm đuôi của token với xác suất nhỏ trong phân phối,\n thông qua phân tích tốc độ thay đổi xác suất token bằng cách sử dụng đạo hàm. <PERSON><PERSON><PERSON> token được giữ lại đến ngưỡng (ví dụ: 0.3), dựa trên đạo hàm hai lần thống nhất.\nMỗi khi tiến về 0, số lượng token bị loại bỏ tăng lên. Thiết lập giá trị 1.0 để vô hiệu hóa.", "rep.pen range": "Phạm vi trừ phạt tái phát", "Mirostat": "Mirostat", "Mode": "Mode", "Mirostat_Mode_desc": "Giá trị 0 sẽ vô hiệu hóa hoàn toàn Mirostat. 1 dành cho Mirostat 1.0 và 2 dành cho Mirostat 2.0", "Tau": "Tau", "Mirostat_Tau_desc": "<PERSON><PERSON><PERSON> so<PERSON>t sự thay đổi của đầu ra Mirostat", "Eta": "eta", "Mirostat_Eta_desc": "<PERSON><PERSON><PERSON> so<PERSON>t tốc độ học tập củ<PERSON> Mirostat", "Ban EOS Token": "Cấm E<PERSON>", "Ban_EOS_Token_desc": "Cấm mã thông báo Cuối chuỗi (EOS) với KoboldCpp (và có thể cả các mã thông báo khác có KoboldAI).\rTốt cho việc viế<PERSON> t<PERSON>, nhưng không nên dùng cho chế độ trò chuyện và hướng dẫn.", "GBNF Grammar": "GBNF Grammar", "Type in the desired custom grammar": "<PERSON><PERSON><PERSON><PERSON> vào cú pháp tùy chỉnh mong muốn", "Samplers Order": "<PERSON><PERSON><PERSON> tự Bộ lấy mẫu", "Samplers will be applied in a top-down order. Use with caution.": "<PERSON><PERSON><PERSON> bộ lấy mẫu sẽ được áp dụng theo thứ tự từ trên xuống. Sử dụng cẩn thận.", "Tail Free Sampling": "Mẫu <PERSON>hông đuôi", "Load koboldcpp order": "<PERSON><PERSON><PERSON> đơn hàng kob<PERSON>", "Preamble": "Preamble", "Use style tags to modify the writing style of the output.": "<PERSON><PERSON> dụng tags để sửa đổi kiểu viết của đầu ra.", "Banned Tokens": "C<PERSON>c token Bị ban", "Sequences you don't want to appear in the output. One per line.": "Các chuỗi bạn không muốn xuất hiện trong kết quả. Một dòng mỗi chuỗi.", "Logit Bias": "Logit Bias", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Helps to ban or reenforce the usage of certain words": "<PERSON><PERSON><PERSON><PERSON> cấm hoặc củng cố việc sử dụng một số từ", "CFG Scale": "Tỷ lệ CFG", "Negative Prompt": "<PERSON><PERSON>u hỏi tiêu cực", "Add text here that would make the AI generate things you don't want in your outputs.": "Thêm văn bản ở đây sẽ khiến trí tuệ nhân tạo tạo ra những điều bạn không muốn trong đầu ra của mình.", "Used if CFG Scale is unset globally, per chat or character": "Sử dụng nếu CFG Scale không được thiết lập toàn cầu, mỗi cuộc trò chuyện hoặc mỗi ký tự.", "Mirostat Tau": "Mirostat Tau", "Mirostat LR": "Mirostat LR", "Min Length": "Min Length", "Top K Sampling": "Mẫu Top K", "Nucleus Sampling": "Mẫu hạt nhân", "Top A Sampling": "Mẫu Top A", "CFG": "CFG", "Neutralize Samplers": "<PERSON><PERSON><PERSON> trung lập các mẫu", "Set all samplers to their neutral/disabled state.": "Đặt tất cả các mẫu vào trạng thái trung lập/tắt.", "Sampler Select": "<PERSON><PERSON><PERSON> mẫu", "Customize displayed samplers or add custom samplers.": "Tùy chỉnh các bộ lấy mẫu được hiển thị hoặc thêm các bộ lấy mẫu tùy chỉnh.", "Epsilon Cutoff": "Cắt Epsilon", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "Cắt ngắn Epsilon đặt một ngưỡng xác suất dưới đó các token sẽ không được lựa chọn để mẫu.\nTrong đơn vị 1e-4; gi<PERSON> trị thích hợp là 3.\n<PERSON><PERSON><PERSON><PERSON> lập 0 để vô hiệu hóa.", "Eta Cutoff": "Cắt Eta", "Eta_Cutoff_desc": "Ngưỡng Eta là tham số chính của kỹ thuật Mẫu Eta đặc biệt.&#13;Trong đơn vị của 1e-4; một giá trị hợp lý là 3.&#13;Đặt thành 0 để tắt.&#13;<PERSON><PERSON> bài báo Truncation Sampling as Language Model Desmoothing củ<PERSON> Hewitt và cộng sự (2022) để biết chi tiết.", "rep.pen decay": "Đ<PERSON>i di<PERSON>n bút phân rã", "Encoder Rep. Pen.": "Bút phạt mã hóa", "No Repeat Ngram Size": "<PERSON><PERSON><PERSON> th<PERSON> không lặp lại", "Skew": "<PERSON><PERSON><PERSON><PERSON>", "Max Tokens Second": "Số lượng Mã thông báo Tối đa / Giây", "Smooth Sampling": "<PERSON><PERSON><PERSON> mẫu mượt mà", "Smooth_Sampling_desc": "<PERSON> phép bạn sử dụng các phép biến đổi bậc hai/khối để điều chỉnh phân bố. G<PERSON><PERSON> trị Hệ số làm mịn thấp hơn sẽ sáng tạ<PERSON> h<PERSON>, thường nằm trong khoảng 0,2-0,3 là điểm ngọt (giả sử đường cong = 1). Gi<PERSON> trị Đường cong làm mịn cao hơn sẽ làm cho đường cong dốc hơn, điều này sẽ trừng phạt mạnh mẽ hơn các lựa chọn có xác suất thấp. Đường cong 1.0 tương đương với việc chỉ sử dụng Smoothing Factor.", "Smoothing Factor": "<PERSON><PERSON> số làm mịn", "Smoothing Curve": "<PERSON><PERSON><PERSON> mịn đ<PERSON> cong", "DRY_Repetition_Penalty_desc": "DRY phạt các mã thông báo sẽ kéo dài phần cuối của đầu vào thành một chuỗi đã xảy ra trước đó trong đầu vào. Đặt hệ số nhân thành 0 để tắt.", "DRY Repetition Penalty": "Hình phạt lặp lại DRY", "DRY_Multiplier_desc": "Đặt thành giá trị > 0 để bật DRY. <PERSON><PERSON><PERSON> so<PERSON>t mức độ hình phạt đối với các chuỗi hình phạt ngắn nhất.", "Multiplier": "Số nhân", "DRY_Base_desc": "<PERSON><PERSON><PERSON> soát tốc độ tăng của hình phạt khi tăng độ dài chuỗi.", "Base": "Base", "DRY_Allowed_Length_desc": "Tr<PERSON><PERSON> tự dài nhất có thể được lặp lại mà không bị phạt.", "Allowed Length": "Allowed Length", "Penalty Range": "Penalty Range", "DRY_Sequence_Breakers_desc": "<PERSON><PERSON><PERSON> mã thông báo mà việc khớp trình tự không được tiếp tục. Được chỉ định dưới dạng danh sách các chuỗi được trích dẫn được phân tách bằng dấu phẩy.", "Sequence Breakers": "<PERSON><PERSON> ngắt trình tự", "JSON-serialized array of strings.": "<PERSON><PERSON>ng chuỗi được tuần tự hóa JSON.", "Dynamic Temperature": "Dynamic Temperature", "Scale Temperature dynamically per token, based on the variation of probabilities": "Nhiệt độ tỷ lệ động cho mỗi token, dựa trên sự biến đổi của xác suất.", "Minimum Temp": "Nhiệt độ Tối thiểu", "Maximum Temp": "Nhiệt độ Tối đa", "Exponent": "Exponent", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (chế độ=1 chỉ dành cho llama.cpp)", "Mirostat_desc": "Mirostat là một bộ điều chỉnh nhiệt cho sự phức tạp của đầu ra.", "Mirostat Mode": "Chế độ Mirostat", "Variability parameter for Mirostat outputs": "<PERSON>ham số biến đổi cho đầu ra của Mirostat.", "Mirostat Eta": "Mirostat Eta", "Learning rate of Mirostat": "Learning rate of Mirostat.", "Beam search": "Beam search", "Helpful tip coming soon.": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>u <PERSON>ch sắp ra mắt.", "Number of Beams": "Số L<PERSON>", "Length Penalty": "Length Penalty", "Early Stopping": "Dừng <PERSON>ớm", "Contrastive search": "Contrastive search", "Penalty Alpha": "Penalty Alpha", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "<PERSON><PERSON> mạnh của thuật ngữ điều chỉnh Tìm kiếm Trái ngược. Đặt thành 0 để vô hiệu hóa CS.", "Do Sample": "<PERSON><PERSON><PERSON>", "Add BOS Token": "Thêm BOS Token", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "Thêm bos_token vào đầu câu hỏi. Vô hiệu hóa điều này có thể làm cho các câu trả lời sáng tạo hơn", "Ban the eos_token. This forces the model to never end the generation prematurely": "Cấm eos_token. <PERSON><PERSON><PERSON><PERSON> này buộc mô hình không bao giờ kết thúc quá trình sinh ra trư<PERSON><PERSON> khi cần thiết", "Ignore EOS Token": "Bỏ qua mã thông báo EOS", "Ignore the EOS Token even if it generates.": "Bỏ qua EOS Token ngay cả khi nó được tạo ra.", "Skip Special Tokens": "Bỏ qua <PERSON>c <PERSON> Đặc biệt", "Temperature Last": "Temperature Last", "Temperature_Last_desc": "Sử dụng bộ lấy Temperature cuối cùng. Thường là hợp lý.\n<PERSON>hi bật: M<PERSON><PERSON> nhóm các token tiềm năng được chọn trước tiên, sau đó nhiệt độ được áp dụng để hiệu chỉnh xác suất tương đối của chúng (k<PERSON> thuật, logits).\nKhi vô hiệu hóa: Nhiệt độ được áp dụng trước tiên để hiệu chỉnh xác suất tương đối của từng token, sau đó một nhóm các token tiềm năng được chọn từ đó.\nVô hiệu hóa nhiệt độ cuối cùng.", "Speculative Ngram": "Speculative Ngram", "Use a different speculative decoding method without a draft model": "Sử dụng phương pháp giải mã suy đoán khác mà không cần mô hình phác thảo.\rƯu tiên sử dụng mô hình dự thảo. <PERSON><PERSON> đầu cơ không hiệu quả bằng.", "Spaces Between Special Tokens": "<PERSON><PERSON><PERSON><PERSON> cách gi<PERSON>a các mã thông báo đặc biệt", "LLaMA / Mistral / Yi models only": "Chỉ áp dụng cho các mô hình LLaMA / Mistral / Yi. H<PERSON><PERSON> chắc chắn chọn bộ phân tích đúng trước.\nChuỗi phải không xuất hiện trong kết quả.\nMỗi dòng chỉ một chuỗi. Văn bản hoặc [nhận diện của token].\nNhiều token bắt đầu bằng dấu cách. Sử dụng bộ đếm token nếu bạn không chắc chắn.", "Example: some text [42, 69, 1337]": "V<PERSON> dụ:\nmột số văn bản\n[42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "Hướng dẫn không cần Bộ phân loại. Mẹo hữu ích hơn sẽ được cập nhật sớm.", "Scale": "Tỷ lệ", "JSON Schema": "JSON Schema", "Type in the desired JSON schema": "Nhập JSON schema mong muốn", "Grammar String": "Chuỗi ngữ pháp", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF hoặc EBNF, t<PERSON><PERSON> th<PERSON><PERSON> vào backend đang sử dụng. Nếu bạn đang sử dụng cái này, bạn nên biết cái nào.", "Top P & Min P": "Top P & Min P", "Load default order": "<PERSON><PERSON><PERSON> thứ tự mặc định", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "chỉ llama.cpp. <PERSON><PERSON><PERSON> định thứ tự lấy mẫu. <PERSON><PERSON><PERSON> chế độ Mirostat khác 0, thứ tự lấy mẫu sẽ bị bỏ qua.", "Sampler Priority": "Ưu tiên sampler", "Ooba only. Determines the order of samplers.": "Chỉ Ooba. <PERSON><PERSON><PERSON> thứ tự của samplers.", "Character Names Behavior": "<PERSON><PERSON>i đặt tên nhân vật", "Helps the model to associate messages with characters.": "<PERSON><PERSON><PERSON><PERSON> mô hình liên kết tin nhắn với các ký tự.", "None": "K<PERSON>ô<PERSON>", "character_names_default": "Ngoại trừ các nhóm và cá tính trong quá khứ. <PERSON><PERSON><PERSON>, h<PERSON><PERSON> đảm bảo bạn cung cấp tên trong Prompt.", "Don't add character names.": "<PERSON><PERSON><PERSON><PERSON> thêm tên nhân vật.", "Completion": "<PERSON><PERSON><PERSON> th<PERSON>", "character_names_completion": "<PERSON><PERSON> dụng hạn chế: chỉ chữ và số Latinh và dấu gạch dưới. Không hoạt động với tất cả các nguồn, đặc biệt là: Claude, MistralAI, Google.", "Add character names to completion objects.": "Thêm tên nhân vật vào completion objects.", "Message Content": "<PERSON><PERSON><PERSON> dung tin nhắn", "Prepend character names to message contents.": "<PERSON>hê<PERSON> tên ký tự vào nội dung tin nhắn.", "Continue Postfix": "<PERSON><PERSON><PERSON><PERSON>", "The next chunk of the continued message will be appended using this as a separator.": "<PERSON>o<PERSON>n tiếp theo của tin nhắn tiếp theo sẽ được thêm vào bằng cách sử dụng đoạn này làm dấu phân cách.", "Space": "<PERSON><PERSON><PERSON><PERSON> gian", "Newline": "Dòng mới", "Double Newline": "Dòng mới kép", "Wrap user messages in quotes before sending": "Cho tin nhắn của người dùng trong dấu ngoặc kép trước khi gửi", "Wrap in Quotes": "Cho vào trong dấu ngoặc", "Wrap entire user message in quotes before sending.": "<PERSON>àn bộ tin nhắn của người dùng trong dấu ngoặc kép trước khi gửi", "Leave off if you use quotes manually for speech.": "Bỏ qua nếu bạn sử dụng dấu ngoặc kép cho lời nói.", "Continue prefill": "<PERSON><PERSON><PERSON><PERSON>c Pre<PERSON>", "Continue sends the last message as assistant role instead of system message with instruction.": "<PERSON><PERSON><PERSON><PERSON> tục gửi tin nhắn cuối cùng dưới dạng Assistant thay vì Prompt hệ thống với Instruction.", "Squash system messages": "<PERSON><PERSON> tin nhắn văn bản hệ thống", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "<PERSON><PERSON><PERSON> hợp các tin nhắn hệ thống liên tiếp thành một (loại bỏ các đoạn hội thoại mẫu). <PERSON><PERSON> thể cải thiện tính nhất quán cho một số model.", "Enable function calling": "<PERSON><PERSON> dụng t<PERSON>h năng gọi hàm (function calling)", "Send inline images": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh n<PERSON>i bộ", "image_inlining_hint_1": "<PERSON><PERSON><PERSON> hình ảnh theo Prompt nếu kiểu máy hỗ trợ.\n                                                <PERSON><PERSON> dụng", "image_inlining_hint_2": "hành động đối với bất kỳ tin nhắn nào hoặc", "image_inlining_hint_3": "menu để đ<PERSON>h kèm tệp hình <PERSON>nh vào cuộc trò chuy<PERSON>n.", "Inline Image Quality": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh nội tuyến", "openai_inline_image_quality_auto": "Tự động", "openai_inline_image_quality_low": "<PERSON><PERSON><PERSON><PERSON>", "openai_inline_image_quality_high": "<PERSON>", "Use AI21 Tokenizer": "Sử bộ <PERSON> (bộ tách từ) của AI21", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "<PERSON><PERSON> dụng bộ tách từ thích hợp cho các Model của Jurassic, hi<PERSON><PERSON> qu<PERSON> hơn GPT.", "Use Google Tokenizer": "Sử Tokenizer của Google", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "<PERSON><PERSON> dụng bộ tách từ phù hợp cho các model c<PERSON><PERSON> Google thông qua API. <PERSON><PERSON> lý prompt chậ<PERSON> hơn, nh<PERSON>ng đếm token ch<PERSON>h xác hơn.", "Use system prompt": "<PERSON><PERSON> dụng Prompt h<PERSON> thống", "(Gemini 1.5 Pro/Flash only)": "(Chỉ cho Gemini 1.5 Pro/Flash)", "Merges_all_system_messages_desc_1": "<PERSON><PERSON><PERSON> nhất tất cả các tin nhắn văn bản hệ thống cho đến tin nhắn đầu tiên có vai trò không thuộc hệ thống và gửi chúng theo cách", "Merges_all_system_messages_desc_2": "c<PERSON><PERSON> đồng.", "Assistant Prefill": "Prefill trợ lý", "Start Claude's answer with...": "<PERSON> tr<PERSON> lời bắt đầu bằng...", "Assistant Impersonation Prefill": "Prefill cho mạo danh trợ lý", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "<PERSON><PERSON><PERSON> yêu cầu hệ thống cho các model đ<PERSON><PERSON><PERSON> hỗ trợ. <PERSON><PERSON>u bị vô hiệu hóa, tin nhắn của người dùng sẽ được thêm vào đầu yêu cầu.", "User first message": "<PERSON> nhắn đầu tiên của người dùng", "Restore User first message": "<PERSON><PERSON><PERSON><PERSON> phục tin nhắn đầu tiên của người dùng", "Human message": "Prompt, hướng dẫn của human, v.v.\n<PERSON><PERSON><PERSON><PERSON> thêm gì khi trống, tứ<PERSON> là yêu cầu Prompt mới với vai trò 'người dùng'.", "New preset": "Preset mới", "Delete preset": "<PERSON><PERSON>a preset", "View / Edit bias preset": "Xem / Chỉnh sửa cài đặt thiên vị", "Add bias entry": "<PERSON><PERSON><PERSON><PERSON> mục thiên vị", "Most tokens have a leading space.": "<PERSON><PERSON><PERSON> hết các token đều có khoảng trống ở đầu.", "API Connections": "Kết nối API", "Text Completion": "Text Completion", "Chat Completion": "Chat Completion", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i thông tin nh<PERSON>y cảm cho Horde.", "Review the Privacy statement": "<PERSON><PERSON> l<PERSON> s<PERSON>ch b<PERSON><PERSON> mật", "Register a Horde account for faster queue times": "<PERSON><PERSON><PERSON> ký tài khoản Horde để có thời gian xếp hàng ngắn hơn", "Learn how to contribute your idle GPU cycles to the Horde": "<PERSON><PERSON><PERSON> hi<PERSON>u cách đóng góp công sức GPU của bạn cho Horde", "Adjust context size to worker capabilities": "Điều chỉnh kích thước context cho phù hợp với khả năng của công nhân", "Adjust response length to worker capabilities": "<PERSON><PERSON>ều chỉnh độ dài phản hồi cho phù hợp với khả năng của công nhân", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "<PERSON><PERSON> thể giúp đỡ với các phản hồi không tốt bằng cách xếp hàng chỉ các nhân viên được phê duyệt. <PERSON><PERSON> thể làm chậm lại thời gian phản hồi.", "Trusted workers only": "Chỉ các nhân viên đư<PERSON>c tin cậy", "API key": "Key API", "Get it here:": "<PERSON><PERSON><PERSON><PERSON> nó tại đây:", "Register": "<PERSON><PERSON><PERSON> ký", "View my Kudos": "<PERSON><PERSON> c<PERSON>a tui", "Enter": "<PERSON><PERSON><PERSON><PERSON>", "to use anonymous mode.": "để sử dụng chế độ <PERSON>n danh.", "Clear your API key": "Xóa key API của tui", "For privacy reasons, your API key will be hidden after you reload the page.": "<PERSON>ì lý do bả<PERSON> mậ<PERSON>, khóa API của bạn sẽ bị ẩn sau khi bạn tải lại trang.", "Models": "Model", "Refresh models": "<PERSON><PERSON><PERSON> mới model", "-- Horde models not loaded --": "-- <PERSON><PERSON><PERSON> mô hình <PERSON> không đ<PERSON><PERSON><PERSON> tải --", "Not connected...": "<PERSON><PERSON><PERSON><PERSON> kết n<PERSON>i...", "API url": "URL API", "Example: http://127.0.0.1:5000/api ": "<PERSON><PERSON> dụ: http://127.0.0.1:5000/api", "Connect": "<PERSON><PERSON><PERSON>", "Cancel": "Hủy bỏ", "Novel API key": "Key API NovelAI", "Get your NovelAI API Key": "Nhận khóa API NovelAI của bạn", "Enter it in the box below": "<PERSON><PERSON><PERSON><PERSON> nó vào ô dưới đây", "Novel AI Model": "Model Novel AI", "No connection...": "<PERSON><PERSON><PERSON><PERSON> có kết nối...", "API Type": "Loại API", "Default (completions compatible)": "Mặc định [OpenAI/tương thích hoàn thành: oobabooga, LM Studio, v.v.]", "TogetherAI API Key": "Key API TogetherAI", "TogetherAI Model": "Model TogetherAI", "-- Connect to the API --": "-- Kết nối với API --", "OpenRouter API Key": "Key API của OpenRouter", "Click Authorize below or get the key from": "<PERSON><PERSON><PERSON><PERSON> và<PERSON> động dưới đây hoặc lấy Key từ", "View Remaining Credits": "<PERSON><PERSON> số dư còn lại", "OpenRouter Model": "Model OpenRouter", "Model Providers": "Nhà cung cấp model", "InfermaticAI API Key": "Key API InfermaticAI", "InfermaticAI Model": "Model InfermaticAI", "DreamGen API key": "Key API DreamGen", "DreamGen Model": "Model DreamGen", "Mancer API key": "Key API của Mancer", "Mancer Model": "<PERSON>", "Make sure you run it with": "<PERSON><PERSON><PERSON> b<PERSON>o bạn chạy nó với nó", "flag": "cờ", "API key (optional)": "Key API (t<PERSON><PERSON>)", "Server url": "URL máy chủ", "Electron Hub API Key": "Key API Electron Hub", "Electron Hub Model": "Model Electron Hub", "Example: http://127.0.0.1:5000": "<PERSON><PERSON> dụ: http://127.0.0.1:5000", "Custom model (optional)": "Model tùy chỉnh (tù<PERSON> ch<PERSON>n)", "vllm-project/vllm": "vllm-project/vllm (Chế độ trình bao bọc API OpenAI)", "vLLM API key": "Key API vLLM", "Example: http://127.0.0.1:8000": "<PERSON><PERSON> dụ: http://127.0.0.1:8000", "vLLM Model": "Model vLLM", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (Chế độ đóng gói cho <PERSON> diện lập trình ứng dụng OpenAI)", "Aphrodite API key": "Key API Aphrodite", "Aphrodite Model": "Moddel cho Aphrodite", "ggerganov/llama.cpp": "ggerganov/llama.cpp", "Example: http://127.0.0.1:8080": "<PERSON><PERSON> dụ: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "<PERSON><PERSON> dụ: http://127.0.0.1:11434", "Ollama Model": "<PERSON>", "Download": "<PERSON><PERSON><PERSON>", "Tabby API key": "Key API Tabby", "koboldcpp API key (optional)": "Key API koboldcpp (t<PERSON><PERSON> ch<PERSON>)", "Example: http://127.0.0.1:5001": "<PERSON><PERSON> dụ: http://127.0.0.1:5001", "Cho phép": "Ủy quyền", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "Nhận mã thông báo API OpenRouter củ<PERSON> bạn bằng cách sử dụng luồng OAuth. Bạn sẽ được chuyển hướng đến openrouter.ai", "Bypass status check": "Bỏ qua check trạng thái", "Chat Completion Source": "<PERSON><PERSON><PERSON><PERSON> cho <PERSON>t Completion", "Reverse Proxy": "Proxy", "Proxy Presets": "Preset proxy", "Saved addresses and passwords.": "Link và mật khẩu đã lưu.", "Save Proxy": "<PERSON><PERSON><PERSON> proxy", "Delete Proxy": "Xóa proxy", "Proxy Name": "<PERSON><PERSON><PERSON> c<PERSON> Proxy", "This will show up as your saved preset.": "<PERSON><PERSON><PERSON>u này sẽ hiển thị dưới dạng cài đặt trước đã lưu của bạn.", "Proxy Server URL": "Link URL proxy", "Alternative server URL (leave empty to use the default value).": "URL máy chủ thay thế (để trống để sử dụng giá trị mặc định).", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "Xóa Key API OAI thực của bạn khỏi bảng API TRƯỚC khi nhập bất kỳ điều gì vào hộp này", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "<PERSON><PERSON>g tôi không thể cung cấp hỗ trợ cho các vấn đề gặp phải khi sử dụng proxy OpenAI không chính thức", "Doesn't work? Try adding": "<PERSON>hông hoạt động? <PERSON><PERSON><PERSON> thử thêm", "at the end!": " ở cuối cùng!", "Proxy Password": "<PERSON><PERSON><PERSON> k<PERSON> cho proxy", "Will be used as a password for the proxy instead of API key.": "Sẽ được sử dụng làm mật khẩu cho proxy thay vì key API.", "Peek a password": "<PERSON><PERSON> m<PERSON>", "OpenAI API key": "OpenAI API key", "View API Usage Metrics": "Xem stats xài API", "Follow": "<PERSON>", "these directions": "nh<PERSON>ng hướng dẫn này", "to get your OpenAI API key.": "để lấy Key API của OpenAI.", "Use Proxy password field instead. This input will be ignored.": "Dùng ô \"<PERSON><PERSON><PERSON> khẩ<PERSON> proxy\" đi. Ô này không dùng được đâu.", "OpenAI Model": "Model OpenAI", "Bypass API status check": "Bỏ qua kiểm tra trạng thái API", "Show External models (provided by API)": "Hiện các model ng<PERSON><PERSON><PERSON> vào (do API cung cấp)", "Get your key from": "<PERSON><PERSON><PERSON> của bạn từ", "Anthropic's developer console": "developer console của Anthropic", "Claude Model": "Model <PERSON>", "Window AI Model": "Model Window AI", "Model Order": "Sắp xếp model OpenRouter", "Alphabetically": "<PERSON> thứ tự bảng chữ cái", "Price": "<PERSON><PERSON><PERSON> (rẻ nhất)", "Context Size": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> c<PERSON>nh", "Group by vendors": "<PERSON>hó<PERSON> theo nhà cung cấp", "Group by vendors Description": "<PERSON>ế<PERSON> các mô hình OpenAI vào một nhóm, các mô hình Anthropic vào một nhóm khác, v.v. <PERSON><PERSON> thể kết hợp với việc sắp xếp.", "Allow fallback routes": "<PERSON> phép các tuyến đường phụ", "Allow fallback routes Description": "<PERSON><PERSON> thay thế tự động nếu mô hình đư<PERSON><PERSON> chọn không thể đáp ứng yêu cầu của bạn.", "AI21 API Key": "AI21 API Key", "AI21 Model": "Model của AI21", "Google AI Studio API Key": "Google AI Studio API Key", "Google Model": "Model của Google", "MistralAI API Key": "MistralAI API Key", "MistralAI Model": "Model của Mi<PERSON>lAI", "Groq API Key": "Groq API Key", "Groq Model": "<PERSON> c<PERSON><PERSON>", "Perplexity API Key": "Perplexity API Key", "Perplexity Model": "Model của Perplexity", "Cohere API Key": "Cohere API Key", "Cohere Model": "<PERSON> c<PERSON><PERSON>", "Custom Endpoint (Base URL)": "Đường link custom (Base URL)", "Custom API Key": "Key API tùy chỉnh", "Available Models": "<PERSON><PERSON><PERSON> <PERSON> hi<PERSON><PERSON> c<PERSON>", "Prompt Post-Processing": "<PERSON><PERSON> lý hậu k<PERSON> Prompt", "Applies additional processing to the prompt before sending it to the API.": "<PERSON><PERSON> dụng x<PERSON> lý bổ sung cho Prompt trư<PERSON>c khi gửi nó tới API.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "<PERSON>ác minh kết nối API của bạn bằng cách gửi một tin nhắn kiểm tra ngắn. Hãy nhớ rằng bạn sẽ được ghi nhận về điều đó!", "Test Message": "Test tin nhắn", "Auto-connect to Last Server": "Tự động kết nối với link trư<PERSON>c đó", "Missing key": "❌ Thi<PERSON>u Key", "Key saved": "✔️ <PERSON><PERSON> lưu Key", "View hidden API keys": "Xem các Key API ẩn", "AI Response Formatting": "<PERSON><PERSON><PERSON> dạng <PERSON> hồ<PERSON> c<PERSON>", "Advanced Formatting": "<PERSON><PERSON><PERSON> dạng <PERSON> cao", "Context Template": "Mẫu context", "Auto-select this preset for Instruct Mode": "Tự động chọn thiết lập này cho Chế độ Hướng dẫn", "Story String": "Chuỗi <PERSON><PERSON><PERSON>n", "Example Separator": "<PERSON><PERSON><PERSON> c<PERSON> Mẫu", "Chat Start": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON>", "Add Chat Start and Example Separator to a list of stopping strings.": "<PERSON><PERSON><PERSON><PERSON>t đầu trò chuyện và Dấu phân cách ví dụ vào danh sách các chuỗi dừng.", "Use as Stop Strings": "<PERSON><PERSON> dụng như chuỗi dừng", "Allow Jailbreak": "Cho phép bẻ khóa", "Context Order": "<PERSON><PERSON><PERSON> tự b<PERSON><PERSON> cảnh", "Summary": "<PERSON><PERSON><PERSON> tóm tắt", "Author's Note": "<PERSON><PERSON> chú của tác giả", "Example Dialogues": "<PERSON><PERSON><PERSON> thoại mẫu", "Hint": "Gợi ý:", "In-Chat Position not affected": "Th<PERSON> tự Tóm tắt và Ghi chú của tác giả chỉ bị ảnh hưởng khi chúng không được đặt vị trí Trong trò chuyện.", "Instruct Mode": "Chế độ Hướng dẫn", "Enabled": "<PERSON><PERSON> bật", "instruct_bind_to_context": "<PERSON><PERSON><PERSON> đượ<PERSON> bật, các mẫu context sẽ được tự động chọn dựa trên tên mẫu hướng dẫn đã chọn hoặc theo sở thích.", "Bind to Context": "Buộc vào Context", "Presets": "Cài đặt Preset", "Auto-select this preset on API connection": "Tự động chọn thiết lập này khi kết nối API", "Activation Regex": "Kích hoạt Regex", "Wrap Sequences with Newline": "Bao gói Các chuỗi với Dòng mới", "Replace Macro in Sequences": "Thay thế Macro trong chuỗi", "Skip Example Dialogues Formatting": "Bỏ qua Định dạng Đoạn hội thoại Mẫu", "Include Names": "<PERSON><PERSON>", "Force for Groups and Personas": "<PERSON><PERSON> b<PERSON> cho Nhóm và Nhân vật", "System Prompt": "Prompt c<PERSON><PERSON> h<PERSON> thống", "Instruct Mode Sequences": "Các chuỗi chế độ hướng dẫn", "System Prompt Wrapping": "<PERSON><PERSON><PERSON> Prompt h<PERSON> thống", "Inserted before a System prompt.": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON> t<PERSON> Prompt <PERSON><PERSON> thống.", "System Prompt Prefix": "<PERSON><PERSON><PERSON><PERSON> đầu ngữ cho Prompt hệ thống", "Inserted after a System prompt.": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>n sau Prompt c<PERSON><PERSON> <PERSON> thống.", "System Prompt Suffix": "<PERSON><PERSON><PERSON> tố nhắc nhở hệ thống", "Chat Messages Wrapping": "<PERSON><PERSON><PERSON> tin nhắn trò chuyện", "Inserted before a User message and as a last prompt line when impersonating.": "<PERSON><PERSON><PERSON><PERSON> chèn trư<PERSON>c thông báo <PERSON>ời dùng và làm dòng nhắc nhở cuối cùng khi mạo danh.", "User Message Prefix": "Tiền tố tin nhắn người dùng", "Inserted after a User message.": "<PERSON><PERSON><PERSON><PERSON> chèn sau tin nhắn của Ng<PERSON>ời dùng.", "User Message Suffix": "<PERSON><PERSON><PERSON> tố tin nhắn của người dùng", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "<PERSON><PERSON><PERSON><PERSON> chèn trước tin nhắn Trợ lý và làm dòng nhắc nhở cuối cùng khi tạo câu trả lời AI.", "Assistant Message Prefix": "Ti<PERSON>n tố tin nhắn trợ lý", "Inserted after an Assistant message.": "<PERSON><PERSON><PERSON><PERSON> chèn sau tin nhắn của Trợ lý.", "Assistant Message Suffix": "<PERSON><PERSON><PERSON> tố tin nhắn trợ lý", "Inserted before a System (added by slash commands or extensions) message.": "<PERSON><PERSON><PERSON><PERSON> chèn trước thông báo <PERSON> thống (đ<PERSON><PERSON><PERSON> thêm bằng lệnh gạch chéo hoặc phần mở rộng).", "System Message Prefix": "<PERSON><PERSON><PERSON><PERSON> tố tin nhắn hệ thống", "Inserted after a System message.": "<PERSON><PERSON><PERSON><PERSON> chèn sau thông báo <PERSON> thống.", "System Message Suffix": "<PERSON><PERSON><PERSON> tố tin nhắn hệ thống", "If enabled, System Sequences will be the same as User Sequences.": "<PERSON><PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON><PERSON> tự hệ thống sẽ giống như Trình tự của người dùng.", "System same as User": "<PERSON><PERSON> thống giống n<PERSON>ời dùng", "Misc. Sequences": "<PERSON>h tinh. tr<PERSON>nh tự", "Inserted before the first Assistant's message.": "<PERSON><PERSON><PERSON><PERSON> chèn trước tin nhắn của Trợ lý đầu tiên.", "First Assistant Prefix": "Tiền tố trợ lý đầu tiên", "instruct_last_output_sequence": "<PERSON><PERSON><PERSON><PERSON> chèn trước tin nhắn cuối cùng của Trợ lý hoặc dưới dạng dòng nhắc nhở cuối cùng khi tạo phản hồi AI (ngoại trừ vai trò trung lập/hệ thống).", "Last Assistant Prefix": "Tiền tố trợ lý cuối cùng", "Will be inserted as a last prompt line when using system/neutral generation.": "Sẽ được chèn dưới dạng dòng nhắc cuối cùng khi sử dụng hệ thống/thế hệ trung lập.", "System Instruction Prefix": "<PERSON><PERSON><PERSON><PERSON> tố hướng dẫn hệ thống", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "<PERSON><PERSON><PERSON> một chuỗi dừng được tạo ra, mọ<PERSON> thứ trước đó sẽ bị xóa khỏi đầu ra (bao gồ<PERSON>).", "Stop Sequence": "Chuỗi Dừng", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "Sẽ được chèn vào đầu lịch sử trò chuyện nếu nó không bắt đầu bằng tin nhắn Người dùng.", "User Filler Message": "<PERSON><PERSON><PERSON><PERSON> báo điền của người dùng", "Context Formatting": "Định dạng context", "(Saved to Context Template)": "(<PERSON><PERSON> lưu trong Mẫu context)", "Always add character's name to prompt": "<PERSON><PERSON><PERSON> thêm tên nhân vật vào điều khiển", "Generate only one line per request": "Chỉ tạo một dòng cho mỗi yêu cầu", "Trim Incomplete Sentences": "<PERSON><PERSON><PERSON> các câu không hoàn chỉnh", "Include Newline": "<PERSON><PERSON> gồm dòng mới", "Misc. Settings": "<PERSON><PERSON><PERSON> cài đặt khác", "Collapse Consecutive Newlines": "<PERSON><PERSON> gọn các dòng mới liên tiếp", "Trim spaces": "Cắt k<PERSON>ng trắng", "Tokenizer": "<PERSON><PERSON><PERSON> cụ tách từ", "Token Padding": "<PERSON><PERSON><PERSON>", "Start Reply With": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> hồ<PERSON> Bằng", "AI reply prefix": "Tiền tố trả lời AI", "Show reply prefix in chat": "<PERSON><PERSON><PERSON> thị tiền tố phản hồi trong chat", "Non-markdown strings": "Chuỗi không Markdown", "separate with commas w/o space between": "phân tách bằng dấu phẩy không có khoảng trắng giữa", "Custom Stopping Strings": "Chuỗi dừng tùy chỉnh", "JSON serialized array of strings": "Mảng chuỗi được tuần tự hóa JSON", "Replace Macro in Stop Strings": "Thay thế Macro trong Chuỗi Dừng Tùy chỉnh", "Auto-Continue": "<PERSON><PERSON> động <PERSON> tục", "Allow for Chat Completion APIs": "Cho phép các API hoàn thành Trò chuyện", "Target length (tokens)": "<PERSON><PERSON> dài mục tiê<PERSON> (token)", "World Info": "World Info", "Locked = World Editor will stay open": "<PERSON><PERSON><PERSON><PERSON> khóa = Trì<PERSON> chỉnh sửa Thế giới sẽ được mở", "Worlds/Lorebooks": "<PERSON><PERSON><PERSON> giới", "Active World(s) for all chats": "<PERSON><PERSON><PERSON> giới Ho<PERSON>t động cho tất cả các cuộc trò chuyện", "-- World Info not found --": "-- <PERSON><PERSON><PERSON><PERSON> tìm thấy World Info --", "Global World Info/Lorebook activation settings": "Cài đặt kích hoạt Thông tin thế giới World Info/Lorebook", "Click to expand": "<PERSON>ấm để mở rộng", "Scan Depth": "<PERSON><PERSON> s<PERSON>u quét", "Context %": "Bối cảnh %", "Budget Cap": "<PERSON><PERSON> s<PERSON>ch tối đa", "(0 = disabled)": "(0 = v<PERSON> hi<PERSON> hóa)", "Scan chronologically until reached min entries or token budget.": "<PERSON>uét theo trình tự thời gian cho đến khi đạt được mục nhập hoặc ngân sách mã thông báo tối thiểu.", "Min Activations": "<PERSON><PERSON><PERSON> ho<PERSON>t tối thiểu", "Max Depth": "<PERSON><PERSON> sâu tối đa", "(0 = unlimited, use budget)": "(0 = kh<PERSON>ng g<PERSON><PERSON><PERSON> hạn, sử dụng ngân sách)", "Insertion Strategy": "<PERSON><PERSON><PERSON>n", "Sorted Evenly": "<PERSON><PERSON><PERSON> x<PERSON>p đều", "Character Lore First": "<PERSON><PERSON> liệu nhân vật đầu tiên", "Global Lore First": "Sử liệu toàn cầu đầu tiên", "Entries can activate other entries by mentioning their keywords": "<PERSON><PERSON><PERSON> mục có thể kích hoạt các mục khác bằng cách đề cập đến từ khóa của họ", "Recursive Scan": "<PERSON><PERSON><PERSON> đệ quy", "Lookup for the entry keys in the context will respect the case": "T<PERSON>m kiếm các khóa mục trong context sẽ tôn trọng trường hợp", "Case Sensitive": "<PERSON>ân biệt chữ hoa chữ thường", "If the entry key consists of only one word, it would not be matched as part of other words": "<PERSON><PERSON><PERSON> kh<PERSON>a mục bao gồm chỉ một từ, nó sẽ không được kết hợp như một phần của các từ khác", "Match Whole Words": "<PERSON><PERSON><PERSON><PERSON> toàn bộ từ", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "Chỉ những mục có số lượng kết quả khớp chính nhiều nhất mới được chọn để lọc Nhóm <PERSON> gồm", "Use Group Scoring": "<PERSON><PERSON> dụng t<PERSON>h điểm nhóm", "Alert if your world info is greater than the allocated budget.": "<PERSON><PERSON><PERSON> báo nếu thông tin thế giới của bạn vư<PERSON>t quá ngân sách đư<PERSON>c phân bổ.", "Alert On Overflow": "<PERSON><PERSON><PERSON> báo khi tràn", "New": "<PERSON><PERSON><PERSON>", "or": "hoặc", "--- Pick to Edit ---": "--- <PERSON><PERSON><PERSON> để Chỉnh sửa ---", "Rename World Info": "Đổi tên World Info", "Open all Entries": "Mở tất cả các <PERSON>", "Close all Entries": "<PERSON><PERSON><PERSON> tất cả các <PERSON>", "New Entry": "<PERSON><PERSON><PERSON>", "Fill empty Memo/Titles with Keywords": "<PERSON><PERSON><PERSON><PERSON> và<PERSON>/Tiêu đề trống với từ khóa", "Import World Info": "Nhập World Info", "Export World Info": "Xuất World Info", "Duplicate World Info": "Nhân đôi World Info", "Delete World Info": "Xóa World Info", "Search...": "<PERSON><PERSON><PERSON> k<PERSON>...", "Search": "<PERSON><PERSON><PERSON>", "Priority": "Ưu tiên", "Custom": "<PERSON><PERSON><PERSON> chỉnh", "Title A-Z": "Ti<PERSON>u đề A-Z", "Title Z-A": "Ti<PERSON><PERSON> đề Z-A", "Tokens ↗": "Token ↗", "Tokens ↘": "Token ↘", "Depth ↗": "<PERSON><PERSON> sâu ↗", "Depth ↘": "<PERSON><PERSON> sâu ↘", "Order ↗": "<PERSON><PERSON><PERSON> tự ↗", "Order ↘": "<PERSON><PERSON><PERSON> tự ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "Kích ho<PERSON>t% ↗", "Trigger% ↘": "Kích ho<PERSON>t% ↘", "Refresh": "<PERSON><PERSON><PERSON>", "User Settings": "Cài đặt người dùng", "Simple": "Đơn g<PERSON>n", "Advanced": "<PERSON><PERSON><PERSON> cao", "UI Language": "ngôn ng<PERSON>", "Account": "<PERSON><PERSON><PERSON>", "Admin Panel": "bảng quản trị", "Logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "Search Settings": "<PERSON><PERSON>i đặt Tìm kiếm", "UI Theme": "<PERSON><PERSON> đề G<PERSON>o di<PERSON>n Ng<PERSON><PERSON> dùng", "Import a theme file": "<PERSON><PERSON><PERSON><PERSON> một tệp chủ đề", "Export a theme file": "<PERSON><PERSON><PERSON> một tệp chủ đề", "Delete a theme": "<PERSON><PERSON><PERSON> một chủ đề", "Update a theme file": "<PERSON><PERSON><PERSON> nhật một tập tin chủ đề", "Save as a new theme": "<PERSON><PERSON><PERSON> dưới dạng chủ đề mới", "Avatar Style:": "<PERSON><PERSON><PERSON> hình đạ<PERSON>n", "Circle": "<PERSON><PERSON><PERSON> tròn", "Square": "<PERSON><PERSON><PERSON> vu<PERSON>ng", "Rectangle": "<PERSON><PERSON><PERSON> chữ nhật", "Chat Style:": "<PERSON><PERSON><PERSON>:", "Flat": "Phẳng\nbong bóng\n<PERSON>ài liệu", "Bubbles": "<PERSON><PERSON> bóng", "Document": "<PERSON><PERSON><PERSON> l<PERSON>", "Specify colors for your theme.": "Chỉ định màu sắc cho chủ đề của bạn.", "Theme Colors": "<PERSON><PERSON><PERSON> chủ đề", "Main Text": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "Italics Text": "<PERSON><PERSON><PERSON> b<PERSON>", "Underlined Text": "<PERSON><PERSON><PERSON> b<PERSON>ân", "Quote Text": "<PERSON><PERSON><PERSON> bản <PERSON> dẫn", "Shadow Color": "<PERSON><PERSON><PERSON>", "Chat Background": "<PERSON><PERSON><PERSON>", "UI Background": "<PERSON><PERSON><PERSON>n U<PERSON>", "UI Border": "Viền UI", "User Message Blur Tint": "<PERSON><PERSON><PERSON> s<PERSON>c làm mờ <PERSON> nhắn của <PERSON> dùng", "AI Message Blur Tint": "<PERSON><PERSON><PERSON> s<PERSON>c làm mờ Tin nhắn của Trí tuệ N<PERSON>ân tạo", "Chat Width": "<PERSON><PERSON><PERSON> r<PERSON>ng chat", "Width of the main chat window in % of screen width": "Chiều rộng của cửa sổ trò chuyện chính tính bằng % chiều rộng màn hình", "Font Scale": "Tỷ l<PERSON>", "Font size": "Cỡ chữ", "Blur Strength": "<PERSON><PERSON><PERSON> độ mạnh mờ", "Blur strength on UI panels.": "<PERSON><PERSON> mờ trên bảng <PERSON>.", "Text Shadow Width": "<PERSON><PERSON> rộng bóng văn bản", "Strength of the text shadows": "<PERSON><PERSON> mạnh của bóng văn bản", "Disables animations and transitions": "<PERSON><PERSON><PERSON> c<PERSON>c hi<PERSON>u <PERSON>ng và chuyển động", "Reduced Motion": "<PERSON><PERSON><PERSON><PERSON> chuyển động", "removes blur from window backgrounds": "Loại bỏ mờ từ hình nền cửa sổ", "No Blur Effect": "<PERSON><PERSON><PERSON><PERSON> có hiệu <PERSON>ng mờ", "Remove text shadow effect": "Loại bỏ hiệu ứng bóng đèn văn bản", "No Text Shadows": "<PERSON><PERSON><PERSON><PERSON> có bóng văn bản", "Reduce chat height, and put a static sprite behind the chat window": "<PERSON><PERSON><PERSON><PERSON> chiều cao của cuộc trò chuyện và đặt một sprite tĩnh phía sau cửa sổ trò chuyện", "Waifu Mode": "<PERSON><PERSON> đ<PERSON>", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "<PERSON><PERSON>n hiển thị danh sách đầy đủ các mục context Hành động Tin nhắn cho các tin nhắn trò chuy<PERSON>, thay vì <PERSON>n chúng sau '...'", "Auto-Expand Message Actions": "Tự động mở rộng Hành động Tin nhắn", "Alternative UI for numeric sampling parameters with fewer steps": "<PERSON><PERSON><PERSON> người dùng thay thế cho các tham số mẫu số học với ít bước hơn", "Zen Sliders": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Entirely unrestrict all numeric sampling parameters": "<PERSON><PERSON><PERSON> toàn không hạn chế tất cả các tham số mẫu số học", "Mad Lab Mode": "<PERSON><PERSON> độ <PERSON>òng thí nghiệm điên", "Time the AI's message generation, and show the duration in the chat log": "<PERSON><PERSON> thời gian tạo ra tin nhắn của trí tuệ nhân tạo và hiển thị thời lượng trong nhật ký trò chuyện", "Message Timer": "Hẹn gi<PERSON>n", "Show a timestamp for each message in the chat log": "Hiển thị dấu thời gian cho mỗi tin nhắn trong nhật ký trò chuyện", "Chat Timestamps": "<PERSON><PERSON><PERSON> th<PERSON>i g<PERSON>", "Show an icon for the API that generated the message": "<PERSON><PERSON>n thị biểu tượng cho API đã tạo ra tin nhắn", "Model Icon": "<PERSON><PERSON><PERSON><PERSON>", "Show sequential message numbers in the chat log": "<PERSON><PERSON>n thị số tin nhắn tuần tự trong nhật ký trò chuyện", "Message IDs": "ID Tin <PERSON>", "Hide avatars in chat messages.": "Ẩn hình đại diện trong tin nhắn trò chuyện.", "Hide Chat Avatars": "Ẩn hình đại diện trò chuyện", "Show the number of tokens in each message in the chat log": "Hiển thị số lượng token trong mỗi tin nhắn trong nhật ký trò chuyện", "Show Message Token Count": "<PERSON><PERSON><PERSON> thị <PERSON>ố lượng Token trong Tin nhắn", "Single-row message input area. Mobile only, no effect on PC": "<PERSON><PERSON> vự<PERSON> nhập tin nhắn một hàng. Chỉ dành cho điện thoại di động, không ảnh hưởng đến PC", "Compact Input Area (Mobile)": "<PERSON><PERSON> <PERSON><PERSON><PERSON> liệu <PERSON> nhẹ (Di động)", "In the Character Management panel, show quick selection buttons for favorited characters": "Trong bảng <PERSON> lý <PERSON> vật, hiển thị các nút lựa chọn nhanh cho các nhân vật đ<PERSON><PERSON><PERSON> yêu thích", "Characters Hotswap": "<PERSON>hay đ<PERSON> n<PERSON> vật", "Enable magnification for zoomed avatar display.": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> năng phóng to để hiển thị hình đại diện đ<PERSON><PERSON><PERSON> phóng to.", "Avatar Hover Magnification": "<PERSON><PERSON><PERSON> to hình đại diện khi di chuột", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "<PERSON>ật hiệu <PERSON>ng phóng to khi di chuột khi bạn hiển thị hình đại diện đư<PERSON><PERSON> phóng to sau khi nhấp vào hình ảnh của hình đại diện trong cuộc trò chuyện.", "Show tagged character folders in the character list": "<PERSON><PERSON>n thị các thư mục nhân vật đư<PERSON><PERSON> gắn thẻ trong danh sách nhân vật", "Tags as Folders": "Tags n<PERSON><PERSON> <PERSON> mụ<PERSON>", "Tags_as_Folders_desc": "Thay đổi gần đây: Thẻ phải được đánh dấu là thư mục trong menu Quản lý thẻ để hiển thị như vậy. Nhấp vào đây để đưa nó lên.", "Character Handling": "Xử lý Nhân vật", "If set in the advanced character definitions, this field will be displayed in the characters list.": "<PERSON><PERSON><PERSON> đư<PERSON><PERSON> thiết lập trong các định nghĩa nhân vật nâng cao, trường này sẽ được hiển thị trong danh sách nhân vật.", "Char List Subheader": "<PERSON><PERSON><PERSON><PERSON> đề phụ danh s<PERSON>ch <PERSON>r", "Character Version": "<PERSON><PERSON><PERSON> b<PERSON> vật", "Created by": "<PERSON><PERSON><PERSON><PERSON> tạo bởi", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "Sử dụng kết hợp mờ, và tìm kiếm nhân vật trong danh sách bằng tất cả các trường dữ liệu, không chỉ bằng một phần của tên", "Advanced Character Search": "<PERSON><PERSON><PERSON> k<PERSON> vật <PERSON> cao", "If checked and the character card contains a prompt override (System Prompt), use that instead": "<PERSON><PERSON><PERSON> đư<PERSON><PERSON> kiểm tra và thẻ nhân vật chứa một lệnh ghi đè (<PERSON><PERSON><PERSON> hệ thống), hãy sử dụng thay vào đó", "Prefer Character Card Prompt": "Ưu tiên <PERSON> ý từ Card", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "<PERSON><PERSON><PERSON> đư<PERSON><PERSON> kiểm tra và thẻ nhân vật chứa một lệnh phá vỡ giam giữ (Hướng dẫn Lịch sử Bài viết), hãy sử dụng thay vào đó", "Prefer Character Card Jailbreak": "Ưu tiên <PERSON>lbreak từ Card", "never_resize_avatars_tooltip": "<PERSON><PERSON><PERSON><PERSON> cắt xén và thay đổi kích thước hình <PERSON>nh ký tự đã nhập. <PERSON><PERSON> tắ<PERSON>, h<PERSON><PERSON> cắt/thay đổi kích thước thành 512x768.", "Never resize avatars": "<PERSON><PERSON><PERSON><PERSON> bao giờ thay đổi kích thư<PERSON><PERSON> hình đại di<PERSON>n", "Show actual file names on the disk, in the characters list display only": "<PERSON><PERSON><PERSON> thị tên tệp thực tế trên đĩa, chỉ trong danh sách nhân vật", "Show avatar filenames": "<PERSON><PERSON><PERSON> thị tên tệp hình đại di<PERSON>n", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "<PERSON>h<PERSON>c nhập các thẻ thẻ nhúng trên thẻ nhân vật nhập khẩu. <PERSON><PERSON><PERSON> khô<PERSON>, các thẻ nhúng sẽ bị bỏ qua", "Import Card Tags": "Nhập Tags Thẻ", "Hide character definitions from the editor panel behind a spoiler button": "Ẩn định nghĩa nhân vật từ bảng chỉnh sửa sau một nút spoil", "Spoiler Free Mode": "Ẩn spoiler", "Miscellaneous": "<PERSON><PERSON> tinh", "Reload and redraw the currently open chat": "Tải lại và vẽ lại cuộc trò chuyện đang mở hiện tại", "Reload Chat": "<PERSON><PERSON><PERSON>", "Debug Menu": "<PERSON><PERSON>", "Smooth Streaming": "Streaming mư<PERSON>t", "Experimental feature. May not work for all backends.": "<PERSON><PERSON><PERSON> năng thực nghiệm. <PERSON><PERSON> thể không hoạt động cho tất cả các <PERSON>ends.", "Slow": "<PERSON><PERSON><PERSON>", "Fast": "<PERSON><PERSON><PERSON>", "Play a sound when a message generation finishes": "<PERSON><PERSON><PERSON> ra âm thanh thông báo khi tin nhắn từ Char kết thúc", "Message Sound": "<PERSON><PERSON>", "Only play a sound when ST's browser tab is unfocused": "Chỉ phát âm thanh khi tab trình duyệt ST không được tập trung", "Background Sound Only": "Chỉ <PERSON><PERSON>", "Reduce the formatting requirements on API URLs": "<PERSON><PERSON><PERSON><PERSON> yêu cầu định dạng trên URL của API", "Relaxed API URLS": "URL API thư giãn", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "Hỏi để nhập World Info/Lorebook cho mỗi nhân vật mới có sẵn lorebook nhúng. Nếu không được kiểm tra, thay vào đó sẽ hiển thị một tin nhắn tóm tắt", "Lorebook Import Dialog": "Nhập ví dụ lời thoại của Lorebook", "Restore unsaved user input on page refresh": "<PERSON><PERSON><PERSON><PERSON> phục đầu vào của người dùng chưa đư<PERSON><PERSON> lưu khi refresh trang", "Restore User Input": "Khô<PERSON> phục input của người dùng", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "<PERSON> phép di chuyển một số thành phần giao diện người dùng bằng cách kéo chúng. Chỉ dành cho PC, không có tác dụng trên điền thoại", "Movable UI Panels": "<PERSON><PERSON>ng G<PERSON> người dùng Có thể di chuyển", "MovingUI preset. Predefined/saved draggable positions": "Cài đặt trước MovingUI. Vị trí có thể kéo trước/saved", "MUI Preset": "Cài đặt trước MUI:", "Save movingUI changes to a new file": "<PERSON><PERSON><PERSON> c<PERSON>c thay đổi của movingUI vào một tập tin mới", "Reset MovingUI panel sizes/locations.": "Đặt lại kích thước/vị trí bảng điều khiển MovingUI.", "Apply a custom CSS style to all of the ST GUI": "<PERSON><PERSON> dụng một kiểu CSS tùy chỉnh cho tất cả GUI của ST", "Custom CSS": "CSS Tùy chỉnh", "Expand the editor": "Mở rộng trình chỉnh sửa", "Chat/Message Handling": "<PERSON><PERSON> lý <PERSON>/<PERSON>", "# Messages to Load": "# tin nhắn. đ<PERSON> tải", "The number of chat history messages to load before pagination.": "<PERSON><PERSON> lượng tin nhắn lịch sử trò chuyện cần tải trước khi phân trang.", "(0 = All)": "(0 = <PERSON><PERSON>t cả)", "Streaming FPS": "FPS của Streaming", "Update speed of streamed text.": "<PERSON><PERSON><PERSON> đ<PERSON> của streaming văn bản.", "Example Messages Behavior": "<PERSON>à<PERSON> vi <PERSON> Mẫu", "Gradual push-out": "<PERSON><PERSON><PERSON> ra dần", "Always include examples": "<PERSON><PERSON><PERSON> bao gồm các ví dụ", "Never include examples": "<PERSON><PERSON><PERSON><PERSON> bao giờ bao gồm các ví dụ", "Send on Enter": "<PERSON><PERSON><PERSON> khi nh<PERSON>n <PERSON>", "Disabled": "<PERSON><PERSON>", "Automatic (PC)": "<PERSON><PERSON> đ<PERSON> (Trên PC)", "Press Send to continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>i để tiếp tục", "Show a button in the input area to ask the AI to continue (extend) its last message": "Hiển thị một nút trong khu vực nhập để yêu cầu trí tuệ nhân tạo tiếp tục (mở rộng) tin nhắn cuối cùng của nó", "Quick 'Continue' button": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "<PERSON><PERSON><PERSON> thị nút mũi tên trên tin nhắn cuối cùng trong trò chuyện để tạo ra các phản hồi trí tuệ nhân tạo thay thế. <PERSON><PERSON> PC và điện thoại di động", "Swipes": "<PERSON><PERSON><PERSON><PERSON>", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "Cho phép sử dụng cử chỉ vuốt trên tin nhắn cuối cùng trong trò chuyện để kích hoạt việc tạo ra vuốt. Chỉ dành cho di động, không ảnh hưởng đến PC", "Gestures": "Cử chỉ", "Auto-load Last Chat": "Tự động tải Đ<PERSON>ạn trò chuyện Cuối cùng", "Auto-scroll Chat": "Tự động cuộn Tr<PERSON> chuy<PERSON>n", "Save edits to messages without confirmation as you type": "<PERSON><PERSON><PERSON> các chỉnh sửa vào các tin nhắn mà không cần xác nhận khi bạn gõ", "Auto-save Message Edits": "Tự động lưu <PERSON>", "Confirm message deletion": "<PERSON><PERSON><PERSON> nhận x<PERSON>a tin nhắn", "Auto-fix Markdown": "Tự động sửa <PERSON>", "Disallow embedded media from other domains in chat messages": "<PERSON>h<PERSON><PERSON> cho phép nhúng phương tiện từ các miền khác vào tin nhắn trò chuyện.", "Forbid External Media": "<PERSON><PERSON><PERSON> tiện Ng<PERSON> tuyến", "Allow {{char}}: in bot messages": "Cho phép {{char}}: trong các <PERSON>", "Allow {{user}}: in bot messages": "Cho phép {{user}}: trong c<PERSON>c <PERSON>", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "Bỏ qua mã hóa và ký tự trong văn bản tin nhắn, cho phép một tập con của đánh dấu HTML cũng như Markdown", "Show tags in responses": "Hiển thị thẻ trong các phản hồi", "Allow AI messages in groups to contain lines spoken by other group members": "Cho phép các tin nhắn của trí tuệ nhân tạo trong các nhóm chứa các dòng được nói bởi các thành viên khác trong nhóm", "Relax message trim in Groups": "Giảm nhẹ việc cắt đoạn tin nhắn trong Nhóm", "Log prompts to console": "<PERSON><PERSON> <PERSON><PERSON>n các Prompt vào bảng đi<PERSON>u khiển", "Requests logprobs from the API for the Token Probabilities feature": "<PERSON><PERSON><PERSON> c<PERSON>u logprobs từ API cho bảng <PERSON><PERSON>", "Request token probabilities": "<PERSON><PERSON><PERSON> c<PERSON>u bảng x<PERSON>c <PERSON> token", "Automatically reject and re-generate AI message based on configurable criteria": "Tự động từ chối và tạo lại tin nhắn của trí tuệ nhân tạo dựa trên các tiêu chí có thể cấu hình", "Auto-swipe": "Tự động vuốt", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "Bật chức năng tự động vuốt. <PERSON><PERSON><PERSON> cài đặt trong phần này chỉ có tác dụng khi tự động vuốt đượ<PERSON> bật", "Minimum generated message length": "<PERSON><PERSON> dài tối thiểu của tin nhắn đ<PERSON><PERSON><PERSON> tạo", "If the generated message is shorter than these many characters, trigger an auto-swipe": "<PERSON><PERSON><PERSON> tin nhắn đư<PERSON><PERSON> tạo ra ngắn hơn điều này, k<PERSON>ch hoạt tự động vuốt", "Blacklisted words": "Từ trong danh sách đen", "words you dont want generated separated by comma ','": "các từ bạn không muốn được tạo ra được phân tách bằng dấu phẩy ','", "Blacklisted word count to swipe": "Số từ trong danh sách đen để vuốt", "Minimum number of blacklisted words detected to trigger an auto-swipe": "S<PERSON> lượng tối thiểu từ trong danh sách đen phát hiện để kích hoạt chức năng tự động vuốt", "AutoComplete Settings": "Cài đặt Tự động Hoàn tất", "Automatically hide details": "Tự động <PERSON>n chi tiết", "Determines how entries are found for autocomplete.": "<PERSON><PERSON><PERSON> định cách tìm thấy các mục để tự động hoàn thành.", "Autocomplete Matching": "<PERSON><PERSON><PERSON>", "Starts with": "<PERSON><PERSON><PERSON> đ<PERSON>u với", "Includes": "<PERSON><PERSON>", "Fuzzy": "Fuzzy", "Sets the style of the autocomplete.": "Đặt kiểu tự động hoàn thành.", "Autocomplete Style": "<PERSON><PERSON> c<PERSON>ch", "Follow Theme": "<PERSON><PERSON>ng bộ giao <PERSON>n", "Dark": "<PERSON><PERSON><PERSON> t<PERSON>i", "Sets the font size of the autocomplete.": "Đặt kích thước phông chữ của tự động hoàn thành.", "Sets the width of the autocomplete.": "Đặt chiều rộng của tự động hoàn thành.", "Autocomplete Width": "<PERSON><PERSON><PERSON> r<PERSON>", "chat input box": "<PERSON><PERSON><PERSON> nh<PERSON>p trò ch<PERSON>", "entire chat width": "toàn bộ chiều rộng trò chuyện", "full window width": "chi<PERSON><PERSON> rộng toàn bộ cửa sổ", "STscript Settings": "Cài đặt STscript", "Sets default flags for the STscript parser.": "Đặt cờ mặc định cho trình phân tích cú pháp STscript.", "Parser Flags": "Cờ phân tích cú pháp", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "<PERSON><PERSON><PERSON><PERSON> sang l<PERSON><PERSON> thoát chặt chẽ hơn, cho ph<PERSON><PERSON> tho<PERSON>t tất cả các ký tự phân cách bằng dấu gạch chéo ngược và dấu gạch chéo ngược cũng được thoát.", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "Thay thế tất cả macro {{getvar::}} và {{getglobalvar::}} bằng các biến trong phạm vi để tránh thay thế macro kép.", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "<PERSON><PERSON> đ<PERSON>i <PERSON>n", "Filter": "<PERSON><PERSON> lọc", "Automatically select a background based on the chat context": "Tự động chọn một nền dựa trên context trò chuyện", "Auto-select": "<PERSON><PERSON> động chọn", "System Backgrounds": "<PERSON><PERSON><PERSON> thống", "Chat Backgrounds": "<PERSON><PERSON><PERSON>", "bg_chat_hint_1": "<PERSON><PERSON><PERSON> nền trò chuyện được tạo bằng", "bg_chat_hint_2": "tiện ích sẽ xuất hiện ở đây.", "Extensions": "<PERSON><PERSON><PERSON><PERSON>", "Notify on extension updates": "<PERSON><PERSON><PERSON><PERSON> bá<PERSON> về các bản cập nhật của tiện ích", "Manage extensions": "<PERSON><PERSON><PERSON><PERSON> lý tiện <PERSON>ch", "Import Extension From Git Repo": "<PERSON><PERSON><PERSON><PERSON> tiện ích từ Git <PERSON>o", "Install extension": "Cài đặt tiện ích", "Extras API:": "<PERSON> b<PERSON> sung:", "Auto-connect": "Tự động kết nối", "Extras API URL": "URL API b<PERSON> sung", "Extras API key (optional)": "Key <PERSON> (t<PERSON><PERSON> ch<PERSON>n)", "Persona Management": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> c<PERSON>ch", "How do I use this?": "Tôi sử dụng cái này như thế nào?", "Click for stats!": "<PERSON><PERSON><PERSON><PERSON> để xem thống kê!", "Usage Stats": "Thống kê sử dụng", "Backup your personas to a file": "<PERSON><PERSON> l<PERSON><PERSON> nhân cách của bạn vào một tập tin", "Backup": "<PERSON><PERSON> lư<PERSON>", "Restore your personas from a file": "<PERSON><PERSON><PERSON><PERSON> phục nhân cách của bạn từ một tập tin", "Restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "Create a dummy persona": "Tạo một nhân cách giả", "Create": "Tạo", "Toggle grid view": "Chuyển đổi chế độ xem lưới", "No persona description": "[<PERSON><PERSON><PERSON><PERSON> có mô tả]", "Name": "<PERSON><PERSON><PERSON>", "Enter your name": "<PERSON><PERSON><PERSON><PERSON> tên của bạn", "Click to set a new User Name": "<PERSON><PERSON><PERSON> để đặt một tên Người dùng mới", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "<PERSON><PERSON><PERSON> để khóa nhân cách được chọn của bạn vào cuộc trò chuyện hiện tại. Nhấp một lần nữa để loại bỏ khóa.", "Click to set user name for all messages": "<PERSON><PERSON><PERSON><PERSON> để đặt tên người dùng cho tất cả các tin nhắn", "Persona Description": "<PERSON><PERSON> c<PERSON>ch", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "<PERSON><PERSON> dụ: [{{user}} là một cô bé mèo người România 28 tuổi.]", "Tokens persona description": "<PERSON><PERSON> c<PERSON>ch", "Position:": "<PERSON><PERSON> trí:", "In Story String / Prompt Manager": "Trong Chuỗi Truyện / <PERSON>u<PERSON>n lý <PERSON>ờ<PERSON> nhắc", "Top of Author's Note": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON> của <PERSON>", "Bottom of Author's Note": "Dưới <PERSON> chú của Tá<PERSON> g<PERSON>", "In-chat @ Depth": "Trong cuộc trò chuy<PERSON> @ Độ sâu", "Depth:": "<PERSON><PERSON> sâu:", "Role:": "<PERSON>ai trò:", "System": "<PERSON><PERSON> th<PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON> dùng", "Assistant": "<PERSON><PERSON><PERSON>", "Show notifications on switching personas": "<PERSON><PERSON><PERSON> thị thông báo khi chuyển đổi nhân cách", "Character Management": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> vật", "Locked = Character Management panel will stay open": "<PERSON><PERSON><PERSON><PERSON> khóa = <PERSON><PERSON><PERSON>u<PERSON>n lý <PERSON>ân vật sẽ được mở", "Select/Create Characters": "Chọn/Tạo Nhân vật", "Favorite characters to add them to HotSwaps": "<PERSON><PERSON><PERSON> thích các nhân vật để thêm chúng vào HotSwaps", "Token counts may be inaccurate and provided just for reference.": "Số lượng mã thông báo có thể không chính xác và chỉ được cung cấp để tham khảo.", "Total tokens": "Tổng số token", "Calculating...": "<PERSON><PERSON> t<PERSON>...", "Tokens": "Token", "Permanent tokens": "Token vĩnh viễn", "Permanent": "<PERSON><PERSON><PERSON> vi<PERSON>n", "About Token 'Limits'": "Gi<PERSON><PERSON> thiệu về 'Giới hạn' Token", "Toggle character info panel": "Chuyển đổi bảng thông tin nhân vật", "Name this character": "Đặt tên cho nhân vật này", "extension_token_counter": "<PERSON><PERSON><PERSON>:", "Click to select a new avatar for this character": "<PERSON><PERSON><PERSON><PERSON> để chọn một hình đại diện mới cho nhân vật này", "Add to Favorites": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> thích", "Advanced Definition": "<PERSON><PERSON><PERSON> cao", "Character Lore": "<PERSON><PERSON><PERSON> vật", "Chat Lore": "<PERSON><PERSON><PERSON><PERSON><PERSON> thuyết trò chuyện", "Export and Download": "Xuất và Tải xuống", "Duplicate Character": "<PERSON><PERSON><PERSON> bản <PERSON> vật", "Create Character": "Tạo Nhân vật", "Delete Character": "Xóa Nhân vật", "More...": "Thêm...", "Link to World Info": "<PERSON><PERSON>n kết đến World Info", "Import Card Lore": "<PERSON>hập <PERSON>", "Scenario Override": "<PERSON><PERSON> <PERSON><PERSON> b<PERSON>n", "Convert to Persona": "<PERSON>y<PERSON><PERSON> <PERSON><PERSON><PERSON> sang <PERSON> (<PERSON>hân <PERSON>)", "Rename": "<PERSON><PERSON><PERSON> tên", "Link to Source": "<PERSON><PERSON><PERSON> kết tới nguồn", "Replace / Update": "<PERSON>hay thế/<PERSON><PERSON><PERSON> nhật", "Import Tags": "<PERSON><PERSON><PERSON><PERSON> thẻ", "Search / Create Tags": "T<PERSON>m kiếm / Tạo Thẻ", "View all tags": "<PERSON><PERSON> tất cả các tag", "Creator's Notes": "<PERSON><PERSON> chú của Tác g<PERSON>", "Show / Hide Description and First Message": "Hiện / Ẩn Mô tả và Tin nhắn Đầu tiên", "Character Description": "<PERSON><PERSON> <PERSON> vật", "Click to allow/forbid the use of external media for this character.": "<PERSON><PERSON><PERSON> để cho phép/cấm sử dụng phương tiện bên ngoài cho nhân vật này.", "Ext. Media": "<PERSON><PERSON>", "Describe your character's physical and mental traits here.": "<PERSON><PERSON> tả ngoại hình và tính cách nhân vật của bạn ở đây.", "First message": "<PERSON> nhắn đầu tiên", "Click to set additional greeting messages": "<PERSON><PERSON><PERSON> để set thêm câu chào :>", "Alt. Greetings": "thay thế. <PERSON><PERSON><PERSON> chào hỏi", "This will be the first message from the character that starts every chat.": "Đây sẽ là câu chào đầu tiên của nhân vật mỗi khi mở đầu cuộc trò chuyện nè :>", "Group Controls": "<PERSON><PERSON><PERSON><PERSON>", "Chat Name (Optional)": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "Click to select a new avatar for this group": "<PERSON><PERSON><PERSON><PERSON> để chọn một hình đại diện mới cho nhóm này", "Group reply strategy": "<PERSON><PERSON><PERSON><PERSON> phản hồ<PERSON> nh<PERSON>m", "Natural order": "Thứ tự tự nhiên", "List order": "<PERSON><PERSON><PERSON> tự danh sách", "Group generation handling mode": "<PERSON>ế độ xử lý tạo nh<PERSON>m", "Swap character cards": "Trao đổi thẻ nhân vật", "Join character cards (exclude muted)": "<PERSON>ham gia thẻ nhân vật (loại trừ tắt tiếng)", "Join character cards (include muted)": "Tham gia thẻ nhân vật (bao gồm cả tắt tiếng)", "Inserted before each part of the joined fields.": "<PERSON><PERSON><PERSON><PERSON> chèn trước mỗi phần của các trường đã nối.", "Join Prefix": "<PERSON>ham gia tiền tố", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "<PERSON><PERSON> chọn 'Tham gia thẻ nhân vật', tất cả các trường tương ứng của các nhân vật sẽ được nối với nhau.\rĐiều này có nghĩa là trong chuỗi câu chuyện chẳng hạn, tất cả các mô tả nhân vật sẽ được nối thành một văn bản lớn.\rNếu muốn tách các trường đó, bạn có thể xác định tiền tố hoặc hậu tố tại đây.\r\rGiá trị này hỗ trợ các macro thông thường và cũng sẽ thay thế {{char}} bằng tên của char có liên quan và <FIELDNAME> bằng tên của phần đó (ví dụ: mô tả, t<PERSON><PERSON> c<PERSON>ch, kị<PERSON> bản, v.v.)", "Inserted after each part of the joined fields.": "<PERSON><PERSON><PERSON><PERSON> chèn sau mỗi phần của các trường đã nối.", "Join Suffix": "<PERSON>ham gia hậu tố", "Set a group chat scenario": "Đặt một kịch bản trò chuyện nhóm", "Click to allow/forbid the use of external media for this group.": "<PERSON><PERSON><PERSON> để cho phép/cấm sử dụng phương tiện bên ngoài cho nhóm này.", "Restore collage avatar": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON>nh đại diện ghép", "Allow self responses": "<PERSON> phép tự phản hồi", "Auto Mode": "<PERSON>ế độ Tự động", "Auto Mode delay": "<PERSON><PERSON> trễ chế độ tự động", "Hide Muted Member Sprites": "Ẩn các thành viên bị câm", "Current Members": "<PERSON><PERSON><PERSON><PERSON> viên hiện tại", "Add Members": "<PERSON><PERSON><PERSON><PERSON> thành viên", "Create New Character": "Tạo Nhân vật <PERSON>", "Import Character from File": "<PERSON><PERSON><PERSON><PERSON> vật từ <PERSON>ệp", "Import content from external URL": "<PERSON><PERSON><PERSON><PERSON> nội dung từ link URL bên ngoài", "Create New Chat Group": "<PERSON><PERSON><PERSON> T<PERSON>ò chuy<PERSON>", "Characters sorting order": "<PERSON><PERSON><PERSON> tự sắp xế<PERSON> vật", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "<PERSON><PERSON><PERSON>", "Oldest": "<PERSON><PERSON> n<PERSON>", "Favorites": "<PERSON><PERSON><PERSON>ch", "Recent": "<PERSON><PERSON><PERSON><PERSON>", "Most chats": "<PERSON><PERSON><PERSON><PERSON> cu<PERSON>c trò chuyện nhất", "Least chats": "<PERSON><PERSON> cu<PERSON>c trò chuyện nhất", "Most tokens": "<PERSON>hiều token nhất", "Least tokens": "<PERSON>t token nhất", "Random": "Ngẫu nhiên", "Toggle character grid view": "Chuyển đổi chế độ xem lưới nhân vật", "Bulk_edit_characters": "Chỉnh sửa nhân vật theo lô", "Bulk select all characters": "<PERSON><PERSON><PERSON> hàng loạt tất cả các ký tự", "Bulk delete characters": "Xóa nhân vật theo lô", "popup-button-save": "<PERSON><PERSON><PERSON>", "popup-button-yes": "<PERSON><PERSON><PERSON>", "popup-button-no": "KHÔNG", "popup-button-cancel": "<PERSON><PERSON><PERSON>", "popup-button-import": "<PERSON><PERSON><PERSON><PERSON>", "Advanced Definitions": "<PERSON><PERSON><PERSON> ng<PERSON> cao", "Prompt Overrides": "<PERSON><PERSON> <PERSON><PERSON> Prompt", "(For Chat Completion and Instruct Mode)": "(<PERSON><PERSON><PERSON> với chế độ hoàn thành trò chuyện và hướng dẫn)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "<PERSON><PERSON><PERSON> {{original}} vào bất kỳ hộp nào để bao gồm Prompt mặc định tương ứng từ cài đặt hệ thống.", "Main Prompt": "Prompt Ch<PERSON>h", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "Bất kỳ nội dung nào ở đây sẽ thay thế Lời nhắc Chính mặc định được sử dụng cho nhân vật này. (v2 spec: hệ thống_lời_nhắc)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "Bất kỳ nội dung nào ở đây sẽ thay thế Lời nhắc Phá vỡ giam giữ mặc định được sử dụng cho nhân vật này. (v2 spec: hệ thống_lời_nhắc_sau_lịch_sử)", "Creator's Metadata (Not sent with the AI prompt)": "Dữ liệu siêu dữ liệu của tác gi<PERSON> (Không đư<PERSON><PERSON> gửi kèm với Prompt AI)", "Creator's Metadata": "<PERSON><PERSON><PERSON> dữ liệu của tác giả", "(Not sent with the AI Prompt)": "(<PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> gửi bằng Lời nhắc AI)", "Everything here is optional": "Tất cả mọi thứ ở đây đều là tùy chọn", "(Botmaker's name / Contact Info)": "(<PERSON><PERSON><PERSON> c<PERSON><PERSON> tác <PERSON> / <PERSON>h<PERSON><PERSON> tin <PERSON>ê<PERSON> hệ)", "(If you want to track character versions)": "(<PERSON><PERSON><PERSON> bạn muốn theo dõi phiên bản của nhân vật)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(<PERSON><PERSON> tả bot, cung cấp mẹo sử dụng hoặc liệt kê các model mà nó đã được thử nghiệm. <PERSON><PERSON><PERSON> này sẽ được hiển thị trong danh sách nhân vật.)", "Tags to Embed": "Tags để nhúng", "(Write a comma-separated list of tags)": "(<PERSON><PERSON><PERSON><PERSON> một danh sách các tags được phân tách bằng dấu phẩy)", "Personality summary": "<PERSON><PERSON><PERSON> t<PERSON>ch", "(A brief description of the personality)": "(<PERSON><PERSON>t mô tả ngắn gọn về tính cách)", "Scenario": "<PERSON><PERSON><PERSON>", "(Circumstances and context of the interaction)": "(<PERSON><PERSON><PERSON> cảnh và context của sự tương tác)", "Character's Note": "<PERSON><PERSON> chú của nhân vật", "(Text to be inserted in-chat @ designated depth and role)": "(<PERSON><PERSON><PERSON> bản sẽ được chèn vào cuộc trò chuyện @ độ sâu và vai trò được chỉ định)", "@ Depth": "@ Chiều sâu", "Role": "<PERSON>ai trò", "Talkativeness": "<PERSON><PERSON><PERSON>", "How often the character speaks in group chats!": "Tần suất nhân vật này phát biểu trong các cuộc trò chuyện nhóm!", "How often the character speaks in": "<PERSON><PERSON>n suất nhân vật này phát biểu trong", "group chats!": "các cuộc trò chuyện nhóm!", "Shy": "<PERSON><PERSON><PERSON><PERSON>", "Normal": "<PERSON><PERSON><PERSON>", "Chatty": "<PERSON><PERSON><PERSON>", "Examples of dialogue": "<PERSON>í dụ về lời đối thoại", "Important to set the character's writing style.": "<PERSON>uan trọng là phải thiết lập phong cách viết của nhân vật.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(<PERSON>ác ví dụ về đoạn hội thoại trò chuyện. Bắt đầu mỗi ví dụ với START trên một dòng mới.)", "Save": "<PERSON><PERSON><PERSON>", "Chat History": "<PERSON><PERSON><PERSON> s<PERSON> <PERSON><PERSON><PERSON>", "Import Chat": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON> ch<PERSON>", "Copy to system backgrounds": "<PERSON><PERSON> ch<PERSON><PERSON> vào n<PERSON>n hệ thống", "Rename background": "<PERSON><PERSON>i tên nền background", "Lock": "<PERSON><PERSON><PERSON><PERSON>", "Unlock": "Mở khóa", "Delete background": "<PERSON><PERSON><PERSON>n background", "Chat Scenario Override": "<PERSON><PERSON> <PERSON><PERSON> k<PERSON>ch bản trò chuy<PERSON>n", "Remove": "Xóa", "Type here...": "<PERSON><PERSON><PERSON><PERSON> vào đây...", "Chat Lorebook": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>n thuyết trò chuyện cho", "Chat Lorebook for": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>n thuyết trò chuyện cho", "chat_world_template_txt": "Thông tin thế giới được chọn sẽ bị ràng buộc với cuộc trò chuyện này. <PERSON>hi tạo phản hồi AI,\n                    nó sẽ được kết hợp với các mục từ sách truyền thuyết toàn cầu và nhân vật.", "Select a World Info file for": "<PERSON><PERSON><PERSON> m<PERSON>t tệp World Info cho", "Primary Lorebook": "<PERSON><PERSON><PERSON> th<PERSON>", "A selected World Info will be bound to this character as its own Lorebook.": "World Info được chọn sẽ được gắn với nhân vật này như một Lorebook riêng của nó.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "<PERSON><PERSON> tạo phản hồi của <PERSON>, nó sẽ được kết hợp với các mục từ một bộ chọn World Info.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "Việc xuất khẩu một nhân vật cũng sẽ xuất khẩu tệp S<PERSON><PERSON> T<PERSON>n thuyết được chọn được nhúng trong dữ liệu JSON.", "Additional Lorebooks": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> sung", "Associate one or more auxillary Lorebooks with this character.": "<PERSON><PERSON><PERSON> kết một hoặc nhiều <PERSON><PERSON><PERSON>ền thuyết phụ trợ với nhân vật này.", "NOTE: These choices are optional and won't be preserved on character export!": "LƯU Ý: <PERSON><PERSON><PERSON> lựa chọn này là tùy chọn và sẽ không được bảo tồn khi xuất khẩu nhân vật!", "Rename chat file": "<PERSON><PERSON><PERSON> tên tệp trò chuy<PERSON>n", "Export JSONL chat file": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> trò chuy<PERSON>n JSONL", "Download chat as plain text document": "<PERSON><PERSON><PERSON> xuống cuộc trò chuyện dưới dạng tài liệu văn bản đơn giản", "Delete chat file": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> trò chuy<PERSON>n", "Use tag as folder": "Gắn thẻ dưới dạng thư mục", "Hide on character card": "Ẩn trên thẻ nhân vật", "Delete tag": "Xóa tag", "Entry Title/Memo": "Ti<PERSON><PERSON> đề Đ<PERSON>ng nhập/<PERSON><PERSON> chú", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "Trạng thái nhập WI:\r🔵 Hằng số\r🟢 Bình thường\r🔗 Được vector hóa\r❌ Bị vô hiệu hóa", "WI_Entry_Status_Constant": "<PERSON><PERSON><PERSON><PERSON> thay đổi", "WI_Entry_Status_Normal": "<PERSON><PERSON><PERSON>", "WI_Entry_Status_Vectorized": "Vector hóa", "WI_Entry_Status_Disabled": "<PERSON><PERSON><PERSON> t<PERSON>", "T_Position": "↑Char: Trướ<PERSON> định nghĩa nhân vật\n↓Char: <PERSON>u định nghĩa nhân vật\n↑AN: Trước Ghi chú tác giả\n↓AN: Sau Ghi chú tác giả\n@D: Ở độ sâu", "Before Char Defs": "<PERSON><PERSON><PERSON><PERSON><PERSON> đ<PERSON>nh ngh<PERSON>a nhân vật", "After Char Defs": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ngh<PERSON>a nh<PERSON> vật", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "Trước AN", "After AN": "Sau AN", "at Depth System": "@D ⚙️", "at Depth User": "@D 👤", "at Depth AI": "@D 🤖", "Depth": "<PERSON><PERSON><PERSON> s<PERSON>u", "Order:": "Thứ tự:", "Order": "Thứ tự:", "Trigger %:": "<PERSON>ò súng %:", "Probability": "<PERSON><PERSON><PERSON>", "Duplicate world info entry": "<PERSON><PERSON><PERSON> nhập thông tin thế giới trùng lặp", "Delete world info entry": "<PERSON><PERSON><PERSON> m<PERSON> nhập thông tin thế giới", "Comma separated (required)": "<PERSON><PERSON> tách bằng dấu <PERSON> (bắt buộc)", "Primary Keywords": "<PERSON>ừ kh<PERSON>a ch<PERSON>h", "Keywords or Regexes": "Từ khóa hoặc Regex", "Comma separated list": "<PERSON><PERSON> sách được phân tách bằng dấu phẩy", "Switch to plaintext mode": "<PERSON><PERSON><PERSON><PERSON> sang chế độ bản rõ", "Logic": "Logic", "AND ANY": "VÀ BẤT KỲ", "AND ALL": "VÀ TẤT CẢ", "NOT ALL": "KHÔNG TẤT CẢ", "NOT ANY": "KHÔNG BẤT KỲ", "(ignored if empty)": "(bỏ qua nếu trống)", "Optional Filter": "<PERSON><PERSON> l<PERSON><PERSON>", "Keywords or Regexes (ignored if empty)": "Từ khóa hoặc Regexes (bỏ qua nếu trống)", "Comma separated list (ignored if empty)": "<PERSON><PERSON> sách được phân tách bằng dấu phẩy (bỏ qua nếu trống)", "Use global setting": "Sử dụng cài đặt chung", "Case-Sensitive": "<PERSON>ân biệt chữ hoa chữ thường", "Yes": "<PERSON><PERSON>", "No": "K<PERSON>ô<PERSON>", "Can be used to automatically activate Quick Replies": "<PERSON><PERSON> thể được sử dụng để tự động kích hoạt Tr<PERSON> lời n<PERSON>h", "Automation ID": "ID tự động", "( None )": "( <PERSON><PERSON><PERSON><PERSON> có )", "Content": "<PERSON><PERSON>i dung", "Exclude from recursion": "<PERSON><PERSON>i trừ khỏi đệ quy", "Prevent further recursion": "<PERSON><PERSON><PERSON> chặn đệ quy tiếp theo", "Delay until recursion": "<PERSON><PERSON><PERSON> hoãn cho đến khi đệ quy", "What this keyword should mean to the AI, sent verbatim": "<PERSON><PERSON><PERSON><PERSON> của từ khóa này đối với AI, g<PERSON><PERSON> một c<PERSON>ch nguyên văn", "Filter to Character(s)": "<PERSON><PERSON><PERSON> vật", "Character Exclusion": "Loại trừ <PERSON> vật", "-- Characters not found --": "-- <PERSON><PERSON><PERSON><PERSON> tìm thấy <PERSON> vật --", "Inclusion Group": "Nhóm <PERSON>", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "Nhóm Bao gồm đảm bảo chỉ một mục từ một nhóm được kích hoạt tại một thời điểm, nếu nhiều mục được kích hoạt.\rHỗ trợ nhiều nhóm được phân tách bằng dấu phẩy.\r\r<PERSON><PERSON>i liệu: World Info - Inclusion Group", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Ưu tiên mục nhập này: <PERSON><PERSON> đượ<PERSON> chọn, mụ<PERSON> nhập này được ưu tiên trong số tất cả các lựa chọn.\rNếu nhiều ưu tiên được ưu tiên thì cái có 'Thứ tự' cao nhất sẽ được chọn.", "Only one entry with the same label will be activated": "Chỉ một mục có cùng nhãn sẽ được kích ho<PERSON>t", "A relative likelihood of entry activation within the group": "<PERSON><PERSON><PERSON> năng tương đối của vi<PERSON><PERSON> kích ho<PERSON>t mục nhập trong nhóm", "Group Weight": "<PERSON>rọng lư<PERSON> nhóm", "Selective": "<PERSON><PERSON><PERSON>", "Use Probability": "Sử dụng <PERSON>", "Add Memo": "<PERSON><PERSON><PERSON><PERSON> bản ghi nhớ", "Text or token ids": "<PERSON><PERSON><PERSON> bản hoặc [id mã thông báo]", "close": "<PERSON><PERSON><PERSON>", "prompt_manager_edit": "<PERSON><PERSON><PERSON><PERSON>", "prompt_manager_name": "<PERSON><PERSON><PERSON>", "A name for this prompt.": "<PERSON><PERSON>n cho Prompt này.", "To whom this message will be attributed.": "<PERSON> nhắn này sẽ được quy cho ai.", "AI Assistant": "<PERSON><PERSON><PERSON>", "prompt_manager_position": "<PERSON><PERSON><PERSON> v<PERSON>", "Next to other prompts (relative) or in-chat (absolute).": "<PERSON><PERSON><PERSON> c<PERSON>nh các Prompt kh<PERSON>c (tương đối) hoặc trong trò chuy<PERSON>n (tuyệt đối).", "prompt_manager_relative": "<PERSON><PERSON><PERSON> quan đến", "prompt_manager_depth": "<PERSON><PERSON><PERSON> s<PERSON>u", "0 = after the last message, 1 = before the last message, etc.": "0 = sau tin nhắn cuối cùng, 1 = trước tin nhắn cuối cùng, v.v.", "Prompt": "Prompt", "The prompt to be sent.": "<PERSON><PERSON><PERSON> nh<PERSON>c đư<PERSON>c gửi đi.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "<PERSON><PERSON><PERSON> nhắc này không thể bị ghi đè bằng thẻ ký tự, ngay cả khi ưu tiên ghi đè.", "prompt_manager_forbid_overrides": "<PERSON><PERSON><PERSON> ghi đè", "reset": "đặt lại", "save": "<PERSON><PERSON><PERSON>", "This message is invisible for the AI": "<PERSON> nhắn này AI không thấy đ<PERSON><PERSON><PERSON> đâu", "Message Actions": "<PERSON><PERSON><PERSON> động tin nh<PERSON>n", "Translate message": "<PERSON><PERSON><PERSON> tin <PERSON>", "Generate Image": "<PERSON><PERSON><PERSON>", "Narrate": "Dẫn chuyện", "Exclude message from prompts": "<PERSON><PERSON><PERSON> trừ tin nhắn khỏi các Prompt", "Include message in prompts": "<PERSON><PERSON> gồm tin nhắn trong các Prompt", "Embed file or image": "<PERSON><PERSON><PERSON><PERSON> tệp hoặc hình ảnh", "Create checkpoint": "<PERSON><PERSON>o điểm kiểm tra", "Create Branch": "Tạo Chi <PERSON>án<PERSON>", "Copy": "Sao chép", "Open checkpoint chat": "Mở trò chuyện điểm kiểm tra", "Edit": "Chỉnh sửa", "Confirm": "<PERSON><PERSON><PERSON>", "Copy this message": "<PERSON>o chép tin nhắn này", "Delete this message": "<PERSON><PERSON><PERSON> tin nhắn này", "Move message up": "<PERSON> chuyển tin nhắn lên", "Move message down": "<PERSON> chuyển tin nhắn xuống", "Enlarge": "<PERSON><PERSON><PERSON> to", "Welcome to SillyTavern!": "SillyT<PERSON>n xin chào!", "welcome_message_part_1": "Xem", "welcome_message_part_2": "<PERSON><PERSON><PERSON> li<PERSON><PERSON> ch<PERSON> th<PERSON>c", "welcome_message_part_3": null, "welcome_message_part_4": "<PERSON><PERSON><PERSON>", "welcome_message_part_5": "trong trò chuyện để nhận lệnh và macro.", "welcome_message_part_6": "Tham gia", "Discord server": "Server Discord", "welcome_message_part_7": "<PERSON><PERSON> biết thêm thông tin và nhận thông báo ch<PERSON>h thức.", "SillyTavern is aimed at advanced users.": "<PERSON><PERSON><PERSON><PERSON><PERSON> dành cho dân pro, dân ch<PERSON>i thứ thi<PERSON>t.", "If you're new to this, enable the simplified UI mode below.": "<PERSON><PERSON><PERSON> bạn chưa quen dùng lắm, bật chế độ UI đơn giản bên dư<PERSON><PERSON> nha.", "Change it later in the 'User Settings' panel.": "Thay đổi nó sau trong bảng 'Cài đặt người dùng'.", "Enable simple UI mode": "<PERSON><PERSON>t chế độ giao diện người dùng đơn giản", "Looking for AI characters?": "Bạn đang tìm kiếm các nhân vật AI à?", "onboarding_import": "<PERSON><PERSON><PERSON><PERSON>", "from supported sources or view": "từ các nguồn hoặc chế độ xem được hỗ trợ", "Sample characters": "<PERSON><PERSON> tự mẫu", "Your Persona": "<PERSON><PERSON><PERSON> c<PERSON>ch của bạn", "Before you get started, you must select a persona name.": "<PERSON><PERSON><PERSON><PERSON><PERSON> khi bắt đầu, bạn phải chọn một cái tên nhân vật đã.", "welcome_message_part_8": "<PERSON><PERSON><PERSON><PERSON> này có thể được thay đổi bất cứ lúc nào thông qua", "welcome_message_part_9": "<PERSON><PERSON><PERSON>.", "Persona Name:": "<PERSON><PERSON><PERSON> nhân vật:", "Temporarily disable automatic replies from this character": "<PERSON><PERSON><PERSON> thời vô hiệu hóa các phản hồi tự động từ nhân vật này", "Enable automatic replies from this character": "<PERSON><PERSON>t phản hồi tự động từ nhân vật này", "Trigger a message from this character": "<PERSON><PERSON><PERSON> hoạt một tin nhắn từ nhân vật này", "Move up": "<PERSON> chuyển lên", "Move down": "<PERSON> xuống", "View character card": "Xem thẻ nhân vật", "Remove from group": "Xóa khỏi nhóm", "Add to group": "Thêm vào nhóm", "Alternate Greetings": "<PERSON><PERSON><PERSON> chào thay thế", "Alternate_Greetings_desc": "<PERSON>úng sẽ được hiển thị dưới dạng thao tác vuốt trên tin nhắn đầu tiên khi bắt đầu cuộc trò chuyện mới.\n                <PERSON><PERSON>c thành viên trong nhóm có thể chọn một trong số họ để bắt đầu cuộc trò chuyện.", "Alternate Greetings Hint": "<PERSON><PERSON><PERSON><PERSON> vào nút để bắt đầu!", "(This will be the first message from the character that starts every chat)": "(Đ<PERSON><PERSON><PERSON> này sẽ là tin nhắn đầu tiên từ nhân vật mà bắt đầu mỗi cuộc trò chuyện)", "Forbid Media Override explanation": "<PERSON><PERSON><PERSON> năng sử dụng phương tiện truyền thông bên ngoài trong trò chuyện của nhân vật/nhóm hiện tại.", "Forbid Media Override subtitle": "Phương tiện: <PERSON><PERSON><PERSON>, video, âm thanh. <PERSON><PERSON><PERSON> ngo<PERSON>: không đư<PERSON><PERSON> lưu trữ trên máy chủ cục bộ.", "Always forbidden": "<PERSON><PERSON><PERSON> c<PERSON>m", "Always allowed": "<PERSON><PERSON><PERSON> luôn cho phép", "View contents": "<PERSON><PERSON> n<PERSON> dung", "Remove the file": "<PERSON><PERSON><PERSON> tập tin", "Unique to this chat": "<PERSON><PERSON> <PERSON>h<PERSON>t cho cuộc trò chuyện này", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "<PERSON>iểm kiểm tra kế thừa Ghi chú từ cha mẹ của chúng và có thể được thay đổi riêng lẻ sau đó.", "Include in World Info Scanning": "Bao gồm trong World Info scanning", "Before Main Prompt / Story String": "Tr<PERSON><PERSON><PERSON> nhắc ch<PERSON>h/Chuỗi câu chuyện", "After Main Prompt / Story String": "Sau Prompt chính/chuỗi câu chuyện", "as": "BẰNG", "Insertion Frequency": "<PERSON><PERSON><PERSON> s<PERSON> chèn", "(0 = Disable, 1 = Always)": "(0 = <PERSON><PERSON><PERSON>, 1 = <PERSON><PERSON><PERSON> luôn)", "User inputs until next insertion:": "<PERSON><PERSON><PERSON> vào của người dùng cho đến lần chèn tiếp theo:", "Character Author's Note (Private)": "<PERSON><PERSON> chú của tác giả nhân vật (Riêng tư)", "Won't be shared with the character card on export.": "Sẽ không được chia sẻ với thẻ nhân vật khi xuất.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "Sẽ được tự động thêm vào làm ghi chú của tác giả cho nhân vật này. Sẽ được sử dụng theo nhó<PERSON>, nhưng\n                            không thể sửa đổi khi cuộc trò chuyện nhóm đang mở.", "Use character author's note": "Sử dụng ghi chú của tác giả nhân vật", "Replace Author's Note": "<PERSON>hay thế ghi chú của tác giả", "Default Author's Note": "<PERSON><PERSON> chú của tác giả mặc định", "Will be automatically added as the Author's Note for all new chats.": "Sẽ được tự động thêm làm Ghi chú của tác giả cho tất cả các cuộc trò chuyện mới.", "Chat CFG": "<PERSON><PERSON>ò <PERSON>n CFG", "1 = disabled": "1 = bị vô hi<PERSON>u h<PERSON>a", "write short replies, write replies using past tense": "viết trả lời <PERSON>, viết trả lời dùng thì quá khứ", "Positive Prompt": "<PERSON><PERSON><PERSON> tích c<PERSON>c", "Use character CFG scales": "Sử dụng thang đo CFG ký tự", "Character CFG": "CFG nhân vật", "Will be automatically added as the CFG for this character.": "Sẽ được tự động thêm làm CFG cho nhân vật này.", "Global CFG": "CFG toàn c<PERSON>u", "Will be used as the default CFG options for every chat unless overridden.": "Sẽ được sử dụng làm tùy chọn CFG mặc định cho mọi cuộc trò chuyện trừ khi bị ghi đè.", "CFG Prompt Cascading": "<PERSON><PERSON><PERSON> tầng nh<PERSON>c nhở CFG", "Combine positive/negative prompts from other boxes.": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> nh<PERSON> Prompt tích cực/tiêu cực từ các ô khác.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "Ví dụ: vi<PERSON><PERSON> đánh dấu vào các hộp trò ch<PERSON>, chung và ký tự sẽ kết hợp tất cả các Prompt phủ định thành một chuỗi được phân tách bằng dấu phẩy.", "Always Include": "<PERSON><PERSON><PERSON> bao gồm", "Chat Negatives": "<PERSON><PERSON><PERSON> chuy<PERSON>n phủ định", "Character Negatives": "<PERSON><PERSON><PERSON> vật ti<PERSON>u c<PERSON>c", "Global Negatives": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c toàn c<PERSON>u", "Custom Separator:": "<PERSON><PERSON><PERSON> phân cách tùy chỉnh:", "Insertion Depth:": "<PERSON><PERSON> sâu chèn:", "Token Probabilities": "<PERSON><PERSON><PERSON> su<PERSON>t <PERSON> thông báo", "Select a token to see alternatives considered by the AI.": "<PERSON><PERSON><PERSON> một mã thông báo để xem các phương án thay thế được AI xem xét.", "Not connected to API!": "Không kết nối với API!", "Type a message, or /? for help": "<PERSON>hập gì đó đi bồ ơi, hoặc gõ /? để tui bày cho", "Continue script execution": "<PERSON><PERSON><PERSON><PERSON> tục thực thi tập lệnh", "Pause script execution": "<PERSON>ạ<PERSON> dừng thực thi tập lệnh", "Abort script execution": "H<PERSON>y bỏ việc thực thi tập lệnh", "Abort request": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u", "Continue the last message": "<PERSON><PERSON><PERSON><PERSON> tục từ tin nhắn cuối cùng", "Send a message": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "Close chat": "<PERSON><PERSON><PERSON> trò <PERSON>", "Toggle Panels": "<PERSON><PERSON><PERSON> b<PERSON> lên", "Back to parent chat": "Quay lại cuộc trò chuyện ch<PERSON>h", "Save checkpoint": "<PERSON><PERSON><PERSON> checkpoint", "Convert to group": "<PERSON>y<PERSON><PERSON> đổi thành nh<PERSON>m", "Start new chat": "<PERSON><PERSON><PERSON> đầu trò chuyện mới", "Manage chat files": "<PERSON><PERSON><PERSON><PERSON> lý tệp trò chuy<PERSON>n", "Delete messages": "<PERSON><PERSON><PERSON> tin n<PERSON>n", "Regenerate": "<PERSON><PERSON><PERSON> lạ<PERSON>", "Ask AI to write your message for you": "<PERSON><PERSON>u AI viết tin nhắn hộ bạn", "Impersonate": "<PERSON><PERSON><PERSON> tho<PERSON><PERSON> danh", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Bind user name to that avatar": "<PERSON><PERSON><PERSON> kết tên người dùng với avatar đó", "Change persona image": "<PERSON>hay đổi ảnh đại diện nhân cách", "Select this as default persona for the new chats.": "<PERSON><PERSON><PERSON> cái này làm nhân cách mặc định cho các cuộc trò chuyện mới.", "Delete persona": "Xóa n<PERSON>ân c<PERSON>ch", "These characters are the winners of character design contests and have outstandable quality.": "Những nhân vật này là người chiến thắng trong các cuộc thi thiết kế nhân vật và có chất lượng vượt trội.", "Contest Winners": "<PERSON><PERSON><PERSON><PERSON> chiến thắng cuộc thi", "These characters are the finalists of character design contests and have remarkable quality.": "Những nhân vật này lọt vào vòng chung kết của các cuộc thi thiết kế nhân vật và có chất lượng vượt trội.", "Featured Characters": "<PERSON><PERSON><PERSON> vật n<PERSON>i bật", "Attach a File": "<PERSON><PERSON><PERSON> k<PERSON>m tập tin", "Open Data Bank": "Mở <PERSON>ân hàng dữ liệu", "Enter a URL or the ID of a Fandom wiki page to scrape:": "Nhập URL hoặc ID của trang wiki Fandom để thu thập:", "Examples:": "<PERSON><PERSON> dụ:", "Example:": "<PERSON><PERSON> dụ:", "Single file": "<PERSON><PERSON><PERSON>", "All articles will be concatenated into a single file.": "Tất cả các bài viết sẽ được nối lại thành một tệp duy nhất.", "File per article": "<PERSON><PERSON><PERSON> tin cho mỗi bài viết", "Each article will be saved as a separate file.": "<PERSON>hông được khu<PERSON>ến khích. Mỗi bài viết sẽ được lưu thành một file riêng biệt.", "Data Bank": "<PERSON><PERSON> liệu ngân hàng", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "<PERSON><PERSON><PERSON> tệp này sẽ có sẵn cho các tiện ích mở rộng hỗ trợ tệp đ<PERSON>h kèm (ví dụ: <PERSON>ộ lưu trữ Vector).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "<PERSON><PERSON><PERSON> lo<PERSON>i tệp được hỗ trợ: <PERSON><PERSON><PERSON> bản thuần túy, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "<PERSON><PERSON>o và thả tập tin vào đây để tải lên.", "Date (Newest First)": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> nhất đầu tiên)", "Date (Oldest First)": "<PERSON><PERSON><PERSON> (<PERSON><PERSON> nhất đầu tiên)", "Name (A-Z)": "<PERSON><PERSON><PERSON> (A-Z)", "Name (Z-A)": "<PERSON><PERSON><PERSON> (Z-A)", "Size (Smallest First)": "<PERSON><PERSON><PERSON> (Nhỏ nhất đầu tiên)", "Size (Largest First)": "<PERSON><PERSON><PERSON> (Lớn nhất đầu tiên)", "Bulk Edit": "Chỉnh sửa hàng lo<PERSON>t", "Select All": "<PERSON><PERSON><PERSON> tất cả", "Select None": "<PERSON><PERSON><PERSON> có", "Global Attachments": "<PERSON><PERSON><PERSON> đ<PERSON> k<PERSON>m toàn c<PERSON>u", "These files are available for all characters in all chats.": "<PERSON><PERSON><PERSON><PERSON> tệp này có sẵn cho tất cả các ký tự trong tất cả các cuộc trò chuyện.", "Character Attachments": "<PERSON><PERSON><PERSON> đ<PERSON> k<PERSON>m ký tự", "These files are available the current character in all chats they are in.": "<PERSON><PERSON><PERSON> tệp này có sẵn ký tự hiện tại trong tất cả các cuộc trò chuyện có chứa chúng.", "Saved locally. Not exported.": "<PERSON><PERSON> lưu cục bộ. <PERSON><PERSON><PERSON>ng xuất khẩu.", "Chat Attachments": "<PERSON><PERSON><PERSON> đ<PERSON> kèm trò ch<PERSON>n", "These files are available to all characters in the current chat.": "<PERSON><PERSON><PERSON><PERSON> tệp này có sẵn cho tất cả các nhân vật trong cuộc trò chuyện hiện tại.", "Enter a base URL of the MediaWiki to scrape.": "<PERSON><PERSON><PERSON>p URL cơ sở của MediaWiki để thu thập.", "Don't include the page name!": "<PERSON><PERSON><PERSON><PERSON> bao gồm tên trang!", "Enter web URLs to scrape (one per line):": "Nhập URL web để cạo (một URL trên mỗi dòng):", "Enter a video URL to download its transcript.": "Nhập URL hoặc ID video để tải xuống bản ghi của nó.", "Expression API": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> ích b<PERSON> sung\nLLM", "ext_sum_with": "<PERSON><PERSON>m tắt với:", "ext_sum_main_api": "API chính", "ext_sum_current_summary": "<PERSON><PERSON><PERSON> tắt hiện tại:", "ext_sum_restore_previous": "<PERSON><PERSON><PERSON> hồi tr<PERSON><PERSON><PERSON> đó", "ext_sum_memory_placeholder": "T<PERSON><PERSON> tắt sẽ được tạo ở đây...", "Trigger a summary update right now.": "<PERSON><PERSON><PERSON> tắt ngay bây giờ", "ext_sum_force_text": "<PERSON><PERSON><PERSON> tắt ngay bây giờ", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "Tắt cập nhật tóm tắt tự động. Trong khi tạm dừng, tóm tắt vẫn giữ nguyên. Bạn vẫn có thể buộc cập nhật bằng cách nhấn nút Tóm tắt ngay (chỉ khả dụng với API chính).", "ext_sum_pause": "<PERSON><PERSON><PERSON>", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "Bỏ qua World Info và Ghi chú của tác giả khỏi văn bản cần tóm tắt. Chỉ có tác dụng khi sử dụng API chính. API bổ sung luôn bỏ qua WI/AN.", "ext_sum_no_wi_an": "Không có WI/AN", "ext_sum_settings_tip": "Chỉnh sửa Prompt t<PERSON><PERSON> tắ<PERSON>, vị tr<PERSON>, v.v.", "ext_sum_settings": "Cài đặt tóm tắt", "ext_sum_prompt_builder": "<PERSON>r<PERSON><PERSON> tạo nh<PERSON>c nhở", "ext_sum_prompt_builder_1_desc": "Tiện ích mở rộng sẽ xây dựng Prompt riêng bằng cách sử dụng các thông báo chưa được tóm tắt. Chặn cuộc trò chuyện cho đến khi bản tóm tắt được tạo.", "ext_sum_prompt_builder_1": "<PERSON><PERSON><PERSON><PERSON>, chặn", "ext_sum_prompt_builder_2_desc": "Tiện ích mở rộng sẽ xây dựng Prompt riêng bằng cách sử dụng các thông báo chưa được tóm tắt. Không chặn cuộc trò chuyện trong khi bản tóm tắt đang được tạo. Không phải tất cả các chương trình phụ trợ đều hỗ trợ chế độ này.", "ext_sum_prompt_builder_2": "Nguyên, không chặn", "ext_sum_prompt_builder_3_desc": "Tiện ích mở rộng sẽ sử dụng trình tạo Prompt chính thông thường và thêm yêu cầu tóm tắt vào đó làm thông báo hệ thống cuối cùng.", "ext_sum_prompt_builder_3": "<PERSON><PERSON> điển, chặn", "Summary Prompt": "<PERSON><PERSON><PERSON> tóm tắt", "ext_sum_restore_default_prompt_tip": "<PERSON><PERSON><PERSON><PERSON> phụ<PERSON> Prompt mặc định", "ext_sum_prompt_placeholder": "Lời nhắc này sẽ được gửi tới AI để yêu cầu tạo bản tóm tắt. {{words}} sẽ phân giải thành tham số 'Số từ'.", "ext_sum_target_length_1": "<PERSON><PERSON> dài tóm tắt mục tiêu", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "từ)", "ext_sum_api_response_length_1": "Đ<PERSON> dài phản hồi <PERSON>", "ext_sum_api_response_length_2": null, "ext_sum_api_response_length_3": "mã thông báo)", "ext_sum_0_default": "0 = mặc định", "ext_sum_raw_max_msg": "[Thô] <PERSON><PERSON> tin nhắn tối đa cho mỗi yêu cầu", "ext_sum_0_unlimited": "0 = không giới hạn", "Update frequency": "<PERSON><PERSON><PERSON> <PERSON><PERSON> cập nh<PERSON>t", "ext_sum_update_every_messages_1": "<PERSON><PERSON><PERSON> nhật mỗi", "ext_sum_update_every_messages_2": "tin nhắn", "ext_sum_0_disable": "0 = v<PERSON> h<PERSON>a", "ext_sum_auto_adjust_desc": "Cố gắng tự động điều chỉnh khoảng thời gian dựa trên số liệu trò chuyện.", "ext_sum_update_every_words_1": "<PERSON><PERSON><PERSON> nhật mỗi", "ext_sum_update_every_words_2": "từ", "ext_sum_both_sliders": "<PERSON><PERSON>u cả hai thanh trư<PERSON>t đều kh<PERSON>c 0 thì cả hai sẽ kích hoạt cập nhật tóm tắt theo các khoảng thời gian tương <PERSON>ng.", "ext_sum_injection_template": "Mẫu tiêm", "ext_sum_memory_template_placeholder": "{{tóm tắt}} sẽ chuy<PERSON>n sang nội dung tóm tắt hiện tại.", "ext_sum_injection_position": "<PERSON><PERSON> trí tiêm", "How many messages before the current end of the chat.": "<PERSON><PERSON> bao nhiêu tin nhắn trước khi kết thúc cuộc trò chuyện hiện tại.", "ext_regex_title": "<PERSON><PERSON><PERSON><PERSON> thức ch<PERSON>h quy", "ext_regex_new_global_script": "+ <PERSON><PERSON><PERSON> c<PERSON>u", "ext_regex_new_scoped_script": "+ Phạm vi", "ext_regex_import_script": "<PERSON><PERSON><PERSON><PERSON>", "ext_regex_global_scripts": "<PERSON><PERSON><PERSON> l<PERSON>nh to<PERSON>n c<PERSON>u", "ext_regex_global_scripts_desc": "<PERSON><PERSON> sẵn cho tất cả các nhân vật. <PERSON><PERSON> lưu vào cài đặt cục bộ.", "ext_regex_scoped_scripts": "<PERSON><PERSON><PERSON> l<PERSON>nh có phạm vi", "ext_regex_scoped_scripts_desc": "Chỉ có sẵn cho nhân vật này. Đã lưu vào dữ liệu thẻ.", "Regex Editor": "<PERSON><PERSON><PERSON><PERSON> soạn thảo Regex", "Test Mode": "Chê độ kiểm tra", "ext_regex_desc": "Regex là công cụ tìm/thay thế chuỗi bằng biểu thức chính quy. Nếu bạn muốn tìm hiểu thêm, hãy nhấp vào ? bên cạnh tiêu đề.", "Input": "<PERSON><PERSON><PERSON> vào", "ext_regex_test_input_placeholder": "Nhập ở đây...", "Output": "<PERSON><PERSON><PERSON> ra", "ext_regex_output_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "Script Name": "<PERSON><PERSON><PERSON> tậ<PERSON> l<PERSON>nh", "Find Regex": "<PERSON><PERSON>m bi<PERSON>u thức ch<PERSON>h quy", "Replace With": "Thay bằng", "ext_regex_replace_string_placeholder": "Sử dụng {{match}} để bao gồm văn bản khớp từ Find Regex hoặc $1, $2, v.v. cho các nhóm nắm bắt.", "Trim Out": "Cắt bỏ", "ext_regex_trim_placeholder": "Cắt bỏ toàn bộ các phần không mong muốn khỏi kết quả khớp regex trước khi thay thế. <PERSON><PERSON><PERSON> từng phần tử bằng dấu enter.", "ext_regex_affects": "ảnh hưởng", "ext_regex_user_input": "<PERSON><PERSON><PERSON> vào của người dùng", "ext_regex_ai_output": "<PERSON><PERSON><PERSON> ra AI", "Slash Commands": "<PERSON><PERSON><PERSON> g<PERSON>ch chéo", "ext_regex_min_depth_desc": "<PERSON>hi áp dụng cho Prompt hoặc hiển thị, chỉ ảnh hưởng đến các tin nhắn có độ sâu ít nhất N cấp độ. 0 = tin nhắn cuối cùng, 1 = tin nhắn áp chót, v.v. Chỉ tính các mục WI @Depth và các tin nhắn có thể sử dụng được, tức là không bị ẩn hoặc hệ thống.", "Min Depth": "<PERSON><PERSON> sâu tối thiểu", "ext_regex_min_depth_placeholder": "<PERSON><PERSON> h<PERSON>", "ext_regex_max_depth_desc": "<PERSON>hi áp dụng cho Prompt hoặc màn hình, chỉ ảnh hưởng đến tin nhắn không quá N cấp độ. 0 = tin nhắn cuối cùng, 1 = tin nhắn áp chót, v.v. Chỉ tính các mục WI @Depth và các tin nhắn có thể sử dụng được, tức là không bị ẩn hoặc hệ thống.", "ext_regex_other_options": "Sự lựa chọn kh<PERSON>c", "Only Format Display": "Chỉ hiển thị định dạng", "ext_regex_only_format_prompt_desc": "<PERSON><PERSON><PERSON> sử trò chuyện sẽ không thay đổi, chỉ có Prompt khi yêu cầu đư<PERSON><PERSON> g<PERSON> (khi tạo).", "Only Format Prompt (?)": "Chỉ định dạng nhắc nhở", "Run On Edit": "<PERSON><PERSON><PERSON> trên Chỉnh sửa", "ext_regex_substitute_regex_desc": "Thay thế {{macros}} trong Tìm Regex trước khi chạy nó", "Substitute Regex": "Thay thế Regex", "ext_regex_import_target": "<PERSON><PERSON>ập vào:", "ext_regex_disable_script": "<PERSON><PERSON><PERSON> tập l<PERSON>nh", "ext_regex_enable_script": "<PERSON><PERSON><PERSON> tập l<PERSON>nh", "ext_regex_edit_script": "Chỉnh sửa tập lệnh", "ext_regex_move_to_global": "<PERSON> chuyển đến các tập lệnh toàn cầu", "ext_regex_move_to_scoped": "<PERSON> chuyển đến các tập lệnh có phạm vi", "ext_regex_export_script": "<PERSON><PERSON><PERSON> tập l<PERSON>nh", "ext_regex_delete_script": "<PERSON><PERSON><PERSON> tậ<PERSON> l<PERSON>nh", "Trigger Stable Diffusion": "<PERSON><PERSON><PERSON><PERSON> tán <PERSON>n đ<PERSON>nh k<PERSON>ch ho<PERSON>t", "sd_Yourself": "<PERSON><PERSON><PERSON> thân bạn", "sd_Your_Face": "Mặt của bạn", "sd_Me": "<PERSON><PERSON><PERSON>", "sd_The_Whole_Story": "<PERSON><PERSON><PERSON> bộ c<PERSON>u chuy<PERSON>n", "sd_The_Last_Message": "<PERSON> nhắn cuối cùng", "sd_Raw_Last_Message": "<PERSON> nhắn cuối cùng thô", "sd_Background": "<PERSON><PERSON>", "Image Generation": "<PERSON><PERSON><PERSON>", "sd_refine_mode": "<PERSON> phép chỉnh sửa Prompt theo cách thủ công trước khi gửi chúng đến API thế hệ", "sd_refine_mode_txt": "Chỉnh sửa Prompt trước khi tạo", "sd_interactive_mode": "Tự động tạo hình ảnh khi gửi tin nhắn như “gửi cho tôi ảnh con mèo”.", "sd_interactive_mode_txt": "<PERSON><PERSON> độ tương tác", "sd_multimodal_captioning": "Sử dụng chú thích đa phương thức để tạo Prompt về chân dung người dùng và nhân vật dựa trên hình đại diện của họ.", "sd_multimodal_captioning_txt": "<PERSON><PERSON> dụng chú thích đa phương thức cho ảnh chân dung", "sd_expand": "Tự động mở rộng Prompt bằng mô hình tạo văn bản", "sd_expand_txt": "<PERSON><PERSON><PERSON> nhắc tự động nâng cao", "sd_snap": "Điều chỉnh nhanh các yêu cầu tạo có tỷ lệ khung hình bắt buộc (chân dung, hình nền) về độ phân giải đã biết gần nhất, đồng thời cố gắng duy trì số lượng pixel tuyệt đối (đ<PERSON><PERSON><PERSON> khuyến nghị cho SDXL).", "sd_snap_txt": "<PERSON><PERSON><PERSON> độ phân giải được điều chỉnh tự động", "Source": "<PERSON><PERSON><PERSON><PERSON>", "sd_auto_url": "Ví dụ: {{auto_url}}", "Authentication (optional)": "<PERSON><PERSON><PERSON> (t<PERSON><PERSON> ch<PERSON>n)", "Example: username:password": "<PERSON><PERSON> dụ: tên người dùng:mật khẩu", "Important:": "<PERSON><PERSON> trọng:", "sd_auto_auth_warning_1": "ch<PERSON>y giao di<PERSON>n người dùng web SD với", "sd_auto_auth_warning_2": "lá cờ! Máy chủ phải có thể truy cập đư<PERSON><PERSON> từ máy chủ SillyTavern.", "sd_drawthings_url": "Ví dụ: {{drawthings_url}}", "sd_drawthings_auth_txt": "chạy ứng dụng DrawThings với tính năng chuyển đổi API HTTP đượ<PERSON> bật trong giao diện người dùng! Máy chủ phải có thể truy cập được từ máy chủ SillyTavern.", "sd_vlad_url": "Ví dụ: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "<PERSON><PERSON>y chủ phải có thể truy cập đ<PERSON><PERSON><PERSON> từ máy chủ SillyTavern.", "Hint: Save an API key in AI Horde API settings to use it here.": "Gợi ý: <PERSON><PERSON><PERSON> API trong cài đặt API AI Horde để sử dụng tại đây.", "Allow NSFW images from Horde": "<PERSON> phép hình <PERSON>nh NSFW từ Horde", "Sanitize prompts (recommended)": "Nhắc nhở vệ sinh (khuyến nghị)", "Automatically adjust generation parameters to ensure free image generations.": "Tự động điều chỉnh các thông số tạo để đảm bảo tạo ra hình ảnh miễn phí.", "Avoid spending Anlas": "<PERSON><PERSON><PERSON><PERSON> chi ti<PERSON><PERSON>", "Opus tier": "(Cấp Opus)", "View my Anlas": "<PERSON><PERSON> t<PERSON>i", "These settings only apply to DALL-E 3": "<PERSON><PERSON><PERSON> cài đặt này chỉ áp dụng cho DALL-E 3", "Image Style": "<PERSON><PERSON><PERSON>", "Image Quality": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>", "Standard": "<PERSON><PERSON><PERSON><PERSON>", "HD": "HD", "sd_comfy_url": "Ví dụ: {{comfy_url}}", "Open workflow editor": "Mở trình chỉnh sửa quy trình công việc", "Create new workflow": "<PERSON><PERSON><PERSON> quy trình làm việc mới", "Delete workflow": "<PERSON><PERSON><PERSON> quy trình làm việc", "Enhance": "<PERSON><PERSON><PERSON> cao", "Refine": "<PERSON><PERSON><PERSON>", "Decrisper": "Bộ giải mã", "Sampling steps": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> l<PERSON> mẫu ()", "Width": "<PERSON><PERSON><PERSON> rộng ()", "Height": "<PERSON><PERSON><PERSON> cao ()", "Resolution": "<PERSON><PERSON><PERSON>", "Model": "Người mẫu", "Sampling method": "Phương ph<PERSON>p lấy mẫu", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (không phải tất cả các trình lấy mẫu đều được hỗ trợ)", "SMEA versions of samplers are modified to perform better at high resolution.": "<PERSON><PERSON><PERSON> phiên bản lấy mẫu SMEA được sửa đổi để hoạt động tốt hơn ở độ phân giải cao.", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "<PERSON><PERSON><PERSON> biến thể DYN của bộ lấy mẫu SMEA thường cho kết quả đầu ra đa dạng hơn nhưng có thể bị lỗi ở độ phân giải rất cao.", "DYN": "DYN", "Scheduler": "<PERSON><PERSON><PERSON><PERSON> lập kế ho<PERSON>ch", "Restore Faces": "<PERSON>h<PERSON><PERSON> phục khuôn mặt", "Hires. Fix": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ch<PERSON>a", "Upscaler": "n<PERSON><PERSON> c<PERSON>p", "Upscale by": "<PERSON> cấp bởi", "Denoising strength": "<PERSON><PERSON><PERSON> độ khử noise", "Hires steps (2nd pass)": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> <PERSON><PERSON> 2)", "Preset for prompt prefix and negative prompt": "Đặt trước cho tiền tố Prompt và Prompt phủ định", "Style": "<PERSON><PERSON> c<PERSON>ch", "Save style": "<PERSON><PERSON><PERSON>", "Delete style": "<PERSON><PERSON><PERSON> k<PERSON>", "Common prompt prefix": "<PERSON><PERSON><PERSON><PERSON> tố n<PERSON> chung", "sd_prompt_prefix_placeholder": "Sử dụng {prompt} để chỉ định vị trí chèn Prompt đ<PERSON><PERSON><PERSON> tạo", "Negative common prompt prefix": "Ti<PERSON>n tố dấu nh<PERSON>c chung phủ định", "Character-specific prompt prefix": "Ti<PERSON>n tố nhắc nhở dành riêng cho ký tự", "Won't be used in groups.": "Sẽ không được sử dụng trong nhóm.", "sd_character_prompt_placeholder": "Bất kỳ đặc điểm nào mô tả ký tự hiện được chọn. Sẽ được thêm vào sau tiền tố nhắc chung.\nVí dụ: nữ, m<PERSON><PERSON> xanh, t<PERSON><PERSON> nâu, <PERSON><PERSON> hồng", "Character-specific negative prompt prefix": "Ti<PERSON>n tố nhắc phủ định dành riêng cho ký tự", "sd_character_negative_prompt_placeholder": "Bất kỳ đặc điểm nào không xuất hiện cho ký tự đã chọn. Sẽ được thêm vào sau tiền tố dấu nhắc chung phủ định.\nVí dụ: đ<PERSON> trang sứ<PERSON>, g<PERSON><PERSON><PERSON>, k<PERSON><PERSON>", "Shareable": "<PERSON><PERSON> thể chia sẻ", "Image Prompt Templates": "Mẫu nhắc nhở hình ảnh", "Vectors Model Warning": "Bạn nên xóa vectơ khi thay đổi mô hình trong khi trò chuyện. <PERSON><PERSON><PERSON> khô<PERSON>, nó sẽ dẫn đến kết quả dưới mệnh giá.", "Translate files into English before processing": "<PERSON><PERSON><PERSON> file sang tiếng <PERSON>h trư<PERSON>c khi xử lý", "Manager Users": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "New User": "<PERSON>ư<PERSON><PERSON> dùng mới", "Status:": "Trạng thái:", "Created:": "Tạo:", "Display Name:": "<PERSON><PERSON><PERSON> hiển thị:", "User Handle:": "<PERSON><PERSON> lý người dùng:", "Password:": "<PERSON><PERSON><PERSON>:", "Confirm Password:": "<PERSON><PERSON><PERSON> nh<PERSON>n mật kh<PERSON>u:", "This will create a new subfolder...": "<PERSON><PERSON><PERSON>u này sẽ tạo một thư mục con mới trong thư mục /data/ với tên thư mục do người dùng xử lý.", "Current Password:": "<PERSON><PERSON><PERSON> <PERSON><PERSON> hiện tại:", "New Password:": "<PERSON><PERSON><PERSON> k<PERSON>u mới:", "Confirm New Password:": "<PERSON><PERSON><PERSON> nhận mật khẩu mới:", "Debug Warning": "<PERSON><PERSON><PERSON> chức năng trong danh mục này chỉ dành cho người dùng pro. Đ<PERSON>ng nhấp vào bất cứ thứ gì nếu bạn không chắc chắn về hậu quả.", "Execute": "<PERSON><PERSON><PERSON><PERSON>", "Are you sure you want to delete this user?": "Bạn có chắc chắn muốn xóa người dùng này?", "Deleting:": "<PERSON><PERSON> x<PERSON>:", "Also wipe user data.": "<PERSON><PERSON><PERSON> thời xóa dữ liệu người dùng.", "Warning:": "Cảnh báo:", "This action is irreversible.": "<PERSON><PERSON><PERSON> động này không thể hoàn tác.", "Type the user's handle below to confirm:": "<PERSON><PERSON><PERSON><PERSON> tên người dùng bên dưới để xác nhận:", "Import Characters": "<PERSON><PERSON><PERSON><PERSON> ký tự", "Enter the URL of the content to import": "Nhập URL củ<PERSON> nội dung cần nhập", "Supported sources:": "<PERSON><PERSON><PERSON> nguồn được hỗ trợ:", "char_import_1": "<PERSON><PERSON><PERSON> vậ<PERSON> (<PERSON><PERSON><PERSON> kết trực tiếp hoặc ID)", "char_import_example": "<PERSON><PERSON> dụ:", "char_import_2": "<PERSON><PERSON> (Nhập URL trực tiếp hoặc ID)", "char_import_3": "JanitorAI (Nhập URL trực tiếp hoặc UUID)", "char_import_4": "Pygmalion.chat (Nhập URL trực tiếp hoặc UUID)", "char_import_5": "AICharacterCards.com (Nhập URL trực tiếp hoặc ID)", "char_import_6": "<PERSON><PERSON><PERSON><PERSON> PNG tr<PERSON><PERSON> tiếp (tham kh<PERSON>o", "char_import_7": "đ<PERSON>i với các máy chủ đư<PERSON>c phép)", "char_import_8": "<PERSON><PERSON>uR<PERSON>m (URL trực tiếp)", "char_import_10": "Perchance (Nhập URL trực tiếp hoặc UUID + .gz)", "Supports importing multiple characters.": "Hỗ trợ nhập nhiều ký tự.", "Write each URL or ID into a new line.": "Viết mỗi URL hoặc ID vào một dòng mới.", "Export for character": "<PERSON><PERSON><PERSON> cho nhân vật", "Export prompts for this character, including their order.": "<PERSON><PERSON>t Prompt cho ký tự này, bao gồ<PERSON> cả thứ tự của chúng.", "Export all": "<PERSON><PERSON><PERSON> tất cả", "Export all your prompts to a file": "<PERSON><PERSON><PERSON> tất cả Prompt c<PERSON><PERSON> b<PERSON><PERSON> sang một tệp", "Insert prompt": "<PERSON><PERSON><PERSON> Prompt", "Delete prompt": "Xóa Prompt", "Import a prompt list": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch Prompt", "Export this prompt list": "<PERSON><PERSON><PERSON> danh s<PERSON>ch Prompt này", "Reset current character": "Đặt lại nhân vật hiện tại", "New prompt": "Prompt mới", "Prompts": "Prompts", "Total Tokens:": "Tổng cộng token:", "prompt_manager_tokens": "<PERSON><PERSON> từ", "Are you sure you want to reset your settings to factory defaults?": "Bạn có chắc chắn muốn đặt lại cài đặt về mặc định ban đầu không?", "Don't forget to save a snapshot of your settings before proceeding.": "<PERSON><PERSON>ng quên sao lưu snapshot của cài đặt của bạn trước khi tiếp tục nha.", "Settings Snapshots": "Cài đặt Snapshot", "Record a snapshot of your current settings.": "<PERSON><PERSON> lại Snapshot của cài đặt hiện tại.", "Make a Snapshot": "<PERSON><PERSON><PERSON> một <PERSON>", "Restore this snapshot": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> n<PERSON>y", "Hi,": "CHÀO,", "To enable multi-account features, restart the SillyTavern server with": "<PERSON><PERSON> bật t<PERSON>h năng đa tà<PERSON>, hãy khởi động lại máy chủ SillyTavern bằng", "set to true in the config.yaml file.": "cách đặt enableUserAccounts thành true trong config.yaml.", "Account Info": "Thông tin tài k<PERSON>n", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "<PERSON><PERSON> thay đổi hình đại diện người dùng của bạn, hã<PERSON> sử dụng các nút bên dưới hoặc chọn nhân vật mặc định trong menu Quản lý Persona.", "Set your custom avatar.": "Đặt avatar tùy chỉnh của bạn.", "Remove your custom avatar.": "<PERSON><PERSON><PERSON> hình avatar c<PERSON><PERSON> b<PERSON>n.", "Handle:": "<PERSON><PERSON> lý:", "This account is password protected.": "<PERSON><PERSON><PERSON> k<PERSON>n nà<PERSON> đư<PERSON> bảo vệ bằng mật khẩu.", "This account is not password protected.": "<PERSON><PERSON><PERSON> k<PERSON>n này không đư<PERSON><PERSON> bảo vệ bằng mật khẩu.", "Account Actions": "<PERSON><PERSON><PERSON> thao tác tài <PERSON>n", "Change Password": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "Manage your settings snapshots.": "<PERSON><PERSON><PERSON><PERSON> lý ảnh snapshots cài đặt của bạn.", "Download a complete backup of your user data.": "<PERSON><PERSON><PERSON> xuống bản sao lưu đầy đủ dữ liệu người dùng của bạn.", "Download Backup": "<PERSON><PERSON><PERSON> xu<PERSON>ng bản sao lưu", "Danger Zone": "<PERSON><PERSON> v<PERSON><PERSON> nguy hiểm", "Reset your settings to factory defaults.": "Đặt lại cài đặt của bạn về mặc định ban đầu.", "Reset Settings": "Đặt lại cài đặt", "Wipe all user data and reset your account to factory settings.": "<PERSON><PERSON><PERSON> toàn bộ dữ liệu người dùng và khôi phục cài đặt gốc cho tài khoản của bạn.", "Reset Everything": "Đặt lại mọi thứ", "Reset Code:": "Đặt lại mã:", "Want to update?": "<PERSON><PERSON><PERSON> cập nhật?", "How to start chatting?": "Bắt đầu chat chit kiểu gì <PERSON>?", "Click _space": "Đúp vào", "and select a": "v<PERSON> ch<PERSON>n ", "Chat API": "Chat API", "and pick a character.": "và chọn nhân vật.", "You can browse a list of bundled characters in the": "<PERSON>ạn có thể xem qua danh sách các nhân vật có sẵn", "Download Extensions & Assets": "<PERSON><PERSON><PERSON> xuống tiện ích mở rộng và Asests", "menu within": "menu bên trong", "Confused or lost?": "<PERSON><PERSON> đầu hay lạc trôi?", "click these icons!": "nhấp vào các biểu tượng này!", "in the chat bar": "trong thanh trò ch<PERSON>n", "SillyTavern Documentation Site": "<PERSON><PERSON><PERSON> l<PERSON> về SillyTavern", "Extras Installation Guide": "<PERSON><PERSON><PERSON>ng dẫn cài đặt bổ sung", "Still have questions?": "Bạn còn thắc mắc?", "Join the SillyTavern Discord": "<PERSON>ham gia <PERSON> c<PERSON><PERSON>", "Post a GitHub issue": "<PERSON><PERSON><PERSON> một vấn đề trên <PERSON>", "Contact the developers": "<PERSON><PERSON><PERSON> v<PERSON><PERSON>"}