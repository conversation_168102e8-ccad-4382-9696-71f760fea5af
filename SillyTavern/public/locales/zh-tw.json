{"Favorite": "我的最愛", "Tag": "標籤", "Duplicate": "複製", "Persona": "使用者角色", "Delete": "刪除", "AI Response Configuration": "AI 回應設定", "AI Configuration panel will stay open": "上鎖 = AI 設定面板將保持開啟", "clickslidertips": "點選滑桿旁的數字以手動輸入。", "MAD LAB MODE ON": "瘋狂實驗室模式", "Documentation on sampling parameters": "取樣參數的說明文件。", "kobldpresets": "Kobold 預設設定檔", "guikoboldaisettings": "GUI KoboldAI 設定", "Update current preset": "更新預設設定檔", "Save preset as": "另存新預設設定檔", "Import preset": "匯入預設設定檔", "Export preset": "匯出預設設定檔", "Restore current preset": "還原目前預設設定檔", "Delete the preset": "刪除預設設定檔", "novelaipresets": "NovelAI 預設設定檔", "Default": "預設", "openaipresets": "聊天補全預設設定檔", "Text Completion presets": "文字補全預設設定檔", "AI Module": "AI 模組", "Changes the style of the generated text.": "變更生成文字的樣式。", "No Module": "無模組", "Instruct": "指示", "Prose Augmenter": "散文增強器", "Text Adventure": "文字冒險", "response legth(tokens)": "回應長度（符元數）", "Streaming": "即時串流", "Streaming_desc": "逐字顯示生成中的回應內容。關閉時，回應將在生成完成後一次性顯示。", "context size(tokens)": "上下文長度（符元數）", "unlocked": "解鎖", "Only enable this if your model supports context sizes greater than 8192 tokens": "僅在您的模型支援超過 8192 個符元的上下文長度時啟用此功能", "Max prompt cost:": "最大提示詞費用：", "Display the response bit by bit as it is generated.": "逐字顯示生成中的回應內容。", "When this is off, responses will be displayed all at once when they are complete.": "關閉時，回應將在生成完成後一次全部顯示。", "Temperature": "溫度", "rep.pen": "重複懲罰", "Rep. Pen. Range.": "重複懲罰範圍", "Rep. Pen. Slope": "重複懲罰斜率", "Rep. Pen. Freq.": "重複懲罰頻率", "Rep. Pen. Presence": "重複懲罰存在", "TFS": "TFS", "Phrase Repetition Penalty": "片語重複懲罰", "Off": "關閉", "Very light": "非常輕", "Light": "輕", "Medium": "中等", "Aggressive": "積極", "Very aggressive": "非常積極", "Unlocked Context Size": "解鎖上下文長度", "Unrestricted maximum value for the context slider": "不限制上下文長度的最大值", "Context Size (tokens)": "上下文長度（符元數）", "Max Response Length (tokens)": "最大回應長度（符元數）", "Multiple swipes per generation": "每次生成多次滑動", "Enable OpenAI completion streaming": "啟用 OpenAI 補全串流", "Frequency Penalty": "頻率懲罰", "Presence Penalty": "存在懲罰", "Count Penalty": "計數懲罰", "Top K": "Top K", "Top P": "Top P", "Repetition Penalty": "重複懲罰", "Min P": "<PERSON>", "Top A": "Top A", "Quick Prompts Edit": "快速提示詞編輯", "Main": "主要提示詞", "NSFW": "NSFW", "Jailbreak": "越獄", "Utility Prompts": "實用提示詞", "Impersonation prompt": "AI 扮演提示詞", "Restore default prompt": "還原預設提示詞", "Prompt that is used for Impersonation function": "用於「AI 扮演使用者」功能的提示詞", "World Info Format Template": "世界資訊格式", "Restore default format": "還原預設格式", "Wraps activated World Info entries before inserting into the prompt.": "在插入提示詞前包裝已啟用的世界資訊條目。", "scenario_format_template_part_1": "使用", "scenario_format_template_part_2": "來標示要插入內容的位置。", "Scenario Format Template": "場景格式", "Personality Format Template": "個性格式", "Group Nudge Prompt Template": "群組聊天格式微調", "Sent at the end of the group chat history to force reply from a specific character.": "在群組聊天歷史結束時傳送以強制特定角色回覆", "New Chat": "新聊天", "Restore new chat prompt": "還原新聊天的提示詞", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "設定在聊天歷史的開頭以表明即將開始新的聊天", "New Group Chat": "新群組聊天", "Restore new group chat prompt": "還原預設群組聊天提示詞", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "設定在聊天歷史的開頭以表明即將開始新的群組聊天", "New Example Chat": "新範例聊天", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "設定在對話範例的開頭以表明即將開始新的範例聊天", "Continue nudge": "繼續輔助微調", "Set at the end of the chat history when the continue button is pressed.": "按下「繼續」按鈕時，插入於聊天歷史的結尾", "Replace empty message": "取代空白訊息", "Send this text instead of nothing when the text box is empty.": "當文字框為空時，傳送此文字以取代空白。", "Seed": "種子", "Set to get deterministic results. Use -1 for random seed.": "設定數值以取得可重現的結果。使用 -1 作為隨機種子", "Temperature controls the randomness in token selection": "溫度（Temperature）控制符元選擇的隨機性。\n- 低溫（<1.0）：產生更可預測且具邏輯性的文字，優先選擇機率較高的符元。\n- 高溫（>1.0）：提升創造性與輸出的多樣性，更常選擇機率較低的符元。\n將值設為 1.0 可使用原始機率。", "Top_K_desc": "Top K 設定可以選擇的最高符元數量。\n例如，Top K 為 20，這意味著只保留排名前 20 的符元（無論它們的機率是多樣還是有限的）。\n設定為 0 以停用。", "Top_P_desc": "Top P(又名核心取樣) 會將所有頂級符元加總，直到達到目標百分比。\n例如，如果前兩個符元都是 25%，而 Top P 設為 0.5，那麼只有前兩個符元會被考慮。\n設定為 1.0 以停用。", "Typical P": "Typical P", "Typical_P_desc": "Typical P 取樣根據符元偏離集合平均熵的程度進行優先排序。\n它會保留累積機率接近預設閾值 (例如 0.5) 的符元，強調那些具有平均資訊量的符元。\n設定為 1.0 以停用。", "Min_P_desc": "Min P 設定基本最小機率。\n這個值會根據最高符元的機率進行調整。例如，如果最高符元機率為 80%,而 Min P 設為 0.1，那麼只有機率高於 8% 的符元會被考慮。\n設定為 0 以停用。", "Top_A_desc": "Top A 根據最高符元機率的平方設定符元選擇的門檻。\n例如，如果 Top A 值為 0.2，而最高符元機率為 50%，那麼低於 5%(0.2 * 0.5^2) 的符元機率就會被排除。\n設定為 0 以停用。", "Tail_Free_Sampling_desc": "無尾取樣 (Tail-Free Sampling, TFS) 會透過分析符元機率的變化率 (使用導數) 來尋找分佈中的低機率符元尾部。\n它會根據標準化的二階導數，保留直到某個閾值 (例如 0.3) 的符元。\n數值越接近 0，表示會棄去越多符元。設定為 1.0 以停用。", "rep.pen range": "重複懲罰範圍", "Mirostat": "Mirostat", "Mode": "模式", "Mirostat_Mode_desc": "0 表示完全停用 Mirostat。1 表示使用 Mirostat 1.0 版本，2 表示使用 Mirostat 2.0 版本。", "Tau": "Tau", "Mirostat_Tau_desc": "這個設定控制了 Mirostat 輸出的可變性。", "Eta": "Eta", "Mirostat_Eta_desc": "這個設定控制 Mirostat 的學習率。", "Ban EOS Token": "禁止 EOS 符元", "Ban_EOS_Token_desc": "對於 KoboldCpp（以及可能也適用於 KoboldAI 的其他符元），禁止使用序列結束 (End-of-Sequence, EOS) 符元。這個設定對於故事創作很有幫助，但不應該在聊天和指令模式中使用。", "GBNF Grammar": "GBNF 語法", "Type in the desired custom grammar": "輸入所需的自定義語法", "Samplers Order": "取樣器順序", "Samplers will be applied in a top-down order. Use with caution.": "取樣器將按從上到下的順序應用。請小心使用。", "Tail Free Sampling": "無尾取樣", "Load koboldcpp order": "載入 koboldcpp 順序", "Preamble": "前言", "Use style tags to modify the writing style of the output.": "使用樣式標籤修改輸出的寫作樣式。", "Banned Tokens": "禁止的符元", "Sequences you don't want to appear in the output. One per line.": "您不希望在輸出中出現的序列。每行一個，使用文字或符元 ID。", "Logit Bias": "Logit 偏差", "Add": "新增", "Helps to ban or reenforce the usage of certain words": "有助於禁止或強化某些符元的使用", "CFG Scale": "CFG 縮放比例", "Negative Prompt": "負面提示詞", "Add text here that would make the AI generate things you don't want in your outputs.": "在此新增文字，以防止 AI 在輸出中生成您不希望出現的內容。", "Used if CFG Scale is unset globally, per chat or character": "若 CFG 縮放比例未被全域設定，它將作用於所有聊天或角色", "Mirostat Tau": "Tau", "Mirostat LR": "Mirostat 學習率", "Min Length": "最小長度", "Top K Sampling": "Top K 取樣", "Nucleus Sampling": "核心取樣", "Top A Sampling": "Top A 取樣", "CFG": "CFG", "Neutralize Samplers": "中和取樣器", "Set all samplers to their neutral/disabled state.": "將所有取樣器設定為中性／停用狀態。", "Sampler Select": "選擇取樣器", "Customize displayed samplers or add custom samplers.": "自訂顯示的取樣器或新增自訂取樣器。", "Epsilon Cutoff": "Epsilon 截斷", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "Epsilon 截斷設定排除符元的機率下限", "Eta Cutoff": "Eta 截斷", "Eta_Cutoff_desc": "Eta 截斷是特殊 Eta 取樣技術的主要參數。\n單位為 1e-4；合理值為 3。\n設為 0 以停用。\n詳細資訊請參見 Hewitt 等人於 2022 年撰寫的論文《Truncation Sampling as Language Model Desmoothing》。", "rep.pen decay": "重複懲罰衰減", "Encoder Rep. Pen.": "編碼器重複懲罰", "No Repeat Ngram Size": "無重複 Ngram 大小", "Skew": "Skew", "Max Tokens Second": "最大符元／秒", "Smooth Sampling": "平滑取樣", "Smooth_Sampling_desc": "允許您使用二次／三次變換來調整分佈。較低的平滑因子值將更具創造性，通常在 0.2-0.3 之間是最佳點（假設曲線=1）。較高的平滑曲線值會使曲線更陡峭，這將更加激烈地懲罰低機率選擇。1.0 的曲線值相當於僅使用平滑因子。", "Smoothing Factor": "平滑因子", "Smoothing Curve": "平滑曲線", "DRY_Repetition_Penalty_desc": "DRY 會懲罰那些將輸入的結尾擴充為已在先前輸入中出現過序列的符元。將乘法器設為 0 以停用。", "DRY Repetition Penalty": "DRY 重複懲罰", "DRY_Multiplier_desc": "將值設為大於 0 來啟用 DRY。控制對最短受懲罰序列的懲罰幅度。", "Multiplier": "乘法器", "DRY_Base_desc": "控制隨著序列長度增加，懲罰增加的速度。", "Base": "基準值", "DRY_Allowed_Length_desc": "可以重複而不受懲罰的最長序列長度。", "Allowed Length": "允許長度", "Penalty Range": "懲罰範圍", "DRY_Sequence_Breakers_desc": "序列停止繼續配對的符元，使用引號包裹字串並以逗號分隔清單。", "Sequence Breakers": "序列中斷器", "JSON-serialized array of strings.": "序列化 JSON 的字串陣列。", "Dynamic Temperature": "動態溫度", "Scale Temperature dynamically per token, based on the variation of probabilities": "根據機率變化，動態調整每個符元的溫度", "Minimum Temp": "最低溫度", "Maximum Temp": "最高溫度", "Exponent": "指數", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat（mode=1 僅適用於 llama.cpp）", "Mirostat_desc": "Mirostat 是輸出困惑度的恆溫器。\nMirostat 會將輸出的困惑度與輸入的困惑度相配對，\n從而避免了重複陷阱（在這個陷阱中，隨著自回歸推理產生字串，輸出的困惑度趨於零）和混亂陷阱（困惑度發散）。\n有關詳細資訊，請參閱 Basu 等人於 2020 年發表的論文《A Neural Text Decoding Algorithm that Directly Controls Perplexity》。\n模式選擇 Mirostat 版本。0=停用，1=Mirostat 1.0（僅限 llama.cpp），2=Mirostat 2.0。", "Mirostat Mode": "Mode", "Variability parameter for Mirostat outputs": "Mirostat 輸出的變異性參數", "Mirostat Eta": "Eta", "Learning rate of Mirostat": "Mirostat 的學習率", "Beam search": "Beam search（波束搜尋）", "Helpful tip coming soon.": "更多有用提示訊息即將推出。", "Number of Beams": "波束數量", "Length Penalty": "長度懲罰", "Early Stopping": "提前停止", "Contrastive search": "對比搜尋", "Contrastive_search_txt": "一種取樣器，透過利用大多數 LLM 的表示空間的等向性，鼓勵多樣性的同時保持一致性。詳細資訊請參閱 Su 等人於 2022 年發表的論文《A Contrastive Framework for Neural Text Generation》。", "Penalty Alpha": "懲罰 Alpha", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "對比搜尋正則化項的強度。設定為 0 以停用 CS", "Do Sample": "進行取樣", "Add BOS Token": "新增 BOS 符元", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "在提示詞的開頭新增 bos_token。停用此功能可以使回應更具創造性", "Ban the eos_token. This forces the model to never end the generation prematurely": "禁止 eos_token。這迫使模型不會提前結束生成", "Ignore EOS Token": "忽略 EOS 符元", "Ignore the EOS Token even if it generates.": "即使生成也忽略 EOS 符元", "Skip Special Tokens": "跳過特殊符元", "Temperature Last": "最後的溫度", "Temperature_Last_desc": "使用最後應用溫度取樣器。這幾乎總是明智的做法。\n啟用時：首先取樣一組合理的符元，然後應用溫度來調整它們的相對機率（技術上講，是 logits）。\n停用時：首先應用溫度調整所有符元的相對機率，然後從中取樣合理的符元。\n停用「最後應用溫度取樣」會增加分佈尾部的機率，這傾向於放大獲得不連貫回應的機會。", "Speculative Ngram": "推測性 Ngram", "Use a different speculative decoding method without a draft model": "使用不含草稿模型的不同推測性解碼方法。", "Spaces Between Special Tokens": "特殊符元之間的空格", "LLaMA / Mistral / Yi models only": "僅限 LLaMA／<PERSON><PERSON><PERSON>／<PERSON> 模型", "Example: some text [42, 69, 1337]": "範例：\n一些文字 [42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "無分類器導引。更多有用提示訊息即將推出", "Scale": "比例", "JSON Schema": "JSON 結構", "Type in the desired JSON schema": "輸入所需的 JSON 結構", "Grammar String": "語法字串", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF 或 EBNF，取決於所使用的後端。如果您使用此功能，應該知道是哪一種", "Top P & Min P": "Top P 和 Min P", "Load default order": "載入預設順序", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "僅適用於 llama.cpp。決定取樣器的順序。如果 Mirostat 模式不為 0，則忽略取樣器順序。", "Sampler Priority": "取樣器優先順序", "Ooba only. Determines the order of samplers.": "僅適用於 O<PERSON><PERSON>。決定取樣器的順序。", "Character Names Behavior": "角色名稱行為", "Helps the model to associate messages with characters.": "幫助模型將訊息與角色關聯起來。", "None": "無", "character_names_default": "除了團體和過去的使用者角色外。否則，請確保在提示中提供名字。", "Don't add character names.": "不要新增角色名稱", "Completion": "補全", "character_names_completion": "字元限制：僅限拉丁字母數字和底線。不適用於所有來源，特別是：Claude、MistralAI、Google。", "Add character names to completion objects.": "新增角色名稱來補充物件", "Message Content": "訊息內容", "Prepend character names to message contents.": "在訊息內容前新增角色名稱", "Continue Postfix": "繼續後綴", "The next chunk of the continued message will be appended using this as a separator.": "繼續訊息的下一塊將使用此作為分隔符號附加", "Space": "空格", "Newline": "換行", "Double Newline": "雙換行", "Wrap user messages in quotes before sending": "傳送前將使用者訊息用引號括起來", "Wrap in Quotes": "用引號包裹", "Wrap entire user message in quotes before sending.": "在傳送之前將整個使用者訊息用引號包裹。", "Leave off if you use quotes manually for speech.": "如果您手動使用引號進行發言，請關閉。", "Continue prefill": "繼續預先填充", "Continue sends the last message as assistant role instead of system message with instruction.": "繼續將最後的訊息作為助理角色傳送，而不是帶有指令的系統訊息。", "Squash system messages": "合併系統訊息", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "將連續的系統訊息合併為一個（不包括對話範例）。可能會提高某些模型的一致性。", "Enable function calling": "啟用函式呼叫", "Send inline images": "傳送內嵌圖片", "image_inlining_hint_1": "如果模型支援，則在提示詞中傳送圖片。\n使用任何訊息上的", "image_inlining_hint_2": "動作或", "image_inlining_hint_3": "選單來附加圖片文件到聊天中。", "Inline Image Quality": "內嵌圖片品質", "openai_inline_image_quality_auto": "自動", "openai_inline_image_quality_low": "低", "openai_inline_image_quality_high": "高", "Use AI21 Tokenizer": "使用 AI21 分詞器", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "對於 Jurassic 模型使用適當的分詞器，比 GPT 的更高效", "Use Google Tokenizer": "使用 Google 分詞器", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "透過 Google 模型的 API 使用適當的分詞器。提示詞處理速度較慢，但提供更準確的符元計數。", "Use system prompt": "使用系統提示詞", "(Gemini 1.5 Pro/Flash only)": "（僅限於 Gemini 1.5 Pro/Flash）", "Merges_all_system_messages_desc_1": "合併所有系統訊息，直到第一則非系統角色的訊息，並透過 google 的", "Merges_all_system_messages_desc_2": "欄位傳送，而不是與其餘提示詞內容一起傳送。", "Assistant Prefill": "預先填充助理訊息", "Start Claude's answer with...": "開始 Claude 的回答⋯", "Assistant Impersonation Prefill": "助理扮演時的預先填充", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "為支援的模型傳送系統提示詞。停用時，使用者訊息將新增到提示詞的開頭。", "User first message": "使用者第一則訊息", "Restore User first message": "還原使用者第一則訊息", "Human message": "人類訊息、指令等。\n當空白時不加入任何內容，也就是需要一個帶有使用者角色的新提示詞。", "New preset": "新預設設定檔", "Delete preset": "刪除預設設定檔", "View / Edit bias preset": "檢視／編輯 Bias 預設設定檔", "Add bias entry": "新增 Bias 條目", "Most tokens have a leading space.": "大多數符元有前導空格", "API Connections": "API 連線", "Text Completion": "文字補全", "Chat Completion": "聊天補全", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "避免傳送敏感資訊到 Horde。", "Review the Privacy statement": "檢視隱私聲明", "Register a Horde account for faster queue times": "註冊 Horde 帳號以縮短等待時間", "Learn how to contribute your idle GPU cycles to the Horde": "了解如何將閒置的 GPU 週期貢獻給 Horde", "Adjust context size to worker capabilities": "根據 worker 的能力調整上下文長度", "Adjust response length to worker capabilities": "根據 worker 的能力調整回應長度", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "僅將已批准的 worker 排隊，可以幫助處理不良回應。可能會延長回應時間。", "Trusted workers only": "僅限受信任的 worker", "API key": "API 金鑰", "Get it here:": "在這裡取得：", "Register": "註冊", "View my Kudos": "瀏覽我的讚賞記錄", "Enter": "輸入", "to use anonymous mode.": "以使用匿名模式。", "Clear your API key": "清除您的 API 金鑰", "For privacy reasons, your API key will be hidden after you reload the page.": "基於安全性考量，重新載入頁面後，您的 API 金鑰將被隱藏。", "Models": "模型", "Refresh models": "重新整理模型", "-- Horde models not loaded --": "-- Horde 模型未載入 --", "Not connected...": "尚未連線⋯", "API url": "API URL", "Example: http://127.0.0.1:5000/api ": "範例：http://127.0.0.1:5000/api ", "Connect": "連線", "Cancel": "關閉", "Novel API key": "NovelAI API 金鑰", "Get your NovelAI API Key": "取得您的 NovelAI API 金鑰", "Enter it in the box below": "請在下面的框中輸入", "Novel AI Model": "NovelAI 模型", "No connection...": "沒有連線⋯", "API Type": "API 類型", "Default (completions compatible)": "預設（相容補全）", "TogetherAI API Key": "TogetherAI API 金鑰", "TogetherAI Model": "TogetherAI 模型", "-- Connect to the API --": "-- 連線到 API --", "OpenRouter API Key": "OpenRouter API 金鑰", "Click Authorize below or get the key from": "點選下方的授權或從以下取得金鑰", "View Remaining Credits": "檢視剩餘點數", "OpenRouter Model": "OpenRouter 模型", "Model Providers": "模型供應商", "InfermaticAI API Key": "InfermaticAI API 金鑰", "InfermaticAI Model": "InfermaticAI 模型", "DreamGen API key": "DreamGen API 金鑰", "DreamGen Model": "DreamGen 模型", "Mancer API key": "Mancer API 金鑰", "Mancer Model": "<PERSON><PERSON> 模型", "Make sure you run it with": "確保您使用以下方式執行它", "flag": "旗標", "API key (optional)": "API 金鑰（可選）", "Server url": "伺服器 URL", "Example: http://127.0.0.1:5000": "範例：http://127.0.0.1:5000", "Custom model (optional)": "自訂模型（選填）", "vllm-project/vllm": "vllm-project/vllm", "vLLM API key": "vLLM API 金鑰", "Example: http://127.0.0.1:8000": "範例：http://127.0.0.1:8000", "vLLM Model": "vLLM 模型", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite 引擎", "Aphrodite API key": "Aphrodite API 金鑰", "Aphrodite Model": "Aphrodite 模型", "ggerganov/llama.cpp": "ggerganov/llama.cpp", "Example: http://127.0.0.1:8080": "範例：http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "範例：http://127.0.0.1:11434", "Ollama Model": "Ollama 模型", "Download": "下載", "Tabby API key": "Tabby API 金鑰", "koboldcpp API key (optional)": "KoboldCpp API 金鑰（可選）", "Example: http://127.0.0.1:5001": "範例：http://127.0.0.1:5001", "Authorize": "授權", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "使用 OAuth 流程取得您的 OpenRouter API 符元。您將被重新導向到 openrouter.ai", "Bypass status check": "繞過狀態檢查", "Chat Completion Source": "聊天補全來源", "Reverse Proxy": "反向代理伺服器", "Proxy Presets": "代理伺服器預設設定檔", "Saved addresses and passwords.": "已儲存的地址和密碼", "Save Proxy": "儲存代理伺服器", "Delete Proxy": "刪除代理伺服器", "Proxy Name": "代理伺服器名稱", "This will show up as your saved preset.": "這將顯示為您儲存的預設設定檔", "Proxy Server URL": "代理伺服器 URL", "Alternative server URL (leave empty to use the default value).": "替代伺服器 URL（留空以使用預設值）。", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "在此框中輸入任何內容之前，從 API 面板中刪除您的實際 OAI API 金鑰", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "我們無法為使用非官方 OpenAI 代理伺服器時遇到的問題提供支援", "Doesn't work? Try adding": "不起作用？嘗試新增", "at the end!": "在 URL 結尾！", "Proxy Password": "代理伺服器密碼", "Will be used as a password for the proxy instead of API key.": "將用作代理的密碼，而不是 API 金鑰", "Peek a password": "顯示密碼", "OpenAI API key": "OpenAI API 金鑰", "View API Usage Metrics": "檢視 API 使用指標", "Follow": "遵循", "these directions": "這些指示", "to get your OpenAI API key.": "以取得您的 OpenAI API 金鑰。", "Use Proxy password field instead. This input will be ignored.": "請改用代理密碼欄位。此輸入將被忽略", "OpenAI Model": "OpenAI 模型", "Bypass API status check": "繞過 API 狀態檢查", "Show External models (provided by API)": "顯示外部模型（由 API 提供）", "Get your key from": "取得您的鑰匙", "Anthropic's developer console": "Anthropic 的開發者控制台", "Claude Model": "<PERSON> 模型", "Window AI Model": "Window AI 模型", "Model Order": "模型順序", "Alphabetically": "按字母順序", "Price": "價格（最便宜的）", "Context Size": "上下文長度", "Group by vendors": "按供應商分組", "Group by vendors Description": "將 OpenAI、Anthropic 等模型放各自供應商的群組中。可以與排序功能結合使用。", "Allow fallback routes": "允許備援路徑", "Allow fallback routes Description": "如果選擇的模型無法滿足要求，會自動選擇替代模型。", "AI21 API Key": "AI21 API 金鑰", "AI21 Model": "AI21 模型", "Google AI Studio API Key": "Google AI Studio API 金鑰", "Google Model": "Google 模型", "MistralAI API Key": "MistralAI API 金鑰", "MistralAI Model": "MistralAI 模型", "Groq API Key": "Groq API 金鑰", "Groq Model": "Groq 模型", "Perplexity API Key": "Perplexity API 金鑰", "Perplexity Model": "Perplexity 模型", "Cohere API Key": "Cohere API 金鑰", "Cohere Model": "Cohere 模型", "Custom Endpoint (Base URL)": "自訂端點（Base URL）", "Custom API Key": "自訂 API 金鑰", "Available Models": "可用模型", "Prompt Post-Processing": "提示詞後處理", "Applies additional processing to the prompt before sending it to the API.": "這個選項會在將提示詞送往 API 之前，對它進行額外的處理。", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "透過傳送簡短的測試訊息來驗證您的 API 連線。請注意，您將因此獲得榮譽！", "Test Message": "測試訊息", "Auto-connect to Last Server": "自動連線至上次使用的伺服器", "Missing key": "❌ 鑰匙遺失", "Key saved": "✔️ 金鑰已儲存", "View hidden API keys": "檢視隱藏的 API 金鑰", "AI Response Formatting": "AI 回應進階格式化", "Advanced Formatting": "進階格式化", "Context Template": "上下文範本", "Auto-select this preset for Instruct Mode": "自動選擇此預設設定檔用於指令模式", "Story String": "故事字串", "Example Separator": "分隔符號範例", "Chat Start": "聊天開始符號", "Add Chat Start and Example Separator to a list of stopping strings.": "將聊天開始和範例分隔符號加入終止字串中。", "Use as Stop Strings": "用作停止字串", "Allow Jailbreak": "允許越獄", "Context Order": "上下文順序", "Summary": "摘要", "Author's Note": "作者備註", "Example Dialogues": "對話範例", "Hint": "提示訊息", "In-Chat Position not affected": "摘要和作者的注釋順序僅在未設定聊天中位置時受影響。", "Instruct Mode": "指示模式", "Enabled": "啟用", "instruct_bind_to_context": "啟用後，系統將根據所選擇的指令範本名稱或偏好，自動選取對應的上下文範本。", "Bind to Context": "附加到上下文", "Presets": "預設設定檔", "Auto-select this preset on API connection": "在 API 連線時自動選擇此預設", "Activation Regex": "啟用正規表示式", "Wrap Sequences with Newline": "用換行符包裹序列", "Replace Macro in Sequences": "取代序列中的巨集", "Skip Example Dialogues Formatting": "跳過對話範例的格式設定", "Include Names": "包含名稱", "Force for Groups and Personas": "強制用於群組和使用者角色", "System Prompt": "系統提示詞", "Instruct Mode Sequences": "指示模式序列", "System Prompt Wrapping": "系統提示詞換行", "Inserted before a System prompt.": "插入在系統提示詞之前。", "System Prompt Prefix": "系統提示詞前綴", "Inserted after a System prompt.": "插入在系統提示詞之後。", "System Prompt Suffix": "系統提示詞後綴", "Chat Messages Wrapping": "聊天訊息換行", "Inserted before a User message and as a last prompt line when impersonating.": "插入在使用者訊息之前，並在扮演使用者時作為最後一行提示詞。", "User Message Prefix": "使用者訊息前綴", "Inserted after a User message.": "插入在使用者提示詞之後。", "User Message Suffix": "使用者訊息後綴", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "插入在助理訊息之前，並在生成 AI 回覆時作為最後一行提示詞。", "Assistant Message Prefix": "助理訊息前綴", "Inserted after an Assistant message.": "插入在助理訊息之後。", "Assistant Message Suffix": "助理訊息後綴", "Inserted before a System (added by slash commands or extensions) message.": "插入在系統（透過斜線命令或擴充功能增加）訊息之前。", "System Message Prefix": "系統訊息前綴", "Inserted after a System message.": "插入在系統訊息之後", "System Message Suffix": "系統訊息後綴", "If enabled, System Sequences will be the same as User Sequences.": "啟用後，系統序列將與使用者序列相同。", "System same as User": "系統與使用者相同", "Misc. Sequences": "其他序列", "Inserted before the first Assistant's message.": "插入在第一則助理訊息之前。", "First Assistant Prefix": "開頭助理前綴", "instruct_last_output_sequence": "插入在最後一則助理訊息之前，或在生成 AI 回覆時作為最後一行提示詞（除了中立／系統角色）。", "Last Assistant Prefix": "結尾助理前綴", "Will be inserted as a last prompt line when using system/neutral generation.": "在使用系統／中立生成時作為最後一行提示詞插入。", "System Instruction Prefix": "系統指令前綴", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "如果生成了停止序列，包括該序列以及之後的所有內容將從輸出中刪除。", "Stop Sequence": "停止序列", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "如果聊天歷史不是以使用者訊息開始，將在聊天歷史的開頭插入。", "User Filler Message": "使用者填充訊息", "Context Formatting": "上下文格式", "(Saved to Context Template)": "（已儲存到上下文範本）", "Always add character's name to prompt": "總是將角色名稱新增到提示詞中", "Generate only one line per request": "每次請求僅生成一行", "Trim Incomplete Sentences": "修剪不完整的句子", "Include Newline": "包含換行符號", "Misc. Settings": "其他設定", "Collapse Consecutive Newlines": "折疊連續的換行符號", "Trim spaces": "修剪空格", "Tokenizer": "分詞器 Tokenizer", "Token Padding": "符元填充", "Start Reply With": "開始回覆", "AI reply prefix": "AI 回覆前綴", "Show reply prefix in chat": "在聊天中顯示回覆前綴", "Non-markdown strings": "非 Markdown 字串", "separate with commas w/o space between": "用逗號分隔，之間無空格", "Custom Stopping Strings": "自訂停止字串", "JSON serialized array of strings": "JSON 序列化字串陣列", "Replace Macro in Stop Strings": "取代自訂停止字串中的巨集", "Auto-Continue": "自動繼續", "Allow for Chat Completion APIs": "允許聊天補全 API", "Target length (tokens)": "目標長度（符元）", "World Info": "世界資訊", "Locked = World Editor will stay open": "上鎖 = 世界編輯器將保持開啟", "Worlds/Lorebooks": "世界／知識書", "Active World(s) for all chats": "所有聊天啟用中的世界書", "-- World Info not found --": "-- 未找到世界資訊 --", "Global World Info/Lorebook activation settings": "全域世界資訊／知識書啟動設定", "Click to expand": "點選展開", "Scan Depth": "掃描深度", "Context %": "上下文百分比", "Budget Cap": "預算上限", "(0 = disabled)": "（0 = 停用）", "Scan chronologically until reached min entries or token budget.": "按時間順序掃描直到達到最小條目或符元預算", "Min Activations": "最小啟動次數", "Max Depth": "最大深度", "(0 = unlimited, use budget)": "（0 = 無限制，使用預算）", "Insertion Strategy": "插入策略", "Sorted Evenly": "均等排序", "Character Lore First": "角色知識書優先", "Global Lore First": "全域知識書優先", "Entries can activate other entries by mentioning their keywords": "條目可以透過提及其關鍵字來啟用其他條目", "Recursive Scan": "遞迴掃描", "Lookup for the entry keys in the context will respect the case": "在上下文中查詢條目關鍵字將區分大小寫", "Case Sensitive": "區分大小寫", "If the entry key consists of only one word, it would not be matched as part of other words": "如果條目關鍵字僅包含一個詞，則不會作為其他詞的一部分進行配對", "Match Whole Words": "完全配對", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "只有符合最多鍵值數量的條目將被選中進行包含群組過濾", "Use Group Scoring": "使用群組評分", "Alert if your world info is greater than the allocated budget.": "如果您的世界資訊超過分配的預算則提醒", "Alert On Overflow": "溢位時警告", "New": "新增", "or": "或", "--- Pick to Edit ---": "--- 選擇編輯 ---", "Rename World Info": "重新命名世界資訊", "Open all Entries": "展開所有條目", "Close all Entries": "收合所有條目", "New Entry": "新條目", "Fill empty Memo/Titles with Keywords": "用關鍵字填充空的備忘錄／標題", "Import World Info": "匯入世界資訊", "Export World Info": "匯出世界資訊", "Duplicate World Info": "複製世界資訊", "Delete World Info": "刪除世界資訊", "Search...": "搜尋⋯", "Search": "搜尋", "Priority": "優先順序", "Custom": "自訂", "Title A-Z": "標題 A-Z", "Title Z-A": "標題 Z-A", "Tokens ↗": "符元 ↗", "Tokens ↘": "符元 ↘", "Depth ↗": "深度 ↗", "Depth ↘": "深度 ↘", "Order ↗": "順序 ↗", "Order ↘": "順序 ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "觸發% ↗", "Trigger% ↘": "觸發% ↘", "Refresh": "重新整理", "User Settings": "使用者設定", "Simple": "簡單", "Advanced": "進階", "UI Language": "介面語言：", "Account": "帳號", "Admin Panel": "管理面板", "Logout": "登出", "Search Settings": "搜尋設定", "UI Theme": "介面主題", "Import a theme file": "匯入主題檔", "Export a theme file": "匯出主題檔", "Delete a theme": "刪除主題", "Update a theme file": "更新主題檔", "Save as a new theme": "另存為新主題", "Avatar Style:": "頭像樣式", "Circle": "圓形", "Square": "方形", "Rectangle": "矩形", "Chat Style:": "對話框樣式：", "Flat": "扁平", "Bubbles": "氣泡", "Document": "文件", "Specify colors for your theme.": "為您的介面主題指定顏色", "Theme Colors": "介面主題顏色", "Main Text": "主要文字", "Italics Text": "斜體文字", "Underlined Text": "帶底線的文字", "Quote Text": "引用文字", "Shadow Color": "陰影顏色", "Chat Background": "聊天背景顏色", "UI Background": "介面背景顏色", "UI Border": "介面邊框顏色", "User Message Blur Tint": "使用者訊息模糊色調", "AI Message Blur Tint": "AI 訊息模糊色調", "Chat Width": "對話框寬度", "Width of the main chat window in % of screen width": "主聊天視窗寬度佔螢幕寬度的百分比", "Font Scale": "字型比例", "Font size": "字型大小", "Blur Strength": "模糊強度", "Blur strength on UI panels.": "UI 面板上的模糊強度", "Text Shadow Width": "文字陰影寬度", "Strength of the text shadows": "文字陰影的強度", "Disables animations and transitions": "停用動畫和過渡效果", "Reduced Motion": "停用動畫過渡效果", "removes blur from window backgrounds": "從視窗背景中移除模糊效果，以加快算繪速度。", "No Blur Effect": "停用模糊效果", "Remove text shadow effect": "移除文字陰影效果。", "No Text Shadows": "停用文字陰影效果", "Reduce chat height, and put a static sprite behind the chat window": "減少聊天高度，並在聊天視窗後放置一個靜態 Sprite。", "Waifu Mode": "視覺小說模式", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "始終顯示聊天訊息的訊息動作上下文項目的完整列表，而不是將它們隱藏在「⋯」後面。", "Auto-Expand Message Actions": "展開訊息快速編輯選單", "Alternative UI for numeric sampling parameters with fewer steps": "數值取樣參數的替代 UI，步驟更少", "Zen Sliders": "Zen 滑桿", "Entirely unrestrict all numeric sampling parameters": "完全解除所有數值取樣參數的限制。", "Mad Lab Mode": "瘋狂實驗室模式", "Time the AI's message generation, and show the duration in the chat log": "計時 AI 的訊息生成，並在聊天記錄中顯示持續時間。", "Message Timer": "訊息計時器", "Show a timestamp for each message in the chat log": "在聊天記錄中為每條訊息顯示時間戳記。", "Chat Timestamps": "對話框時間戳記", "Show an icon for the API that generated the message": "顯示生成訊息的 API 圖示。", "Model Icon": "顯示模型圖示", "Show sequential message numbers in the chat log": "在聊天記錄中顯示連續的訊息編號。", "Message IDs": "顯示訊息 ID", "Hide avatars in chat messages.": "在聊天訊息中隱藏頭像", "Hide Chat Avatars": "隱藏聊天頭像", "Show the number of tokens in each message in the chat log": "在聊天記錄中顯示每條訊息中的符元數量", "Show Message Token Count": "顯示訊息符元數", "Single-row message input area. Mobile only, no effect on PC": "將聊天訊息輸入欄預設為單行顯示。僅適用於行動版，電腦版無效。", "Compact Input Area (Mobile)": "緊湊的聊天訊息輸入欄（行動版）", "In the Character Management panel, show quick selection buttons for favorited characters": "在角色管理面板中，顯示加入到最愛角色的快速選擇按鈕。", "Characters Hotswap": "角色卡快捷選單", "Enable magnification for zoomed avatar display.": "啟用放大功能檢視頭像。", "Avatar Hover Magnification": "頭像懸停時的放大倍數", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "當您點選訊息中的角色頭像後，將啟用滑鼠懸停時的放大效果。", "Show tagged character folders in the character list": "在角色列表中顯示標籤角色資料夾。", "Tags as Folders": "標籤作為資料夾", "Tags_as_Folders_desc": "標籤必須在「標籤管理」選單中標記為資料夾才可適用。點選這裡開啟。", "Character Handling": "角色處理", "If set in the advanced character definitions, this field will be displayed in the characters list.": "如果在進階角色定義中設定，這個欄位將顯示在角色清單中。", "Char List Subheader": "角色列表子標題", "Character Version": "角色版本", "Created by": "創作者", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "使用模糊配對，並透過所有資料欄位在列表中搜尋角色，而不僅僅是透過名稱子字串。", "Advanced Character Search": "進階角色搜尋", "If checked and the character card contains a prompt override (System Prompt), use that instead": "如果選中並且角色卡包含提示詞覆寫（系統提示詞），則使用該提示詞。", "Prefer Character Card Prompt": "角色卡主要提示詞優先", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "如果選中並且角色卡包含越獄覆寫（聊天歷史後指示），則使用該提示詞。", "Prefer Character Card Jailbreak": "角色卡越獄優先", "never_resize_avatars_tooltip": "避免裁剪與調整匯入的角色頭像大小。未啟用此選項時，圖片將被裁剪／調整為 512x768。此設定會關閉上傳頭像時的裁剪彈出視窗。", "Never resize avatars": "永不調整頭像大小", "Show actual file names on the disk, in the characters list display only": "僅在角色列表顯示實際檔案名稱。", "Show avatar filenames": "顯示頭像檔案名稱", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "在角色匯入時提示詞匯入嵌入的卡片標籤。否則，嵌入的標籤將被忽略。", "Import Card Tags": "匯入卡片中的標籤", "Hide character definitions from the editor panel behind a spoiler button": "在編輯器面板中將角色定義隱藏在劇透按鈕後面。", "Spoiler Free Mode": "無劇透模式", "Miscellaneous": "其他", "Reload and redraw the currently open chat": "重新載入並重繪目前開啟的聊天", "Reload Chat": "重新載入聊天", "Debug Menu": "偵錯選單", "Smooth Streaming": "平滑串流", "Experimental feature. May not work for all backends.": "實驗性功能。可能不適用於所有後端", "Slow": "慢", "Fast": "快", "Play a sound when a message generation finishes": "訊息生成完成時播放音效。", "Message Sound": "訊息音效", "Only play a sound when ST's browser tab is unfocused": "僅在 ST 的瀏覽器分頁未聚焦時播放音效。", "Background Sound Only": "僅作為背景音效", "Reduce the formatting requirements on API URLs": "降低 API URL 的格式要求。", "Relaxed API URLS": "寬鬆的 API URL 格式", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "當新角色含有知識書時，詢問是否要匯入嵌入的世界資訊／知識書。如果未選中，則會顯示簡短的訊息。", "Lorebook Import Dialog": "匯入知識書對話框", "Restore unsaved user input on page refresh": "在頁面重新整理時還原未儲存的使用者輸入。", "Restore User Input": "還原使用者輸入", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "允許透過拖動重新定位某些 UI 元素。僅適用於 PC 版。", "Movable UI Panels": "可拖動的 UI 模式", "MovingUI preset. Predefined/saved draggable positions": "MovingUI 預設設定檔。預先定義／儲存可拖動位置。", "MUI Preset": "MovingUI 預設設定檔", "Save movingUI changes to a new file": "另存 MovingUI 變更為新檔案", "Reset MovingUI panel sizes/locations.": "重設 MovingUI 面板大小／位置", "Apply a custom CSS style to all of the ST GUI": "將自訂 CSS 樣式應用於所有 SillyTavern 介面", "Custom CSS": "自訂 CSS 樣式", "Expand the editor": "展開編輯器", "Chat/Message Handling": "聊天／訊息處理", "# Messages to Load": "每頁載入的訊息數", "The number of chat history messages to load before pagination.": "每頁載入的聊天歷史訊息數", "(0 = All)": "（0 = 全部）", "Streaming FPS": "串流 FPS", "Update speed of streamed text.": "更新串流文字的速度", "Example Messages Behavior": "訊息行為範例：", "Gradual push-out": "逐步推出", "Always include examples": "總是包含範例", "Never include examples": "永不包含範例", "Send on Enter": "按下 Enter 鍵傳送：", "Disabled": "停用", "Automatic (PC)": "自動（PC）", "Press Send to continue": "按下傳送繼續", "Show a button in the input area to ask the AI to continue (extend) its last message": "在輸入區域顯示一個按鈕，請求 AI 繼續（擴充）其最後一則訊息", "Quick 'Continue' button": "快速「繼續回應」按鈕", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "在最後的聊天訊息中顯示箭頭按鈕，以生成替代的 AI 回應。適用於 PC 和行動版", "Swipes": "滑動", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "允許在最後的聊天訊息上使用滑動手勢以觸發滑動生成。僅適用於行動版。", "Gestures": "手勢", "Auto-load Last Chat": "自動載入最後的聊天", "Auto-scroll Chat": "自動滾動聊天", "Save edits to messages without confirmation as you type": "在輸入時自動儲存訊息的編輯。", "Auto-save Message Edits": "自動儲存編輯的訊息", "Confirm message deletion": "刪除訊息前確認", "Auto-fix Markdown": "自動修正 Markdown", "Disallow embedded media from other domains in chat messages": "禁止在聊天訊息中嵌入來自其他領域的媒體。", "Forbid External Media": "禁止使用外部媒體", "Allow {{char}}: in bot messages": "允許機器人訊息中使用 {{char}}:", "Allow {{user}}: in bot messages": "允許機器人訊息中使用 {{user}}:", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "跳過編碼訊息文字中的 < 和 > 字元，允許一部分 HTML 標記以及 Markdown", "Show tags in responses": "在回應中顯示標籤", "Allow AI messages in groups to contain lines spoken by other group members": "允許群組中的 AI 訊息包含其他群組成員說的話", "Relax message trim in Groups": "放寬群組中的訊息修剪", "Log prompts to console": "將提示詞記錄到控制台", "Requests logprobs from the API for the Token Probabilities feature": "從 API 請求 logprobs 用於符元機率功能。", "Request token probabilities": "請求符元機率", "Automatically reject and re-generate AI message based on configurable criteria": "根據可設定標準自動拒絕並重新生成 AI 訊息。", "Auto-swipe": "自動滑動", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "啟用自動滑動功能。此部分的設定僅在啟用自動滑動時有效。", "Minimum generated message length": "生成訊息的最小長度", "If the generated message is shorter than these many characters, trigger an auto-swipe": "如果生成的訊息比這個短，將觸發自動滑動。", "Blacklisted words": "黑名單詞語", "words you dont want generated separated by comma ','": "您不想生成的文字，使用逗號分隔", "Blacklisted word count to swipe": "滑動的黑名單詞語數量", "Minimum number of blacklisted words detected to trigger an auto-swipe": "偵測到的黑名單詞語數量觸發自動滑動的最小值。", "AutoComplete Settings": "自動完成設定", "Automatically hide details": "自動隱藏詳細資訊", "Determines how entries are found for autocomplete.": "決定如何找到自動完成的條目", "Autocomplete Matching": "自動完成配對", "Starts with": "開始於", "Includes": "包含", "Fuzzy": "模糊", "Sets the style of the autocomplete.": "設定自動完成的樣式", "Autocomplete Style": "自動完成樣式", "Follow Theme": "沿用介面主題", "Dark": "深色", "Sets the font size of the autocomplete.": "設定自動完成的字型大小", "Sets the width of the autocomplete.": "設定自動完成的寬度", "Autocomplete Width": "自動完成寬度", "chat input box": "聊天輸入框", "entire chat width": "整個聊天寬度", "full window width": "全視窗寬度", "STscript Settings": "STscript 設定", "Sets default flags for the STscript parser.": "設定 STscript 解析器的預設象徵", "Parser Flags": "解析器象徵", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "切換到更嚴格的字元跳脫，允許所有分隔符號使用反斜線跳脫，反斜線自己也可以跳脫。", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "將所有 {{getvar::}} 和 {{getglobalvar::}} 巨集取代為區域變數以避免雙重巨集取代", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "變更背景圖片", "Filter": "篩選", "Automatically select a background based on the chat context": "根據聊天上下文自動選擇背景", "Auto-select": "自動選擇", "System Backgrounds": "系統背景圖片", "Chat Backgrounds": "聊天背景圖片", "bg_chat_hint_1": "使用擴充功能", "bg_chat_hint_2": "所產生的背景圖片將在此顯示。", "Extensions": "擴充功能", "Notify on extension updates": "擴充功能更新通知", "Manage extensions": "管理擴充功能", "Import Extension From Git Repo": "從 Git 倉庫匯入擴充功能", "Install extension": "安裝擴充功能", "Extras API:": "擴充功能 API：", "Auto-connect": "自動連線", "Extras API URL": "擴充功能 API URL", "Extras API key (optional)": "擴充功能 API 金鑰（選填）", "Persona Management": "使用者角色管理", "How do I use this?": "我該如何使用這個？", "Click for stats!": "點選以檢視統計資料！", "Usage Stats": "統計資料", "Backup your personas to a file": "備份您的使用者角色檔案", "Backup": "備份", "Restore your personas from a file": "還原您的使用者角色檔案", "Restore": "還原", "Create a dummy persona": "建立一個虛構使用者角色", "Create": "建立", "Toggle grid view": "切換為網格檢視", "No persona description": "無使用者角色描述", "Name": "名稱", "Enter your name": "輸入您的名字", "Click to set a new User Name": "設定新的使用者名稱", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "綁定目前所選的使用者角色至本次聊天。再次點選將移除綁定。", "Click to set user name for all messages": "設定所有訊息的使用者名稱", "Persona Description": "使用者角色描述", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "範例：[{{user}} 是一個 28 歲的羅馬尼亞貓娘。]", "Tokens persona description": "角色描述符元數", "Position:": "插入位置：", "In Story String / Prompt Manager": "提示詞管理器／故事字串中", "Top of Author's Note": "作者備註的頂端", "Bottom of Author's Note": "作者備註的底端", "In-chat @ Depth": "聊天中 @ 深度", "Depth:": "深度：", "Role:": "角色：", "System": "系統", "User": "使用者", "Assistant": "助理", "Show notifications on switching personas": "切換角色時顯示通知", "Character Management": "角色管理", "Locked = Character Management panel will stay open": "上鎖 = 角色管理面板將保持開啟", "Select/Create Characters": "選擇／建立角色", "Favorite characters to add them to HotSwaps": "將角色加入到最愛來新增到快速切換", "Token counts may be inaccurate and provided just for reference.": "符元計數可能不準確，僅供參考。", "Total tokens": "符元總計", "Calculating...": "計算中⋯", "Tokens": "符元", "Permanent tokens": "永久符元", "Permanent": "永久", "About Token 'Limits'": "關於符元數「限制」", "Toggle character info panel": "切換角色資訊面板", "Name this character": "為此角色命名", "extension_token_counter": "符元：", "Click to select a new avatar for this character": "點選以選擇此角色的新頭像", "Add to Favorites": "新增至我的最愛", "Advanced Definition": "進階定義", "Character Lore": "角色知識書", "Chat Lore": "聊天知識書", "Export and Download": "匯出和下載", "Duplicate Character": "複製角色", "Create Character": "建立角色", "Delete Character": "刪除角色", "More...": "更多⋯", "Link to World Info": "連結到世界資訊", "Import Card Lore": "匯入角色卡知識書", "Scenario Override": "場景覆寫", "Convert to Persona": "轉換為使用者角色", "Rename": "重新命名", "Link to Source": "連結到來源", "Replace / Update": "取代／更新", "Import Tags": "匯入標籤", "Search / Create Tags": "搜尋／建立標籤", "View all tags": "檢視所有標籤", "Creator's Notes": "創作者備註", "Show / Hide Description and First Message": "顯示／隱藏描述和第一則訊息", "Character Description": "角色描述", "Click to allow/forbid the use of external media for this character.": "點選以允許／禁止此角色使用外部媒體", "Ext. Media": "外部媒體權限", "Describe your character's physical and mental traits here.": "在此描述角色的身體和心理特徵。", "First message": "初始訊息", "Click to set additional greeting messages": "點選以設定額外的問候訊息", "Alt. Greetings": "額外問候語", "This will be the first message from the character that starts every chat.": "這將是每次聊天開始時角色傳送的第一則訊息。", "Group Controls": "群組控制", "Chat Name (Optional)": "聊天名稱（選填）", "Click to select a new avatar for this group": "點選以選擇此群組的新頭像", "Group reply strategy": "群組回應策略", "Natural order": "自然順序", "List order": "清單順序", "Group generation handling mode": "群組生成處理模式", "Swap character cards": "交換角色卡", "Join character cards (exclude muted)": "合併角色卡欄位（排除靜音）", "Join character cards (include muted)": "合併角色卡欄位（包括靜音）", "Inserted before each part of the joined fields.": "插入在合併欄位的每一部分之前。", "Join Prefix": "加入前綴", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "選擇「合併角色卡欄位」時，所有角色的相關欄位將被合併。\r例如，在故事字串中，所有角色的描述將合併為一段大文字。\r若您希望這些欄位保持分隔，您可以在此定義前綴或後綴。\r\r此值支援常規巨集 {{macros}}，並會將 {{char}} 替換為相關角色的名稱，可將 <FIELDNAME> 替換為欄位名稱（例如：角色描述、個性、場景等）。", "Inserted after each part of the joined fields.": "插入在合併欄位的每一部分之後。", "Join Suffix": "加入後綴", "Set a group chat scenario": "設定群組聊天場景", "Click to allow/forbid the use of external media for this group.": "點選以允許／禁止此群組使用外部媒體", "Restore collage avatar": "還原預設頭像", "Allow self responses": "允許自我回應", "Auto Mode": "自動模式", "Auto Mode delay": "自動模式延遲", "Hide Muted Member Sprites": "於群組拼貼頭像中隱藏靜音成員", "Current Members": "目前成員", "Add Members": "新增成員", "Create New Character": "建立角色", "Import Character from File": "由本機檔案匯入角色", "Import content from external URL": "由外部 URL 匯入內容", "Create New Chat Group": "建立聊天群組", "Characters sorting order": "角色排序依據", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "最新", "Oldest": "最舊", "Favorites": "我的最愛", "Recent": "最近", "Most chats": "最常聊天", "Least chats": "最少聊天", "Most tokens": "最多符元", "Least tokens": "最少符元", "Random": "隨機", "Toggle character grid view": "切換為角色網格檢視", "Bulk_edit_characters": "批次編輯角色\n\n點選以切換角色\n「Shift+點選」可選擇／取消選擇範圍的角色\n右鍵以檢視動作\n右鍵以檢視動作", "Bulk select all characters": "全選所有角色", "Bulk delete characters": "批次刪除角色", "popup-button-save": "儲存", "popup-button-yes": "確定", "popup-button-no": "取消", "popup-button-cancel": "關閉", "popup-button-import": "匯入", "Advanced Definitions": "進階定義", "Prompt Overrides": "提示詞覆寫", "(For Chat Completion and Instruct Mode)": "（用於聊天補全和指令模式）", "Insert {{original}} into either box to include the respective default prompt from system settings.": "在任一框中插入 {{original}} 以包含系統設定中的預設提示詞。", "Main Prompt": "主要提示詞", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "此處的任何內容將取代此角色使用的預設主要提示詞。（v2 規範：system_prompt）", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "此處的任何內容將取代此角色使用的預設越獄提示詞。（v2 規範：post_history_instructions）", "Creator's Metadata (Not sent with the AI prompt)": "創作者的中繼資料（不會與 AI 提示詞一起傳送）", "Creator's Metadata": "創作者的中繼資料", "(Not sent with the AI Prompt)": "（不與 AI 提示詞一起傳送）", "Everything here is optional": "此處所有內容均為選填", "(Botmaker's name / Contact Info)": "（機器人創作者的名字／聯絡資訊）", "(If you want to track character versions)": "（若您想追蹤角色版本）", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "（描述機器人，提供使用技巧，或列出已測試的聊天模型。這將顯示在角色列表中。）", "Tags to Embed": "嵌入標籤", "(Write a comma-separated list of tags)": "（使用逗號分隔每個標籤）", "Personality summary": "個性摘要", "(A brief description of the personality)": "（關於角色性格的簡要描述）", "Scenario": "場景設想", "(Circumstances and context of the interaction)": "（互動情形與聊天背景）", "Character's Note": "角色備註", "(Text to be inserted in-chat @ designated depth and role)": "（在聊天中以指定角色於 @ 深度位置插入文字）", "@ Depth": "@ 深度", "Role": "角色", "Talkativeness": "健談度", "How often the character speaks in group chats!": "角色在群組聊天中的發言頻率！", "How often the character speaks in": "角色發言頻率", "group chats!": "群組聊天！", "Shy": "寡言", "Normal": "正常", "Chatty": "健談", "Examples of dialogue": "對話範例", "Important to set the character's writing style.": "設定角色的寫作樣式很重要。", "(Examples of chat dialog. Begin each example with START on a new line.)": "（聊天對話範例。每個範例以新的行並以「START」開始。）", "Chat History": "聊天記錄", "Import Chat": "匯入聊天", "Copy to system backgrounds": "複製到系統背景圖片", "Rename background": "重新命名背景圖片", "Lock": "上鎖", "Unlock": "解鎖", "Delete background": "刪除背景圖片", "Chat Scenario Override": "聊天場景覆寫", "Remove": "刪除", "Type here...": "在此輸入⋯", "Chat Lorebook": "聊天知識書", "Chat Lorebook for": "聊天知識書", "chat_world_template_txt": "選定的世界資訊將附加到此聊天。\n在生成 AI 回覆時，它將與全域和角色知識書中的條目結合。", "Select a World Info file for": "選擇世界資訊檔案", "Primary Lorebook": "主要知識書", "A selected World Info will be bound to this character as its own Lorebook.": "選定的世界資訊將作為此角色的知識書附加到此角色。", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "生成 AI 回覆時，將與全域世界資訊選擇器中的條目結合。", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "匯出角色時，也會匯出嵌入 JSON 資料中的選定知識書檔案。", "Additional Lorebooks": "額外的知識書", "Associate one or more auxillary Lorebooks with this character.": "將一個或多個輔助知識書與此角色關聯。", "NOTE: These choices are optional and won't be preserved on character export!": "請注意：此為額外選項，不會隨角色卡資料一同匯出！", "Rename chat file": "重新命名聊天檔案", "Export JSONL chat file": "匯出 JSONL 聊天檔案", "Download chat as plain text document": "將聊天下載為純文字檔案", "Delete chat file": "刪除聊天檔案", "Drag to reorder tag": "拖動以重新排序標籤", "Use tag as folder": "將標籤作為資料夾", "Hide on character card": "在角色卡上隱藏標籤", "Delete tag": "刪除標籤", "Entry Title/Memo": "條目標題／備註", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "世界資訊條目狀態：🔵常數 🟢正常 🔗向量 ❌停用", "WI_Entry_Status_Constant": "🔵", "WI_Entry_Status_Normal": "🟢", "WI_Entry_Status_Vectorized": "🔗", "WI_Entry_Status_Disabled": "❌", "T_Position": "↑角色：角色定義之前\n↓角色：角色定義之後\n↑備註：作者備註之前\n↓備註：作者備註之後\n@深度", "Before Char Defs": "角色定義之前", "After Char Defs": "角色定義之後", "Before EM": "範例訊息之前", "After EM": "範例訊息之後", "Before AN": "作者備註之前", "After AN": "作者備註之後", "at Depth System": "@D ⚙️ 在系統深度", "at Depth User": "@D 👤 在使用者深度", "at Depth AI": "@D 🤖 在 AI 深度", "Depth": "深度", "Order:": "順序：", "Order": "順序", "Trigger %:": "觸發％：", "Probability": "機率", "Duplicate world info entry": "複製世界資訊條目", "Delete world info entry": "刪除世界資訊條目", "Comma separated (required)": "逗號分隔（必填）", "Primary Keywords": "主要關鍵字", "Keywords or Regexes": "關鍵字或正規表示式", "Comma separated list": "逗號分隔列表", "Switch to plaintext mode": "切換到純文字模式", "Logic": "邏輯", "AND ANY": "且「包含任一」", "AND ALL": "且「包含全部」", "NOT ALL": "且「未完全包含」", "NOT ANY": "且「完全不含」", "(ignored if empty)": "（如果為空則忽略）", "Optional Filter": "選填過濾器", "Keywords or Regexes (ignored if empty)": "關鍵字或正規表示式（如果為空則忽略）", "Comma separated list (ignored if empty)": "逗號分隔列表（如果為空則忽略）", "Use global setting": "使用全域設定", "Case-Sensitive": "區分大小寫", "Yes": "是", "No": "否", "Can be used to automatically activate Quick Replies": "可用於自動啟用快速回覆", "Automation ID": "自動化 ID", "( None )": "（無）", "Content": "內容", "Exclude from recursion": "不可遞迴", "Prevent further recursion": "防止進一步遞迴", "Delay until recursion": "延遲遞迴", "What this keyword should mean to the AI, sent verbatim": "這個關鍵字對 AI 應意味著什麼，逐字傳送", "Filter to Character(s)": "角色篩選", "Character Exclusion": "角色排除", "-- Characters not found --": "-- 未找到角色 --", "Inclusion Group": "納入群組", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "納入群組可確保在同一群組中，同時被觸發的多個條目僅有一個會被啟用。\r支援多個以逗號分隔的群組。\r詳見 SillyTavern 官方文件：World Info - Inclusion Group", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "優先此條目：勾選後，此條目將在所有可選條目中被優先考慮。\r若有多個條目同時設為優先，將選擇「順序（Order）」數值最高的條目。", "Only one entry with the same label will be activated": "僅啟用一個具有相同標籤的條目", "A relative likelihood of entry activation within the group": "此條目在群組中被選中的相對可能性", "Group Weight": "群組權重", "Selective": "選擇性", "Use Probability": "使用機率", "Add Memo": "新增備忘錄", "Text or token ids": "文字或符元 ID", "close": "關閉", "prompt_manager_edit": "編輯", "prompt_manager_name": "名稱", "A name for this prompt.": "這個提示詞的名稱。", "To whom this message will be attributed.": "此訊息所屬的角色。", "AI Assistant": "人工智慧助手", "prompt_manager_position": "位置", "Next to other prompts (relative) or in-chat (absolute).": "與其他提示詞相鄰（相對位置）或在聊天中（絕對位置）。", "prompt_manager_relative": "相對位置", "prompt_manager_depth": "深度", "0 = after the last message, 1 = before the last message, etc.": "0 = 在最後一則訊息之後，1 = 在最後一則訊息之前，以此類推。", "Prompt": "提示詞", "The prompt to be sent.": "要傳送的提示詞。", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "即使啟用優先覆寫，此提示詞也不能被角色卡片覆寫。", "prompt_manager_forbid_overrides": "禁止覆寫", "reset": "重設", "save": "儲存", "This message is invisible for the AI": "此訊息對 AI 不可見", "Message Actions": "訊息動作", "Translate message": "翻譯訊息", "Generate Image": "生成圖片", "Stop Image Generation": "終止圖片生成", "Narrate": "朗讀訊息", "Exclude message from prompts": "從提示詞中排除訊息", "Include message in prompts": "在提示詞中包含訊息", "Embed file or image": "嵌入檔案或圖片", "Create checkpoint": "建立檢查點", "Create Checkpoint": "建立檢查點", "Create Branch": "建立分支", "Copy": "複製", "Open checkpoint chat": "開啟檢查點聊天", "Edit": "編輯", "Confirm": "確認", "Copy this message": "複製此訊息", "Delete this message": "刪除此訊息", "Move message up": "上移訊息", "Move message down": "下移訊息", "Enlarge": "放大", "Welcome to SillyTavern!": "歡迎來到 SillyTavern!", "welcome_message_part_1": "閱讀", "welcome_message_part_2": "官方文件", "welcome_message_part_3": "。", "welcome_message_part_4": "在聊天輸入框內輸入", "welcome_message_part_5": "來顯示巨集或命令列表。", "welcome_message_part_6": "加入", "Discord server": "Discord 伺服器", "welcome_message_part_7": "取得公告和資訊。", "SillyTavern is aimed at advanced users.": "SillyTavern 專為進階使用者設計", "If you're new to this, enable the simplified UI mode below.": "如果您是新手，請啟用下方的簡易 UI 模式", "Change it later in the 'User Settings' panel.": "稍後可在「使用者設定」面板中變更", "Enable simple UI mode": "啟用簡易 UI 模式", "Looking for AI characters?": "正在尋找 AI 角色？", "onboarding_import": "匯入", "from supported sources or view": "從支援的來源或檢視", "Sample characters": "範例角色", "Your Persona": "您的使用者角色", "Before you get started, you must select a persona name.": "在開始之前，您必須選擇一個使用者角色名稱", "welcome_message_part_8": "這個隨時可以透過", "welcome_message_part_9": "圖示來變更。", "Persona Name:": "使用者角色名稱", "Temporarily disable automatic replies from this character": "暫時停用此角色的自動回覆", "Enable automatic replies from this character": "啟用此角色的自動回覆", "Trigger a message from this character": "觸發此角色的訊息", "Move up": "上移", "Move down": "下移", "View character card": "檢視角色卡", "Remove from group": "從群組中移除", "Add to group": "新增到群組", "Alternate Greetings": "額外問候語", "Alternate_Greetings_desc": "這些將在開始新聊天時顯示為第一則訊息的滑動選項。\n群組成員可以選擇其中之一來開始對話。", "Alternate Greetings Hint": "額外問候語的提示訊息", "(This will be the first message from the character that starts every chat)": "（這將是每次聊天開始時角色傳送的第一則訊息）", "Forbid Media Override explanation": "此角色／群組在聊天中使用外部媒體的能力。", "Forbid Media Override subtitle": "禁止媒體覆寫副標題", "Always forbidden": "總是禁止", "Always allowed": "總是允許", "View contents": "檢視內容", "Remove the file": "刪除檔案", "Unique to this chat": "此聊天獨有", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "檢查點繼承其上級的註釋，之後可以單獨更改", "Include in World Info Scanning": "納入世界資訊掃描", "Before Main Prompt / Story String": "在主要提示詞／故事字串之前", "After Main Prompt / Story String": "在主要提示詞／故事字串之後", "as": "作為", "Insertion Frequency": "插入頻率", "(0 = Disable, 1 = Always)": "（0 = 停用，1 = 永久）", "User inputs until next insertion:": "使用者輸入直到下一次插入：", "Character Author's Note (Private)": "角色作者備註（私人）", "Won't be shared with the character card on export.": "匯出時不與角色卡共享。", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "將自動新增為此角色的作者備註。將在群組中使用，但在群組聊天開啟時無法修改", "Use character author's note": "使用角色作者備註", "Replace Author's Note": "取代作者註釋", "Default Author's Note": "預設作者註釋", "Will be automatically added as the Author's Note for all new chats.": "將自動新增為所有新聊天的作者備註", "Chat CFG": "聊天 CFG", "1 = disabled": "1 = 停用", "write short replies, write replies using past tense": "撰寫簡短回覆，使用過去式撰寫回覆", "Positive Prompt": "正面提示詞", "Use character CFG scales": "使用角色 CFG 比例", "Character CFG": "角色 CFG", "Will be automatically added as the CFG for this character.": "將自動新增為此角色的 CFG", "Global CFG": "全域 CFG", "Will be used as the default CFG options for every chat unless overridden.": "將作為每次聊天的預設 CFG 選項，除非被覆寫", "CFG Prompt Cascading": "CFG 提示詞級聯", "Combine positive/negative prompts from other boxes.": "結合其他文字方塊中的正／負提示詞", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "例如：勾選聊天、全域和角色框將所有負面提示詞合併為一個逗號分隔的字串", "Always Include": "總是包含", "Chat Negatives": "聊天負面提示詞", "Character Negatives": "角色負面提示詞", "Global Negatives": "全域負面提示詞", "Custom Separator:": "自訂分隔符號：", "Insertion Depth:": "插入深度：", "Token Probabilities": "符元機率", "Select a token to see alternatives considered by the AI.": "選擇一個符元以檢視 AI 考慮的替代方案", "Not connected to API!": "未連線到 API！", "Type a message, or /? for help": "輸入訊息，或 /? 取得支援", "Continue script execution": "繼續腳本執行", "Pause script execution": "暫停腳本執行", "Abort script execution": "中止腳本執行", "Abort request": "中止請求", "Continue the last message": "繼續生成最新訊息", "Send a message": "傳送訊息", "Close chat": "關閉聊天", "Toggle Panels": "切換面板", "Back to parent chat": "返回上層聊天", "Save checkpoint": "儲存檢查點", "Convert to group": "轉換為群組", "Start new chat": "開始新聊天", "Manage chat files": "管理聊天檔案", "Delete messages": "刪除訊息", "Regenerate": "重新生成", "Ask AI to write your message for you": "由 AI 扮演您的角色撰寫訊息", "Impersonate": "AI 扮演使用者", "Continue": "繼續", "Bind user name to that avatar": "將使用者名稱附加到該頭像", "Change persona image": "更改使用者角色圖片", "Select this as default persona for the new chats.": "設定為新聊天的預設使用者角色", "Delete persona": "刪除使用者角色", "These characters are the winners of character design contests and have outstandable quality.": "這些角色都是角色設計比賽的優勝者，品質卓越。", "Contest Winners": "比賽獲勝者", "These characters are the finalists of character design contests and have remarkable quality.": "這些角色都是角色設計比賽的入圍作品，品質卓越。", "Featured Characters": "特色角色", "Attach a File": "上傳附件檔案", "Open Data Bank": "開啟資料庫", "Enter a URL or the ID of a Fandom wiki page to scrape:": "輸入 URL 或 Fandom 維基頁面的 ID 來抓取：", "Examples:": "範例：", "Example:": "範例：", "Single file": "單個檔案", "All articles will be concatenated into a single file.": "所有文章將連接成一個檔案。", "File per article": "每篇文章一個檔案", "Each article will be saved as a separate file.": "每篇文章將另存為一個檔案。", "Data Bank": "資料庫", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "這些檔案將可用於支援附件的擴充功能（例如向量儲存）。", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "支援的檔案類型：純文字，PDF，Markdown，HTML，EPUB。", "Drag and drop files here to upload.": "拖放檔案至此即可上傳。", "Date (Newest First)": "日期（最新優先）", "Date (Oldest First)": "日期（最舊優先）", "Name (A-Z)": "名稱（A-Z）", "Name (Z-A)": "名稱（Z-A）", "Size (Smallest First)": "尺寸（由小到大）", "Size (Largest First)": "尺寸（由大到小）", "Bulk Edit": "批次編輯", "Select All": "全選", "Select None": "選擇無", "Global Attachments": "全域附件", "These files are available for all characters in all chats.": "適用於所有聊天、所有角色。", "Character Attachments": "角色附件", "These files are available the current character in all chats they are in.": "適用於該角色參與的所有聊天。", "Saved locally. Not exported.": "僅本機儲存，不匯出。", "Chat Attachments": "聊天附件", "These files are available to all characters in the current chat.": "適用於本次聊天中的所有角色。", "Enter a base URL of the MediaWiki to scrape.": "輸入要抓取的 MediaWiki 的基礎 URL。", "Don't include the page name!": "不要包括頁面名稱！", "Enter web URLs to scrape (one per line):": "輸入要抓取的網頁 URL（每行一個）：", "Enter a video URL to download its transcript.": "輸入影片 URL 來下載其文字記錄。", "Expression API": "表達 API", "Fallback Expression": "回退表達式", "ext_sum_title": "聊天摘要", "ext_sum_with": "摘要來源", "ext_sum_main_api": "主要 API", "ext_sum_current_summary": "摘要內容：", "ext_sum_restore_previous": "還原為上一則", "ext_sum_memory_placeholder": "將在此生成摘要⋯", "Trigger a summary update right now.": "立即更新摘要內容。", "ext_sum_force_text": "重新摘要", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "停用自動摘要更新。暫停時，摘要保持原樣。您仍可以透過點選「重新摘要」按鈕強制更新（僅適用於使用「主要 API」)。", "ext_sum_pause": "暫停", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "摘要時將省略世界資訊和作者備註。此選項僅適用於使用主要 API，擴充功能 API 始終自動省略世界資訊與作者備註。", "ext_sum_no_wi_an": "排除世界資訊及作者備註", "ext_sum_settings_tip": "編輯摘要提示、插入位置等", "ext_sum_settings": "摘要設定", "ext_sum_prompt_builder": "摘要提示詞產生器", "ext_sum_prompt_builder_1_desc": "將使用尚未摘要的訊息以建立摘要提示詞，在完成摘要前將暫停聊天功能。", "ext_sum_prompt_builder_1": "原始、阻塞", "ext_sum_prompt_builder_2_desc": "將使用尚未摘要的訊息自動建立摘要提示詞，在完成摘要前仍可正常進行聊天。並非所有後端都支援此模式。", "ext_sum_prompt_builder_2": "原始、非阻塞", "ext_sum_prompt_builder_3_desc": "將使用常規主提示產生器，並將摘要請求新增至其中作為最後一則系統訊息。", "ext_sum_prompt_builder_3": "經典、阻塞", "Summary Prompt": "摘要提示詞", "ext_sum_restore_default_prompt_tip": "還原為預設提示詞", "ext_sum_prompt_placeholder": "此提示詞將傳送給 AI 以請求生成摘要。{{words}} 將解析為「字數」參數。", "ext_sum_target_length_1": "目標摘要長度", "ext_sum_target_length_2": "（", "ext_sum_target_length_3": "字）", "ext_sum_api_response_length_1": "API 響應長度", "ext_sum_api_response_length_2": "（", "ext_sum_api_response_length_3": "個符元）", "ext_sum_0_default": "0 = 預設", "ext_sum_raw_max_msg": "[原始] 每則請求的最大訊息數", "ext_sum_0_unlimited": "0 = 無限制", "Update frequency": "更新頻率", "ext_sum_update_every_messages_1": "更新每", "ext_sum_update_every_messages_2": " 訊息", "ext_sum_0_disable": "0 = 停用", "ext_sum_auto_adjust_desc": "嘗試根據聊天指標自動調整更新間隔。", "ext_sum_update_every_words_1": "更新每", "ext_sum_update_every_words_2": " 字", "ext_sum_both_sliders": "若兩個滑桿數值皆不為零，則將各自按照其時間間隔更新摘要。", "ext_sum_injection_template": "插入範本", "ext_sum_memory_template_placeholder": "{{summary}} 會解析為目前的摘要內容。", "ext_sum_injection_position": "插入位置", "How many messages before the current end of the chat.": "距離本次聊天結尾前的訊息數量。", "ext_regex_title": "正規表示式", "ext_regex_new_global_script": "+ 全域", "ext_regex_new_scoped_script": "+ 區域", "ext_regex_import_script": "匯入腳本", "ext_regex_global_scripts": "全域腳本", "ext_regex_global_scripts_desc": "適用於所有角色，資料將儲存到本機。", "ext_regex_scoped_scripts": "區域腳本", "ext_regex_scoped_scripts_desc": "僅適用於目前角色，資料將儲存到該角色卡中。", "Regex Editor": "正規表示式編輯器", "Test Mode": "測試模式", "ext_regex_desc": "正規表示式（Regex）是一種使用正規表示式尋找／取代字串的工具。如果您想了解更多，請點選標題旁邊的「？」", "Input": "輸入", "ext_regex_test_input_placeholder": "在此輸入⋯", "Output": "輸出", "ext_regex_output_placeholder": "空的", "Script Name": "腳本名稱", "Find Regex": "尋找正規表示式", "Replace With": "取代為", "ext_regex_replace_string_placeholder": "使用 {{match}} 來包含來自尋找正規表示式的匹配文字或 $1、$2 等捕獲組。", "Trim Out": "修剪掉", "ext_regex_trim_placeholder": "在取代之前，全域修剪正規表示式匹配中的任何不需要的部分。每個元素用輸入鍵分隔。", "ext_regex_affects": "影響條目", "ext_regex_user_input": "使用者輸入", "ext_regex_ai_output": "AI 輸出", "Slash Commands": "斜線命令", "ext_regex_min_depth_desc": "當應用於提示或顯示時，僅影響至少 N 層深的訊息。0 = 最後一則訊息，1 = 倒數第二個訊息等。", "Min Depth": "最小深度", "ext_regex_min_depth_placeholder": "無限制", "ext_regex_max_depth_desc": "當應用於提示或顯示時，僅影響不超過 N 層深度的訊息。0 = 最後一則訊息，1 = 倒數第二個訊息等。", "ext_regex_other_options": "其他選項", "Only Format Display": "僅修改聊天顯示", "ext_regex_only_format_prompt_desc": "不修改聊天記錄，僅修改傳送訊息（請求文字生成時）時的系統提示詞。", "Only Format Prompt (?)": "僅修改系統提示詞", "Run On Edit": "編輯時執行", "ext_regex_substitute_regex_desc": "在執行「尋找正規表達式」前，將 {{macros}}（巨集）替換為對應內容", "Substitute Regex": "取代正規表示式", "ext_regex_import_target": "匯入至：", "ext_regex_disable_script": "停用腳本", "ext_regex_enable_script": "啟用腳本", "ext_regex_edit_script": "編輯腳本", "ext_regex_move_to_global": "移至全域腳本", "ext_regex_move_to_scoped": "移至區域腳本", "ext_regex_export_script": "匯出腳本", "ext_regex_delete_script": "刪除腳本", "Trigger Stable Diffusion": "觸發 Stable Diffusion", "sd_Yourself": "角色", "sd_Your_Face": "角色的臉", "sd_Me": "使用者", "sd_The_Whole_Story": "整篇故事", "sd_The_Last_Message": "最後一則訊息", "sd_Raw_Last_Message": "最後一則原始訊息", "sd_Background": "背景", "Image Generation": "圖片生成設定", "sd_refine_mode": "允許在傳送至生成 API 前，手動編輯提示詞字串", "sd_refine_mode_txt": "生成前編輯提示詞", "sd_interactive_mode": "當傳送「給我一張貓的圖片」這類訊息時，自動生成圖片。", "sd_interactive_mode_txt": "互動模式", "sd_multimodal_captioning": "根據使用者和角色的頭像，使用多模態模型描述生成肖像提示詞。", "sd_multimodal_captioning_txt": "對肖像使用多模態模型描述", "sd_expand": "使用文字生成模型自動擴寫提示詞。", "sd_expand_txt": "自動潤飾提示詞", "sd_snap": "對於具有特定長寬比的生成請求（如肖像、背景），將其調整至最接近的已知解析度，同時儘量保持絕對像素數（建議用於 SDXL）。", "sd_snap_txt": "自動調整解析度", "Source": "來源", "sd_auto_url": "範例：{{auto_url}}", "Authentication (optional)": "授權驗證（選填）", "Example: username:password": "範例：帳號:密碼", "Important:": "重要：", "sd_auto_auth_warning_1": "使用", "sd_auto_auth_warning_2": "旗標執行 SD Web UI！伺服器必須能夠被 SillyTavern 主機存取。", "sd_drawthings_url": "範例：{{drawthings_url}}", "sd_drawthings_auth_txt": "執行 DrawThings 應用程式並在介面中啟用 HTTP API 開關！伺服器必須能夠被 SillyTavern 主機存取。", "sd_vlad_url": "範例：{{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "伺服器必須能夠被 SillyTavern 主機存取。", "Hint: Save an API key in AI Horde API settings to use it here.": "提示訊息：在 AI Horde API 設定中儲存一個 API 金鑰，以便在此使用。", "Allow NSFW images from Horde": "允許來自 Horde 的 NSFW 圖片", "Sanitize prompts (recommended)": "清理提示詞（建議）", "Automatically adjust generation parameters to ensure free image generations.": "自動調整生成參數以確保生成免費的圖片。", "Avoid spending Anlas": "避免消耗 Anlas 點數", "Opus tier": "Opus 級別", "View my Anlas": "檢視我的 Anlas 點數", "These settings only apply to DALL-E 3": "此設定僅會套用在 DALL-E 3", "Image Style": "圖片樣式", "Image Quality": "圖片品質", "Standard": "標準", "HD": "高畫質", "sd_comfy_url": "範例：{{comfy_url}}", "Open workflow editor": "開啟 workflow 編輯器", "Create new workflow": "建立新的 workflow", "Delete workflow": "刪除 workflow", "Enhance": "增強", "Refine": "精煉", "Decrisper": "德克里斯珀", "Sampling steps": "取樣步數", "Width": "寬度（Width）", "Height": "高度（Height）", "Resolution": "解析度", "Model": "模型", "Sampling method": "取樣方法", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON>（並非所有取樣器均受支援）", "SMEA versions of samplers are modified to perform better at high resolution.": "SMEA 版本的取樣器經過修改，能在高解析度下表現更佳。", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "SMEA 取樣器的 DYN 變體通常會產生更多樣化的輸出，但在非常高的解析度下可能會失敗。", "DYN": "DYN", "Scheduler": "排程器", "Restore Faces": "修復臉部", "Hires. Fix": "高解析度修正", "Upscaler": "放大演演算法", "Upscale by": "放大倍率", "Denoising strength": "重繪幅度", "Hires steps (2nd pass)": "高解析步驟（2nd pass）", "Preset for prompt prefix and negative prompt": "提示詞前綴和負面提示詞的預設設定檔", "Style": "樣式", "Save style": "儲存樣式", "Delete style": "刪除樣式", "Common prompt prefix": "通用提示詞前綴", "sd_prompt_prefix_placeholder": "使用 {prompt} 指定生成的提示詞將被插入的位置。", "Negative common prompt prefix": "通用負面提示詞前綴", "Character-specific prompt prefix": "角色提示詞前綴", "Won't be used in groups.": "群聊中無效", "sd_character_prompt_placeholder": "描述該角色的特徵。這些特徵將新增在通用提示詞前綴之後。例如：女性、綠色眼睛、棕色頭髮、粉紅色襯衫。", "Character-specific negative prompt prefix": "角色負面提示詞前綴", "sd_character_negative_prompt_placeholder": "不應出現在該角色上的任何特徵。這些特徵將新增在負面通用提示詞前綴之後。例如：珠寶、鞋子、眼鏡。", "Shareable": "分享至角色卡", "Image Prompt Templates": "圖片生成提示詞", "Vectors Model Warning": "向量模型警告", "Translate files into English before processing": "處理前將檔案翻譯成英文", "Manager Users": "管理使用者", "New User": "新使用者", "Status:": "狀態：", "Created:": "建立於：", "Display Name:": "顯示名稱：", "User Handle:": "使用者控制程式碼：", "Password:": "密碼：", "Confirm Password:": "確認密碼：", "This will create a new subfolder...": "這將建立一個新的子資料夾⋯", "Current Password:": "目前密碼：", "New Password:": "新密碼：", "Confirm New Password:": "確認新密碼：", "Debug Warning": "偵錯警告", "Execute": "執行", "Are you sure you want to delete this avatar?": "您確定要刪除使用者嗎？", "Deleting:": "刪除中：", "Also wipe user data.": "同時清除使用者資料。", "Warning:": "警告：", "This action is irreversible.": "此動作不可逆轉。", "Type the user's handle below to confirm:": "在下方輸入使用者的控制程式碼以確認：", "Import Characters": "匯入角色", "Enter the URL of the content to import": "輸入要匯入的內容的 URL", "Supported sources:": "支援的來源：", "char_import_1": "Chub 角色（直接連結或 ID）", "char_import_example": "例子：", "char_import_2": "Chub Lorebook（直接連結或 ID）", "char_import_3": "JanitorAI 角色（直接連結或 ID）", "char_import_4": "Pygmalion.chat 角色（直接連結或 ID）", "char_import_5": "AICharacterCards.com 角色（直接連結或 ID）", "char_import_6": "直接 PNG 連結（請參閱", "char_import_7": "對於允許的主機）", "char_import_8": "RisuRealm 角色（直接連結）", "char_import_10": "Perchance 角色（直接連結或 ID + .gz）", "Supports importing multiple characters.": "支援匯入多個字元。", "Write each URL or ID into a new line.": "將每個 URL 或 ID 寫入新行。", "Export for character": "匯出字元", "Export prompts for this character, including their order.": "匯出該角色的提示，包括其順序。", "Export all": "全部匯出", "Export all your prompts to a file": "將所有提示匯出到文件", "Insert prompt": "插入提示", "Delete prompt": "刪除提示", "Import a prompt list": "匯入提示列表", "Export this prompt list": "匯出此提示列表", "Reset current character": "重設目前字元", "New prompt": "新提示", "Prompts": "提示", "Total Tokens:": "代幣總數：", "prompt_manager_tokens": "代幣", "Are you sure you want to reset your settings to factory defaults?": "您確定要將設定重設為原廠預設值嗎？", "Don't forget to save a snapshot of your settings before proceeding.": "在繼續之前，請不要忘記進行備份。", "Settings Snapshots": "設定備份", "Record a snapshot of your current settings.": "記錄目前設定的快照。", "Make a Snapshot": "建立快照", "Restore this snapshot": "還原此快照", "Hi,": "嗨，", "To enable multi-account features, restart the SillyTavern server with": "要啟用多帳號功能，請在 config.yaml 文件中將", "set to true in the config.yaml file.": "設為 true 後重啟 SillyTavern 伺服器。", "Account Info": "帳號資訊", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "要更改您的使用者頭像，請使用下方按鈕或在使用者角色管理選單中選擇一個預設人物。", "Set your custom avatar.": "設定您的頭像。", "Remove your custom avatar.": "移除您的頭像。", "Handle:": "使用者名稱：", "This account is password protected.": "這個帳號受到密碼保護。", "This account is not password protected.": "這個帳號並未受到密碼保護。", "Account Actions": "帳號動作", "Change Password": "變更密碼", "Manage your settings snapshots.": "管理您的設定值快照。", "Download a complete backup of your user data.": "下載您的完整使用者備份。", "Download Backup": "下載備份", "Danger Zone": "危險區域", "Reset your settings to factory defaults.": "將您的設定值還原為原廠預設值。", "Reset Settings": "重設設定值", "Wipe all user data and reset your account to factory settings.": "刪除您所有的使用者設定以及帳號資料，並恢復為系統預設值。", "Reset Everything": "刪除所有資料", "Reset Code:": "重設驗證碼：", "Want to update?": "想更新到最新版嗎？", "How to start chatting?": "如何開始聊天？", "Click _space": "點選", "and select a": "並擇一", "Chat API": "聊天 API", "and pick a character.": "並選擇一個角色。", "You can browse a list of bundled characters in the": "您可以在", "Download Extensions & Assets": "下載擴充功能＆額外資源", "menu within": "選單中瀏覽內建角色列表", "Confused or lost?": "困惑或迷路了嗎？", "click these icons!": "點選這些圖示！", "in the chat bar": "到聊天欄中", "SillyTavern Documentation Site": "SillyTavern 文件網站", "Extras Installation Guide": "Extras 安裝指南", "Still have questions?": "仍有更多問題？", "Join the SillyTavern Discord": "加入 Si<PERSON><PERSON><PERSON><PERSON> Discord", "Post a GitHub issue": "發布 GitHub 問題", "Contact the developers": "聯絡開發者", "(-1 for random)": "（-1 表示隨機）", "(Optional)": "（可選）", "(use _space": "（使用", "api_no_connection": "未連線⋯", "No model description": "[無描述]", "openai_logit_bias_no_items": "無項目", "Any contents here will replace the default Post-History Instructions used for this character. (v2 specpost_history_instructions)": "此處填入的內容將取代該角色的預設聊天歷史後指示（Post-History Instructions）。\n（v2 格式：specpost_history_instructions）", "comma delimited,no spaces between": "逗號分割，無需空格", "e.g. black-forest-labs/FLUX.1-dev": "例如：black-forest-labs/FLUX.1-dev", "Example: gpt-4o": "例如：gpt-4o", "Example: http://localhost:1234/v1": "例如：http://localhost:1234/v1", "popup-button-crop": "裁剪", "(disabled when max recursion steps are used)": "（當最大遞迴步驟數使用時將停用）", "A greedy, brute-force algorithm used in LLM sampling to find the most likely sequence of words or tokens. It expands multiple candidate sequences at once, maintaining a fixed number (beam width) of top sequences at each step.": "一種用於 LLM 抽樣的貪婪演演算法，用於尋找最可能的單詞或標記序列。該方法會同時展開多個候選序列，並在每一步中保持固定數量的頂級序列（beam width）。", "A multiplicative factor to expand the overall area that the nodes take up.": "節點佔用該擴充功能區域的倍數。", "Abort current image generation task": "終止目前的圖片生成任務", "Add Character and User names to a list of stopping strings.": "將角色和使用者角色名稱新增至停止字元列表。", "Alignment for rank nodes.": "對排名節點的對齊方式。", "Always show the node full info panel at the bottom left of the timeline view. When off, show it near the node.": "始終將節點的完整資訊面板顯示在時間軸檢視的左下角。關閉時，將顯示在節點附近。", "Always show the node tooltip at the bottom left of the timeline view. When off, show it near the node.": "始終將節點的工具提示欄顯示在時間軸檢視的左下角。關閉時，將顯示在節點附近。", "Apply current sorting as Order": "應用此排序為順序", "Cap the number of entry activation recursions": "限制入口啟動的遞迴次數", "Caption": "標題", "Close popup": "關閉彈出視窗", "Color configuration for Timelines when 'Use UI Theme' in Style Settings is off.": "關閉「使用介面主題」的時間線顏色。", "context_allow_post_history_instructions": "在文字完成模式中包含聊天歷史後指示（Post-History Instructions），但可能導致不良輸出。", "Create a new connection profile": "建立新的連線設定檔", "Defines on importing cards which action should be chosen for importing its listed tags. 'Ask' will always display the dialog.": "定義匯入角色卡時應採取的動作。選擇「詢問」將始終顯示對話框。", "delay_until_recursion_level": "定義遞迴掃描的延遲層級。\r最初僅匹配第一層（數字最小的層級）。\r未找到匹配時，下一層將成為可匹配的層級。\r此過程會重複，直到所有層級都被檢查完畢。\r與「延遲至遞迴」設定相關聯。", "Delete a connection profile": "刪除連線設定檔", "Delete template": "刪除範本", "Delete the template": "刪除此範本", "Disabling is not recommended.": "不建議停用。", "Display swipe numbers for all messages, not just the last.": "顯示所有訊息的滑動編號，而不僅是最後一條訊息。", "Duplicate persona": "複製使用者角色", "Edit a connection profile": "編輯連線設定檔", "Enable auto-select of input text in some text fields when clicking/selecting them. Applies to popup input textboxes, and possible other custom input fields.": "啟用自動選擇輸入框中的文字，適用於彈出輸入框及其他自定義輸入框。", "Entries with a cooldown can't be activated N messages after being triggered.": "設有冷卻時間的條目於觸發後的 N 條訊息內無法再次啟用。", "Entries with a delay can't be activated until there are N messages present in the chat.": "有延遲的條目需等待聊天中出現 N 條訊息後才能啟用。", "Expand swipe nodes when the timeline view first opens, and whenever the graph is refreshed. When off, you can expand them by long-pressing a node, or by pressing the Toggle Swipes button.": "時間線檢視首次開啟或重新整理時展開滑動節點。關閉時可透過長按節點或點選「切換滑動」按鈕展開。", "Export Advanced Formatting settings": "匯出進階格式設定", "Export template": "匯出範本", "Find similar characters": "尋找相似角色", "Height of a node, in pixels at zoom level 1.0.": "縮放等級為 1.0 時的節點像素高度。", "How the automatic graph builder assigns a rank (layout depth) to graph nodes.": "自動圖表生成器分配圖節點等級（配置深度）的方式。", "If checked and the character card contains a Post-History Instructions override, use that instead": "勾選後，將使用角色卡中的聊天歷史後指示（Post-History Instructions）覆蓋。", "Import Advanced Formatting settings": "匯入進階格式設定\n也可提供舊版檔案作為提示詞和上下文範本使用", "Import template": "匯入範本", "In group chat, highlight the character(s) that are currently queued to generate responses and the order in which they will respond.": "在群組聊天中，突出顯示該生成回應的角色及順序。", "Include names with each message into the context for scanning": "將每條訊息的名稱納入掃描上下文", "Inserted before the first User's message": "插入到第一條使用者訊息前。", "instruct_enabled": "啟用指令模式（Instruct Mode）", "instruct_last_input_sequence": "插入到最後一條使用者訊息之前。", "instruct_template_activation_regex_desc": "連線 API 或選擇模型時，若模型名稱符合所提供的正規表達式，則自動啟動該指令範本（Instruct Template）。", "Load Asset List": "載入資源列表", "load_asset_list_desc": "根據資源列表文件載入擴充功能及資源。\n\n該欄位中的預設資源 URL 指向官方擴充功能及資源列表。\n可在此插入您的自定義資源列表。\n\n若需安裝單個第三方擴充功能，請使用右上角的「安裝擴充功能」按鈕。", "markdown_hotkeys_desc": "啟用快捷鍵以在某些文字輸入框中插入 Markdown 格式字元。詳見「/help hotkeys」。", "Not all samplers supported.": "並非所有取樣器均受支援。", "Open the timeline view. Same as the slash command '/tl'.": "開啟時間線檢視，與斜線指令「/tl」相同。", "Penalize sequences based on their length.": "根據序列長度進行懲罰。", "Reload a connection profile": "重新載入連線設定檔", "Rename current preset": "重新命名此預設設定檔", "Rename current prompt": "重新命名此提示詞", "Rename current template": "重新命名此範本", "Reset all Timelines settings to their default values.": "將所有時間軸設定重設為預設值。", "Restore current prompt": "還原目前的提示詞", "Restore current template": "還原目前的範本", "Save prompt as": "另存提示詞為", "Save template as": "另存範本為", "sd_adetailer_face": "在生成過程中使用 ADetailer 臉部模型。需在後端安裝 ADetailer 擴充功能。", "sd_free_extend": "自動使用目前選定的 LLM 擴充功能的「自由模式」提示詞（不包括肖像或背景）。", "sd_function_tool": "使用功能工具自動偵測意圖以生成圖片。", "Seed_desc": "用於生成確定性和可重現輸出的隨機種子。設定為 -1 時將使用隨機種子。", "Select your current Context Template": "選擇您目前的上下文範本", "Select your current Instruct Template": "選擇您目前的指令範本", "Select your current System Prompt": "選擇您目前的系統提示詞", "Separation between adjacent edges in the same rank.": "同一層級中相鄰邊之間的間距。", "Separation between adjacent nodes in the same rank.": "同一層級中相鄰節點之間的間距。", "Separation between each rank in the layout.": "配置中各層級之間的間距。", "Settings for the visual appearance of the Timelines graph.": "時間線圖形的視覺外觀設定。", "Show a button in the input area to ask the AI to impersonate your character for a single message": "於輸入框中新增按鈕，讓 AI 模仿您的角色傳送一則訊息。", "Show a legend for colors corresponding to different characters and chat checkpoints.": "顯示一個圖例，標註不同角色和對話檢查點對應的顏色。", "Show the AI character's avatar as the graph root node. When off, the root node is blank.": "將 AI 角色的頭像作為圖形的根節點；關閉時，根節點為空。", "Sticky entries will stay active for N messages after being triggered.": "觸發後，置頂條目將在接下來的 N 條訊息中保持活躍。", "stscript_parser_flag_replace_getvar_label": "防止 {{getvar::}} 和 {{getglobalvar::}} 巨集的字面巨集樣值被自動解析。\n例如，{{newline}} 將保持為字面字串 {{newline}}。\n\n（此功能透過內部將 {{getvar::}} 和 {{getglobalvar::}} 巨集替換為區域變數來實現。）", "Style and routing of graph edges.": "圖形邊的樣式和路徑。", "Swap width and height": "交換寬度與高度", "Swipe left": "向左滑動", "Swipe right": "向右滑動", "Switch the Character/Tags filter around to exclude the listed characters and tags from matching for this entry": "切換角色／標籤篩選，將列出的角色和標籤從此條目的匹配中排除", "sysprompt_enabled": "啟用系統提示詞", "The number of sequences generated at each step with Beam Search.": "在 Beam Search 中每一步生成的序列數量。", "The visual appearance of a node in the graph.": "圖形中節點的視覺外觀。", "Update a connection profile": "更新連線設定檔", "Update current prompt": "更新此提示詞", "Update current template": "更新此範本", "Use GPU acceleration for positioning the full info panel that appears when you click a node. If the tooltip arrow tends to disappear, turning this off may help.": "啟用 GPU 加速來定位點選節點時出現的完整資訊面板。若發現工具提示箭頭經常消失，可考慮關閉此功能。", "Use the colors of the ST GUI theme, instead of the colors configured in Color Settings specifically for this extension.": "使用使用者設定中的介面主題顏色，取代下方「顏色設定」中額外設定的顏色。", "View connection profile details": "檢視連線設定檔詳細資訊", "When enabled, nodes that have swipes splitting off of them will appear subtly larger, in addition to having the double border.": "啟用後，具分支滑動的節點將顯示雙重邊框，還會略微放大。", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized": "世界資訊條目狀態：\\r🔵 恆定\\r🟢 正常\\r🔗 向量化", "Width of a node, in pixels at zoom level 1.0.": "縮放等級為 1.0 時，節點的像素寬度。", "world_button_title": "角色背景設定\n「Shift+點選」可開啟「連結至世界資訊」彈窗", "# of Beams": "# of Beams", "Additional Parameters": "其他參數", "All": "全部", "Allow fallback models": "允許回退模型", "Allow fallback providers": "允許回退供應商", "Allow reverse proxy": "允許反向代理", "Alternate Greeting #": "備選問候語 #", "alternate_greetings_hint_1": "點選", "alternate_greetings_hint_2": "按鈕開始！", "Always": "總是", "ANY support requests will be REFUSED if you are using a proxy.": "使用代理時，所有支援請求均不予受理。", "API": "API", "API Key": "API 金鑰", "Ask": "詢問", "Ask every time": "每次都詢問", "Assets URL": "資源 URL", "Assistant Message Sequences": "助理訊息序列", "Assistant Prefix": "助理訊息前綴", "Assistant Suffix": "助理訊息後綴", "Audio Playback Speed": "音檔播放速度", "Auto-select Input Text": "自動選擇輸入文字", "Automatically caption images": "自動產生圖片註解", "Auxiliary": "輔助提示詞", "Background Image": "背景圖片", "Block Entropy API Key": "Block Entropy API 金鑰", "Can be set manually or with an _space": "可以手動設定或使用 _space", "Caption Prompt": "註解功能提示詞", "category": "類別", "Character Expressions": "角色情緒立繪", "Character Node Color": "角色節點顏色", "character_names_none": "避免使用角色名稱作為前綴。此功能在群組中可能表現不理想，需謹慎考慮。", "Characters": "角色", "Chat Message Visibility (by source)": "聊天訊息可見性（按來源）", "Chat vectorization settings": "聊天向量化設定", "Checked: all entries except ❌ status can be activated.": "勾選：除 ❌ 狀態外的所有條目均可啟動。", "Checkpoint Color": "檢查點節點邊框顏色", "Chunk boundary": "Chunk 邊界", "Chunk overlap (%)": "Chunk 重疊（％）", "Chunk size (chars)": "<PERSON><PERSON> 大小（字元數）", "class": "所有類別", "Classifier API": "分類器 API", "Click to set": "點選以設定", "CLIP Skip": "CLIP 跳過", "Completion Object": "完成物件", "Conf": "設定檔", "Connection Profile": "連線設定檔", "Cooldown": "冷卻時間", "Create new folder in the _space": "請在「SillyTavern/data/使用者資料夾", "currently_loaded": "[目前已載入]", "currently_selected": "[目前已選取]", "Custom (OpenAI-compatible)": "自定義（相容 OpenAI）", "Custom Expressions": "自定義角色表情", "Data Bank files": "資料庫文件", "Default / Fallback Expression": "預設／回退表情", "Delay": "延遲", "Do not proceed if you do not agree to this!": "若不同意此條款，請勿繼續！", "Edge Color": "邊緣顏色", "Edit captions before saving": "在儲存前編輯註解", "Enable for files": "啟用文件檔案向量化", "Enable for World Info": "啟用世界資訊向量化", "enable_functions_desc_1": "允許使用", "enable_functions_desc_2": "功能工具", "enable_functions_desc_3": "可供多種擴充功能利用，實現更多功能。", "Enabled for all entries": "對所有條目啟用", "Enabled for chat messages": "啟用聊天訊息向量化", "Endpoint URL": "端點 URL", "Enter a Model ID": "輸入模型 ID", "Example: https://****.endpoints.huggingface.cloud": "例如：https://****.endpoints.huggingface.cloud", "Exclude": "排除", "Exclude Top Choices (XTC)": "排除頂端選項（XTC）", "tag_import_existing": "現有項目", "expression_label_pattern": "[情緒名稱].[圖檔格式]（例如：neutral.png）。", "ext_translate_auto_mode": "自動翻譯模式", "ext_translate_btn_chat": "翻譯聊天內容", "ext_translate_btn_input": "翻譯輸入內容", "ext_translate_clear": "清除翻譯", "ext_translate_mode_both": "翻譯輸入和回應", "ext_translate_mode_inputs": "僅翻譯輸入", "ext_translate_mode_none": "無翻譯", "ext_translate_mode_provider": "翻譯提供者", "ext_translate_mode_responses": "僅翻譯回應", "ext_translate_target_lang": "目標語言", "ext_translate_title": "聊天翻譯", "Extensions Menu": "擴充功能選單", "Extras": "擴充功能", "Extras API": "擴充功能 API", "Featherless Model Selection": "Featherless 模型選擇", "File vectorization settings": "檔案向量化設定", "Filter to Characters or Tags": "角色／標籤篩選", "First User Prefix": "第一使用者前綴", "folder of your user data directory and name it as the name of the character.": "」中新增資料夾，並將該資料夾命名為角色名稱（名稱需與使用者資料夾中的角色名稱一致）。", "Group Scoring": "群組評分", "Groups and Past Personas": "群組與過去的使用者角色設定", "Hint:": "提示：", "Hint: Set the URL in the API connection settings.": "提示：在 API 連線設定中設定 URL。", "Horde": "Horde", "HuggingFace Token": "HuggingFace 符元", "Image Captioning": "圖片註解", "Generate Caption": "產生圖片註解", "Injection Position": "插入位置", "Relative (to other prompts in prompt manager) or In-chat @ Depth.": "插入位置（與提示詞管理器中的其他提示相比）或聊天中的深度位置。", "Injection Template": "插入範本", "Insert#": "插入#", "Instruct Sequences": "指令序列", "Instruct Template": "指令範本", "Interactive Mode": "互動模式", "Karras": "<PERSON><PERSON><PERSON>", "Keep model in memory": "將模型儲存在記憶體中", "Keyboard": "鍵盤：", "AI Horde Website": "AI Horde 網站", "Last User Prefix": "最後使用者前綴", "Linear": "線性", "LLM": "LLM", "LLM Prompt": "LLM 提示詞", "Load a custom asset list or select": "載入或選擇自定義資源列表", "Load an asset list": "載入資源列表", "Local": "本機", "Local (Transformers)": "本機（Transformers）", "macro)": "巨集）", "Main API": "主要 API", "Markdown Hotkeys": "Markdown 快捷鍵", "Master Export": "進階匯出", "Master Import": "進階匯入", "Max Entries": "最大條目數", "Max Recursion Steps": "最大遞迴步數", "Message attachments": "訊息附件", "Message Template": "訊息範本", "Model ID": "模型 ID", "mui_reset": "重設", "Multimodal (OpenAI / Anthropic / llama / Google)": "多模態（OpenAI／Anthropic／llama／Google）", "must be set in Tabby's config.yml to switch models.": "須在 <PERSON><PERSON>'s config.yml 中設定以切換模型。", "Names as Stop Strings": "將名稱用作停止字串", "Never": "從不", "NomicAI API Key": "NomicAI API 金鑰", "Non-recursable": "不可遞迴", "None (disabled)": "無（已停用）", "OK": "確定", "Old messages are vectorized gradually as you chat. To process all previous messages, click the button below.": "舊訊息會在聊天時逐步向量化。\n若要處理所有先前訊息，請點選下方按鈕。", "Only used when Main API or WebLLM Extension is selected.": "僅在選擇主要 API 或 WebLLM 擴充功能時使用。", "Open a chat to see the character expressions.": "開啟聊天以檢視角色表情。", "Post-History Instructions": "聊天歷史後指示", "Prefer Character Card Instructions": "角色卡聊天歷史後指示優先", "Prioritize": "優先處理", "Prompt Content": "提示詞內容", "prompt_manager_in_chat": "聊天中的提示詞管理", "prompt_post_processing_none": "無", "Purge Vectors": "清除向量", "Put images with expressions there. File names should follow the pattern:": "將表情立繪放置於此，檔案名稱應遵循以下格式：", "Quad": "四元數", "Query messages": "查詢訊息", "Quick Impersonate button": "快速模擬按鈕", "Recursion Level": "遞迴層級", "Remove all image overrides": "移除所有圖片覆蓋", "Restore default": "恢復預設", "Retain#": "保留#", "Retrieve chunks": "檢索 Chunks", "Sampler Order": "取樣順序", "Score threshold": "分數閾值", "sd_free_extend_small": "（互動／指令）", "sd_free_extend_txt": "使用「自由模式」。由 LLM 自動擴寫圖片生成提示", "sd_function_tool_txt": "使用功能工具", "sd_prompt_-1": "聊天訊息範本", "sd_prompt_-2": "功能工具提示詞", "sd_prompt_0": "角色（第二人稱，你）", "sd_prompt_1": "使用者（第一人稱，我）", "sd_prompt_10": "肖像（多模態模式）", "sd_prompt_11": "自由模式（由 LLM 自動擴寫）", "sd_prompt_2": "場景（擷取場景資訊）", "sd_prompt_3": "原始最後訊息", "sd_prompt_4": "最後訊息", "sd_prompt_5": "肖像（第二人稱，你）", "sd_prompt_7": "背景", "sd_prompt_8": "角色（多模態模式）", "sd_prompt_9": "使用者（多模態模式）", "Select a Model": "選擇模型", "Select the API for classifying expressions.": "選擇分類表情的 API。", "Select with Enter": "按 Enter 選擇", "Select with Tab": "按 Tab 選擇", "Select with Tab or Enter": "按 Tab 或 Enter 選擇", "Separators as Stop Strings": "以分隔符號作為停止字串", "Set the default and fallback expression being used when no matching expression is found.": "設定在無法配對表情時所使用的預設表情和備用圖片。", "Set your API keys and endpoints in the API Connections tab first.": "請先在「API 連線」頁面中設定您的 API 金鑰和端點。", "Show default images (emojis) if sprite missing": "無對應圖片時，顯示為預設表情符號（emoji）", "Show group chat queue": "顯示群組聊天佇列", "Size threshold (KB)": "大小閾值（KB）", "Slash Command": "斜線命令", "space_ slash command.": " 斜線命令。", "Sprite Folder Override": "表情立繪資料夾覆蓋", "Sprite set:": "立繪組：", "Show Gallery": "檢視相簿", "Sticky": "黏性", "Style Preset": "樣式預設設定檔", "Summarize chat messages for vector generation": "摘要聊天訊息以進行向量化處理", "Summarize chat messages when sending": "傳送時摘要聊天內容", "Swipe # for All Messages": "為所有訊息分配滑動編號 #", "System Message Sequences": "系統訊息序列", "System Prefix": "系統訊息前綴", "System Prompt Sequences": "系統提示詞序列", "System Suffix": "系統訊息後綴", "Tabby Model": "<PERSON><PERSON> 模型", "tag_import_all": "全部匯入", "tag_import_none": "不匯入", "Text Generation WebUI (oobabooga)": "文字生成 WebUI (oobabooga)", "The server MUST be started with the --embedding flag to use this feature!": "若要使用此功能，伺服器必須啟動時加上 --embedding 象徵。", "Threshold": "閾值", "to install 3rd party extensions.": "用於安裝第三方擴充功能。", "Top": "頂端", "Translate text to English before classification": "分類前，將訊息翻譯為英文", "Uncheck to hide the extensions messages in chat prompts.": "不勾選即可隱藏聊天提示詞中的擴充功能訊息。", "Unchecked: only entries with ❌ status can be activated.": "未勾選時：僅允許啟用狀態為 ❌ 的條目。", "Unified Sampling": "統一取樣（Unified Sampling）", "Upload sprite pack (ZIP)": "批次上傳立繪包（.ZIP）", "Use a forward slash to specify a subfolder. Example: _space": "使用「/」來設定子目錄，例如：_space", "Use ADetailer (Face)": "使用 ADetailer 進行臉部處理。", "Use an admin API key.": "使用管理員的 API 金鑰。", "Use global": "啟用全域設定", "User Message Sequences": "使用者訊息序列", "User Node Color": "使用者節點顏色", "User Prefix": "使用者訊息前綴", "User Suffix": "使用者訊息後綴", "Using a proxy that youre not running yourself is a risk to your data privacy.": "使用非自行管理的代理服務存在資料隱私洩漏風險。", "Vector Storage": "向量儲存", "Vector Summarization": "向量摘要", "Vectorization Model": "向量生成模型", "Vectorization Source": "向量化來源", "Vectorize All": "向量化全部資料", "View Stats": "檢視統計資料", "Warning: This might cause your sent messages to take a bit to process and slow down response time.": "警告：這可能會導致訊息處理速度變慢，並延長回應時間。", "WarningThis might cause your sent messages to take a bit to process and slow down response time.": "警告：這將顯著減緩向量生成速度，因為所有訊息都需先進行摘要。", "WebLLM Extension": "WebLLM 擴充功能", "Whole Words": "匹配完整單字", "Will be used if the API doesnt support JSON schemas or function calling.": "若 API 不支援 JSON 模式或函式呼叫，將使用此設定。", "World Info settings": "世界資訊設定", "You are in offline mode. Click on the image below to set the expression.": "您目前為離線狀態，請點選下方圖片進行表情設定。", "You can find your API key in the Stability AI dashboard.": "API 金鑰可在 Stability AI 儀錶板中檢視。", "Stop Inspecting": "停止檢查", "Inspect Prompts": "檢查提示詞", "Toggle prompt inspection": "切換提示詞檢查", "Top nsigma": "Top nsigma", "Controls the stopping condition for beam search. If checked, the generation stops as soon as there are '# of Beams' sequences. If not checked, a heuristic is applied and the generation is stopped when it's very unlikely to find better candidates.": "Controls the stopping condition for beam search. If checked, the generation stops as soon as there are '# of Beams' sequences. If not checked, a heuristic is applied and the generation is stopped when it's very unlikely to find better candidates.", "Confirm token parsing with": "確認符元（Token）解析方式", "KoboldAI Horde": "KoboldAI Horde", "KoboldAI Horde Website": "KoboldAI Horde 網站", "Derive context size from backend": "從後端推導上下文大小", "Using a proxy that you're not running yourself is a risk to your data privacy.": "使用非自行管理的代理服務可能導致您的資料隱私外洩。", "Claude API Key": "Claude API 金鑰", "Electron Hub API Key": "Electron Hub API 金鑰", "Electron Hub Model": "Electron Hub 模型", "NanoGPT API Key": "NanoGPT API 金鑰", "NanoGPT Model": "NanoGPT 模型", "context_derived": "若可能，根據模型後設資料推導。", "instruct_derived": "若可能，根據模型後設資料推導。", "Inserted before the first User's message.": "插入於第一則使用者訊息之前。", "0 = unlimited, 1 = scans once and doesn't recurse, 2 = scans once and recurses once, etc": "0 = 無限制，1 = 掃描一次不遞迴，2 = 掃描一次後遞迴一次 ⋯以此類推\n（啟用最小啟動次數時無效）", "Quick 'Impersonate' button": "快速「AI 扮演使用者」按鈕", "Manual": "手動", "Any contents here will replace the default Post-History Instructions used for this character. (v2 spec: post_history_instructions)": "此處填入的內容將取代該角色的預設聊天歷史後指示（Post-History Instructions）。\n（v2 格式：specpost_history_instructions）", "The content of this prompt is pulled from elsewhere and cannot be edited here.": "此提示內容由其他地方提取，無法在此進行編輯。", "Open checkpoint chat\nShift+Click to replace the existing checkpoint with a new one": "開啟檢查點聊天\n使用「Shift+點選」將以新檢查點替換現有的。", "Reroll with the entire prefix": "使用完整前綴重新生成", "Disable": "停用", "Enable": "啟用", "These files are available for the current character in all chats they are in.": "這些檔案適用於該角色所在的所有聊天。", "These files are available for all characters in the current chat.": "這些檔案適用於本次聊天中的所有角色。", "Set your API keys and endpoints in the 'API Connections' tab first.": "請先於「API 連線」選單中設定 API 金鑰和端點。", "Profile name:": "設定檔名稱：", "Creating a Connection Profile": "建立連線設定檔", "{{@key}}": "{{@key}}:", "Enter a name:": "輸入名稱：", "Omitted Settings:": "忽略的設定：", "Will be used if the API doesn't support JSON schemas or function calling.": "將於 API 不支援 JSON 結構或函式呼叫時使用。", "ext_sum_webllm": "WebLLM 擴充功能", "ext_sum_restore_tip": "恢復先前的摘要；重複使用以清除此聊天的摘要狀態。", "ext_sum_force_tip": "將立即更新摘要。", "ext_sum_include_wi_scan_desc": "於掃描世界資訊時包含最新摘要。", "ext_sum_include_wi_scan": "包含世界資訊掃描", "None (not injected)": "無（不插入）", "ext_sum_injection_position_none": "此摘要將不會插入提示詞中，但可透過 {{summary}} 巨集存取。", "Labels and Message": "標籤與訊息", "Label": "標籤", "(label of the button, if no icon is chosen) ": "（若未選擇圖示，則為按鈕的標籤）", "Title": "名稱", "(tooltip, leave empty to show message or /command)": "（工具提示，留空以顯示訊息或 /command）", "Message / Command:": "訊息／指令：", "Word wrap": "自動換行", "Tab size:": "標籤大小：", "Ctrl+Enter to execute": "以 Ctrl+Enter 執行", "Context Menu": "內容選單", "Auto-Execute": "自動執行", "Don't trigger auto-execute": "不觸發自動執行", "Invisible (auto-execute only)": "隱藏（僅自動執行）", "Execute on startup": "啟動時執行", "Execute on user message": "根據使用者訊息執行", "Execute on AI message": "根據 AI 訊息執行", "Execute on chat change": "聊天內容變更時執行", "Execute on new chat": "新增聊天時執行", "Execute on group member draft": "群組成員變更時執行", "Automation ID:": "自動化 ID：", "Testing": "測試", "Quick Reply": "快速回覆", "Enable Quick Replies": "啟用快速回覆", "Combine Quick Replies": "合併快速回覆", "Show Popout Button": "顯示彈出按鈕（桌面版）", "Global Quick Reply Sets": "全域快速回覆", "Chat Quick Reply Sets": "聊天快速回覆", "Edit Quick Replies": "編輯快速回覆", "Disable Send (Insert Into Input Field)": "停用傳送（插入到輸入欄位）", "Place Quick Reply Before Input": "在輸入前插入快速回覆", "Inject user input automatically": "自動插入使用者輸入", "(if disabled, use ": "（若停用，請使用", "macro for manual injection)": "巨集進行手動插入）", "Color": "顏色", "Only apply color as accent": "僅使用顏色作為強調", "ext_regex_new_global_script_desc": "新增「全域」正規表達式", "ext_regex_new_scoped_script_desc": "新增「區域」正規表達式", "ext_regex_disallow_scoped": "不使用區域正規表達式", "ext_regex_allow_scoped": "使用區域正規表達式", "ext_regex_user_input_desc": "使用者傳送的訊息。", "ext_regex_ai_input_desc": "從生成式 API 接收到的訊息。", "ext_regex_slash_desc": "使用 STscript 指令傳送的訊息。", "ext_regex_wi_desc": "世界資訊／知識書條目內容。需要勾選「僅格式化提示詞」！", "ext_regex_run_on_edit_desc": "當指定角色的訊息被編輯時執行正規腳本。", "Macro in Find Regex": "巨集替換模式", "Don't substitute": "不替換（純文字匹配）", "Substitute (raw)": "原始替換（不處理 *、. 等特殊字元）", "Substitute (escaped)": "轉義替換（將特殊字元 *、. 等當作普通文字處理）", "Ephemerality": "暫時性", "ext_regex_only_format_visual_desc": "僅改變聊天介面顯示的訊息，不修改聊天記錄檔案內容。", "Hint: Save an API key in Horde KoboldAI API settings to use it here.": "提示：請於 Horde KoboldAI API 設定中儲存 API 金鑰以進行使用。", "Prompt Upsampling": "提示提升（Upsampling）", "Uncheck to hide the extension's messages in chat prompts.": "取消選取可在聊天提示詞中隱藏擴充功能的訊息。", "ext_translate_delete_confirm_1": "確定要刪除嗎？", "ext_translate_delete_confirm_2": "這將「永久刪除」本次聊天中所有訊息的翻譯文字，且無法復原。", "Select TTS Provider": "選擇 TTS 提供者", "tts_enabled": "啟用", "Narrate user messages": "朗讀使用者訊息", "Auto Generation": "自動生成", "Requires auto generation to be enabled.": "需要啟用自動生成功能。", "Narrate by paragraphs (when streaming)": "按段落朗讀（使用「串流」時）", "Only narrate quotes": "僅朗讀「引號」中的文字", "Ignore text, even quotes, inside asterisk": "忽略 *（星號）內的文字（包括「引號」）", "Narrate only the translated text": "僅朗讀翻譯後的文字", "Skip codeblocks": "跳過程式碼塊", "Skip tagged blocks": "跳過 <標記> 塊", "Pass Asterisks to TTS Engine": "將 *（星號）視為普通文字傳送至 TTS 引擎（否則忽略）", "Warning: This will slow down vector generation drastically, as all messages have to be summarized first.": "警告：操作後將顯著降低向量生成速度，因為所有訊息都必須先進行摘要。", "Note:": "注意：", "this chat is temporary and will be deleted as soon as you leave it.": "此聊天為臨時聊天，離開後將被刪除。", "Import Tags For _begin": "為", "Import Tags For _end": "匯入標籤", "Click remove on any tag to remove it from this import.<br />Select one of the import options to finish importing the tags.": "點選任意標籤上的「移除」可將其於本次匯入中刪除。\n選擇一個匯入選項以完成標籤匯入。", "Existing Tags": "現有標籤", "New Tags": "新標籤", "Folder Tags": "資料夾標籤", "The following tags will be auto-imported based on the currently selected folders": "以下標籤將根據目前選擇的資料夾自動匯入", "Import None": "不匯入", "Import All": "全部匯入", "Import Existing": "匯入現有標籤", "Import": "匯入", "chat_rename_1": "輸入此聊天檔案的新名稱：", "chat_rename_2": "!! 使用已存在的檔案名稱將導致錯誤 !!", "chat_rename_3": "這將斷開各檢查點間的連結。", "chat_rename_4": "無需在結尾加上 `.jsonl`。", "Include Body Parameters": "包含請求主體參數", "custom_include_body_desc": "包含在 Chat Completion 請求體中的參數（YAML 格式）\n\n範例：\ntop_k: 20\nrepetition_penalty: 1.1", "Exclude Body Parameters": "排除請求主體參數", "custom_exclude_body_desc": "排除於 Chat Completion 請求體中的參數（YAML 格式）\n\n範例：\n- frequency_penalty\n- presence_penalty", "Include Request Headers": "包含請求標頭（Request Headers）", "custom_include_headers_desc": "新增於 Chat Completion 請求的自定義標頭（YAML 格式）\n\n範例：\nCustomHeader: custom-value\nAnotherHeader: custom-value", "THIS IS PERMANENT!": "這是「永久性」的！", "Also delete the chat files": "同時刪除此聊天檔案", "Are you sure you want to duplicate this character?": "您確定要複製該角色嗎？", "If you just want to start a new chat with the same character...": "若您只是想與該角色開始新聊天，請使用左下角選單中的「開始新聊天」。", "forbid_media_global_state_forbidden": "（禁止）", "forbid_media_global_state_allowed": "（允許）", "help_format_1": "文字格式化命令：", "help_format_2": "*文字*", "help_format_3": "顯示為", "help_format_4": "斜體", "help_format_5": "**文字**", "help_format_6": "顯示為", "help_format_7": "粗體", "help_format_8": "***text***", "help_format_9": "顯示為", "help_format_10": "粗斜體", "help_format_11": "__文字__", "help_format_12": "顯示為", "help_format_13": "底線", "help_format_14": "~~text~~", "help_format_15": "顯示為", "help_format_16": "刪除線", "help_format_17": "[text](url)", "help_format_18": "顯示為", "help_format_19": "超連結", "help_format_20": "![text](url)", "help_format_21": "顯示為圖片", "help_format_22": "```text```", "help_format_23": "顯示為程式碼區塊（反引號內允許換行）", "help_format_like_this": "像這樣", "help_format_24": "`text`", "help_format_25": "顯示為", "help_format_26": "單行程式碼", "help_format_27": "> text", "help_format_28": "顯示為塊引用（注意 > 後的空格）", "help_format_29": "# text", "help_format_30": "顯示為一級標題（注意空格）", "help_format_32": "## text", "help_format_33": "顯示為二級標題（注意空格）", "help_format_35": "### text", "help_format_36": "顯示為三級標題（注意空格）", "help_1": "您好！請選擇您想了解的幫助主題：", "help_2": "斜線命令", "help_or": "或", "help_3": "格式化", "help_4": "快捷鍵", "help_5": "{{macros}}（巨集）", "help_6": "還有問題嗎？請造訪", "help_7": "SillyTavern 官方文件網站", "help_8": " 了解更多資訊！", "help_hotkeys_0": "聊天快捷鍵", "help_hotkeys_1": "↑（方向鍵）", "help_hotkeys_2": "編輯聊天中的最後一則訊息", "help_hotkeys_3": "Ctrl+↑", "help_hotkeys_4": "編輯聊天中的最後一則使用者訊息", "help_hotkeys_5": "←（方向鍵）", "help_hotkeys_6": "向左滑動", "help_hotkeys_7": "→（方向鍵）", "help_hotkeys_8": "向右滑動（注意：若聊天框中已有輸入，滑動快捷鍵將被停用）", "help_hotkeys_9": "Enter", "help_hotkeys_10": "（選中聊天框時）", "help_hotkeys_10_1": "向 AI 傳送您的訊息", "help_hotkeys_11": "Ctrl+Enter", "help_hotkeys_12": "重新生成最後一則 AI 回應", "help_hotkeys_13": "Alt+Enter", "help_hotkeys_14": "繼續生成最後一則 AI 回應", "help_hotkeys_15": "Esc 鍵", "help_hotkeys_16": "停止 AI 回應生成，關閉使用者介面，取消訊息編輯", "help_hotkeys_17": "Ctrl+Shift+↑", "help_hotkeys_18": "滾動到上下文行", "help_hotkeys_19": "Ctrl+Shift+↓", "help_hotkeys_20": "Markdown 快捷鍵", "help_hotkeys_21": "適用於聊天框和帶有此圖示的文字區域：", "help_hotkeys_22": "**粗體**", "help_hotkeys_23": "*斜體*", "help_hotkeys_24": "__底線__", "help_hotkeys_25": "`單行程式碼`", "help_hotkeys_26": "~~刪除線~~", "Show Raw Prompt": "顯示原始提示詞", "Copy Prompt": "複製提示詞", "Show Prompt Differences": "顯示提示詞差異", "System-wide Replacement Macros (in order of evaluation):": "系統範圍替換巨集（按評估順序）：", "help_macros_1": "僅用於斜線命令批次處理。替換為前一條命令的返回結果。", "help_macros_2": "插入一個換行符。", "help_macros_3": "修剪巨集指令周圍的換行符。", "help_macros_4": "無操作，僅返回空字串。", "help_macros_5": "在 API 設定中定義的全域提示詞。僅在高階定義提示詞覆蓋中有效。", "help_macros_6": "使用者輸入", "help_macros_7": "角色的主要提示詞覆蓋", "help_macros_8": "角色的聊天歷史後指示覆蓋", "help_macros_9": "角色描述", "help_macros_10": "角色的性格特徵", "help_macros_11": "角色場景", "help_macros_12": "您目前的使用者描述", "help_macros_13": "角色對話範例", "help_macros_14": "未格式化的對話範例", "(only for Story String)": "（僅適用於故事字串）", "help_macros_summary": "由「訊息摘要」擴充功能生成的最新聊天摘要（如果可用）。", "help_macros_15": "您目前的使用者名稱", "help_macros_16": "角色名稱", "help_macros_17": "角色版本", "help_macros_18": "以逗號分隔的群組成員名稱列表（包含靜音成員）或單人聊天中的角色名稱。別名：{{charIfNotGroup}}", "help_groupNotMuted": "與 {{group}} 相同，但不包含靜音成員", "help_macros_19": "目前所選之 API 的文字生成模型名稱。", "Can be inaccurate!": "可能不準確！", "help_macros_20": "最新聊天訊息的文字內容。", "help_macros_lastUser": "最新使用者聊天訊息的文字內容。", "help_macros_lastChar": "最新角色聊天訊息的文字內容。", "help_macros_21": "最新聊天訊息的索引 # 編號。適用於斜線命令批次處理。", "help_macros_22": "包含在上下文中的第一條訊息的 ID。需在目前對話中至少進行一次生成。", "help_macros_23": "最新聊天訊息中所滑動的 ID（以 1 起始）。若最新訊息為使用者訊息或提示為隱藏，則為空字串。", "help_macros_24": "最新聊天訊息中的滑動次數。如果最新訊息為使用者訊息或提示為隱藏，則為空字串。", "help_macros_reverse": "反轉巨集的內容。", "help_macros_25": "您可以在此留下備註，巨集將被替換為空白內容。對 AI 不可見。", "help_macros_26": "目前時間", "help_macros_27": "目前日期", "help_macros_28": "目前星期幾", "help_macros_29": "目前 ISO 時間（24 小時制）", "help_macros_30": "目前 ISO 日期（YYYY-MM-DD）", "help_macros_31": "指定格式的目前日期／時間，例如，德國日期／時間：", "help_macros_32": "指定 UTC 時區偏移量的目前時間，例如 UTC-4 或 UTC+2", "help_macros_33": "計算 time1 和 time2 之間的時間差。接受時間和日期巨集。（例如：{{timeDiff::{{isodate}} {{time}}::2024/5/11 12:30:00}}）", "help_macros_34": "上次使用者訊息傳送後的時間", "help_macros_35": "設定 AI 的行為偏好，直到下一次使用者輸入。引號中的文字很重要。", "help_macros_36": "擲骰子。（例如：", "space_  will roll a 6-sided dice and return a number between 1 and 6)": "將擲一個六面骰並回傳 1 到 6 間的數字）", "help_macros_37": "從列表中返回隨機一項。（例如：", "space_  will return 1 of the 4 numbers at random. Works with text lists too.": "將隨機返回 4 個數字中的 1 個。也適用於文字列表。）", "help_macros_38": "用於隨機的替代語法，允許在列表中使用逗號。", "help_macros_39": "從列表中選擇隨機一項。工作原理與 {{random}} 相同，但選擇結果將在本次聊天中保持一致，不會在後續訊息或提示處理時重新滾動。", "help_macros_40": "若使用 Text Generation WebUI 後端，動態將引號中的文字新增到停用單詞序列中。對其他後端無效。可在任何地方使用（角色描述、世界資訊、作者備註等）。引號內容很重要。", "Instruct Mode and Context Template Macros:": "指令模式與上下文範本巨集：", "(enabled in the Advanced Formatting settings)": "（在高階格式化設定中啟用）", "help_macros_41": "允許的最大提示詞長度（以符元為單位）=（上下文大小 - 回應長度）", "help_macros_42": "上下文範本對話範例分隔符號", "help_macros_43": "上下文範本聊天開始行", "help_macros_44": "主要提示詞（啟用後，將覆蓋角色提示詞或預設系統提示）", "help_macros_45": "主要提示詞", "help_macros_46": "指令系統提示詞前綴序列", "help_macros_47": "指令系統提示詞後綴序列", "help_macros_48": "指令使用者前綴序列", "help_macros_49": "指令使用者後綴序列", "help_macros_50": "指令助理前綴序列", "help_macros_51": "指令助理後綴序列", "help_macros_52": "指令助理的開頭輸出序列", "help_macros_53": "指令助理的結尾輸出序列", "help_macros_54": "指令系統訊息前綴序列", "help_macros_55": "指令系統訊息後綴序列", "help_macros_56": "指令系統指令前綴", "help_macros_57": "指令第一則使用者訊息補全", "help_macros_58": "指令停止序列", "help_macros_first_user": "指令使用者開頭輸入序列", "help_macros_last_user": "指令使用者結尾輸入序列", "Chat variables Macros:": "聊天變數巨集：", "Local variables = unique to the current chat": "區域變數 = 僅作用於本次聊天", "Global variables = works in any chat for any character": "全域變數 = 作用於所有聊天中的所有角色", "Scoped variables = works in STscript": "區域變數 = 適用於 STscript", "help_macros_59": "替換為區域變數 \"name\" 的值", "help_macros_60": "替換為空字串，並將區域變數 \"name\" 設定為 \"value\"", "help_macros_61": "替換為空字串，並將 \"increment\" 數值新增到區域變數 \"name\"", "help_macros_62": "替換為區域變數 \"name\" 的值增加 1 後的結果", "help_macros_63": "替換為區域變數 \"name\" 的值減少 1 後的結果", "help_macros_64": "替換為全域變數 \"name\" 的值", "help_macros_65": "替換為空字串，並將全域變數 \"name\" 設定為 \"value\"", "help_macros_66": "替換為空字串，並將 \"increment\" 數值新增到全域變數 \"name\"", "help_macros_67": "替換為全域變數 \"name\" 的值增加 1 後的結果", "help_macros_68": "替換為全域變數 \"name\" 的值減少 1 後的結果", "help_macros_69": "替換為區域變數 \"name\" 的值", "help_macros_70": "替換為區域變數 \"name\" 中指定索引（適用於陣列／列表或物件／字典）的值", "{{name}}": "{{name}}", "If necessary, you can later restore this chat file from the /backups folder": "若需要，您可以稍後從 /backups 資料夾恢復此聊天檔案", "Also delete the current chat file": "同時刪除目前的聊天檔案", "Are you sure you want to connect to the following proxy URL?": "您確定要連線到以下代理 URL 嗎？", "Encountered an error while processing your request.": "處理您的請求時遇到錯誤。", "Check you have credits available on your": "請檢查您的帳號中是否有可用餘額", "OpenAI account quora_error": "OpenAI 帳號", "dot quota_error": "。", "If you have sufficient credits, please try again later.": "若餘額充足，請稍後重試。", "Download Model": "下載模型", "Downloader Options": "下載選項", "Extra parameters for downloading/HuggingFace API": "下載／使用 HuggingFace API 的額外參數。\r若不確定，請留空。", "Revision": "修訂", "Folder Name": "輸出資料夾名稱", "HF Token": "HF 符元", "Include Patterns": "包含模式", "Glob patterns of files to include in the download.": "要包含於下載中文件的全域模式。\r每行輸入一個模式。", "Exclude Patterns": "排除模式", "Glob patterns of files to exclude in the download.": "要排除於下載中的文件的全域模式。\r每行輸入一個模式。", "Tag Management": "管理標籤", "Save your tags to a file": "將標籤儲存到文件", "Restore tags from a file": "從文件中恢復標籤", "Create a new tag": "建立新標籤", "Drag handle to reorder. Click name to rename. Click color to change display.": "拖動以重新排序。點選名稱重新命名。點選顏色更改顯示。", "Click on the folder icon to use this tag as a folder.": "點選資料夾圖示以將此標籤作為資料夾。", "Use alphabetical sorting": "按字母順序排序 ", "tags_sorting_desc": "啟用後，標籤將在建立或重新命名時將自動按字母排序。\n停用時，新標籤將附加到結尾。\n若標籤被手動拖動重新排序，則自動排序將被停用。", "and connect to an": "並連線到", "You can add more": "您可加入更多", "or_welcome": "或", "from other websites": "以從其他網站新增。", "Go to the": "前往", "to install additional features.": "以安裝更多功能。", "If you're connected to an API, try asking me something!": "若您已連線 API，嘗試問我一些問題吧！", "Title/Memo": "標題／備註", "Strategy": "插入策略", "Position": "位置", "Trigger %": "觸發％", "Dialogue Colorizer": "對話著色器", "Global Dialogue Settings": "全域對話設定", "Chat Bubble Lightness": "聊天氣泡亮度", "The lightness to use for the chat bubble color.": "設定聊天氣泡的亮度。", "Character Dialogue Settings": "角色對話設定", "Dialogue settings for characters.": "設定角色對話顏色。", "The static color to use for character dialog if 'Color Source' is set to 'Static Color'.": "若將「顏色來源」設定為「靜態顏色」，請先設定該角色對話的靜態顏色。", "Persona Dialogue Settings": "使用者對話設定", "Persona Dialogue Settings Info": "設定使用者對話顏色。", "Dialogue Color": "對話顏色", "The color to use for this character's dialogue (quoted text). Overrides the global setting.": "該角色對話中引號文字的顏色。覆蓋全域設定。", "Avatar Vibrant": "頭像顏色", "Use a vibrant color dynamically calculated from the character's avatar.": "使用從角色頭像動態提取出的色彩。", "Static Color": "靜態顏色", "Use a specified static color.": "使用指定的靜態顏色。", "Per-Character Only": "僅限特定角色", "Use the default quote color except for characters with a specified override color.": "除非角色有指定的覆蓋顏色，否則使用預設的引號文字顏色。", "Chat Bubbles": "聊天氣泡", "Color the chat bubbles. Only works with the 'Bubbles' chat style.": "為聊天氣泡上色，僅適用於「氣泡」聊天樣式。", "Quoted Text": "引號文字", "Color quoted text.": "為引號文字上色。", "Color both chat bubbles and quoted text.": "同時為聊天氣泡和引號文字上色。", "Color Source": "顏色來源", "The source to use for dialogue color.": "設定對話顏色的來源。", "Color Targets": "顏色目標", "Which elements to color.": "設定需要上色的元素。", "Open Chat History": "開啟聊天記錄", "Reset": "重設", "Save": "儲存", " folder of your user directory (typically 'data/default-user'). Place your expressions there.": "資料夾內，放置您的角色立繪（通常為「data/default-user」）。", "Always show the node full info panel at the…e timeline view. When off, show it near the node.": "始終在時間軸檢視底端顯示節點的完整資訊面板。若關閉，則在節點附近顯示。", "Always show the node tooltip at the bottom …e timeline view. When off, show it near the node.": "始終在時間軸檢視左下角顯示節點的提示框。若關閉，則在節點附近顯示。", "Dialogue settings for user personas.": "Dialogue settings for user personas.", "Expand swipe nodes when the timeline view f… a node, or by pressing the Toggle Swipes button.": "在時間軸檢視首次開啟時展開滑動節點，或透過按下「切換滑動」按鈕來展開節點。", "Send as a character": "以角色身份傳送", "Use GPU acceleration for positioning the fu…ow tends to disappear, turning this off may help.": "使用 GPU 加速來定位完整資訊面板。若面板頻繁消失，建議關閉此選項以解決問題。", "Use the colors of the ST GUI theme, instead…n Color Settings specifically for this extension.": "使用使用者設定中的介面主題顏色，取代下方「顏色設定」中額外設定的顏色。", "When enabled, nodes that have swipes splitt… larger, in addition to having the double border.": "啟用後，具有分支滑動的節點除了顯示雙重邊框外，還會顯示為更大的尺寸。", "Brightness": "亮度", "Character Tint Settings": "角色色調設定 *", "Commands": "指令", "Contrast": "對比度", "Darken Unfocused Character Sprites": "暗化未聚焦的角色立繪", "Delete tint": "儲存色調", "Ease": "平滑過渡", "Ease-In": "淡入", "Ease-In-Out": "淡入 + 淡出", "Ease-Out": "淡出", "Emulate Character Card as Sprite": "[測試版] 模擬角色卡為角色立繪", "Enable Character Tint (Requires Prome to be enabled)": "啟用角色色調", "Enable Chat Tint": "啟用聊天色調", "Enable Focus Mode": "啟用聚焦模式", "Enable Prome": "啟用 Prome", "Enable Sprite Shadow": "啟用角色立繪陰影效果", "Enable Sprite Shake": "[測試版] 啟用角色立繪震動效果", "Enable Traditional VN Mode": "啟用傳統視覺小說模式 *", "Enable User Sprite": "啟用使用者立繪", "Enable World Tint": "啟用世界色調", "Features marked with a": "若想使用帶有", "Focus Mode Animation": "聚焦模式動畫", "Focus Mode Animation Speed": "聚焦模式動畫速度", "Focus Mode Settings": "聚焦模式設定", "Grayscale": "灰階", "Hide Sheld (Message Box)": "隱藏聊天訊息欄位", "Horizontal Letterbox": "水平遮罩", "Hue": "色相", "Invert": "反轉", "Inverts the character colors.": "反轉角色顏色。", "Inverts the world colors.": "反轉世界顏色。", "Keybinds": "快捷鍵", "Letterbox Color": "遮罩顏色", "Letterbox Configuration*": "遮罩設定 *", "Letterbox Mode": "遮罩模式", "Letterbox Size": "遮罩大小", "Makes the character black and white.": "使角色使用灰階效果。", "Makes the character warmer in color.": "使角色色調更暖。", "Makes the world black and white.": "使世界使用灰階效果。", "Makes the world warmer in color.": "使世界色調更暖。", "Note: Create a sprite folder in the ": "注意：請在", "require Prome to be enabled.": "標記的功能，需要啟用「Prome」。", "Saturate": "飽和", "Saturates the character colors.": "增加角色顏色飽和度。", "Saturates the world colors.": "增加世界顏色飽和度。", "Save tint": "刪除色調", "Select the animation for focus mode.": "選擇聚焦模式的動畫效果。", "Select the color of the letterbox.": "選擇遮罩顏色。", "Select the letterbox mode for the Prome VN UI.": "選擇 Prome 視覺小說介面的遮罩模式", "Select the tint preset to use for the Prome VN UI.": "選擇用於 Prome 視覺小說介面的色調預設設定檔。", "Sepia": "復古", "Set the blur of the character shadow.": "設定角色陰影模糊程度。", "Set the brightness of the character.": "設定角色亮度。", "Set the brightness of the world.": "設定世界亮度。", "Set the contrast of the character.": "設定角色對比度。", "Set the contrast of the world.": "設定世界對比度。", "Set the hue of the character.": "設定角色色相。", "Set the hue of the world.": "設定世界色相。", "Set the size of the letterbox.": "設定遮罩大小。", "Set the speed of the focus animation.": "設定聚焦動畫速度。", "Set the strength of the character blur.": "設定角色模糊強度。", "Set the strength of the world blur.": "設定世界模糊強度。", "Set the X offset of the character shadow.": "設定角色陰影的 X 軸偏移量。", "Set the Y offset of the character shadow.": "設定角色陰影的 Y 軸偏移量。", "Shadow Blur": "模糊陰影", "Shadow X Offset": "X 軸偏移量", "Shadow Y Offset": "Y 軸偏移量", "Share World Tint With Characters": "角色適用世界色調 *", "Sheld Configuration": "設定聊天訊息欄位", "Sprite Configuration": "設定角色立繪 *", "Sprite List": "角色立繪列表", "Sprite Shadow Configuration": "設定角色立繪陰影", "Tint Configuration": "設定色調", "Tint Presets": "色調預設設定檔", "Type the name of the sprite set to use for your pe…rites in the 'characters' folder in SillyTavern).": "輸入您要使用的個人角色立繪集名稱（需將立繪存放於 SillyTavern 中的「characters」資料夾內）。", "User Sprite Configuration": "[測試版] 使用者立繪設定", "Vertical Letterbox": "垂直遮罩", "World Tint Settings": "設定世界色調", "Prome (Visual Novel Extension)": "Prome（進階視覺小說模式）", "Brought to you by": "由", "and Prometheus.": "與 Prometheus（普羅米修斯十七號）呈獻。", "Type the name of the sprite set to use for your persona. (Place your sprites in the 'characters' folder in SillyTavern).": "輸入您想用於個人立繪的立繪集名稱。（請將立繪放置在 SillyTavern 的「characters」資料夾中）。", "Prome Keybinds": "Prome 的快捷鍵", "Hide/Show SillyTavern's Sheld (Message Box)": "隱藏／顯示 SillyTavern 的聊天訊息欄位", "Prome Commands": "Prome 指令", "Show/Hide the letterbox (black bars) in the VN UI": "顯示／隱藏視覺小說模式中的黑邊（信箱模式）", "Toggles focus mode on character sprites": "切換角色立繪的焦點模式", "Sets the focus mode animation": "設定焦點模式動畫", "Toggles the defocus tint on non-speaking character sprites": "切換非對話角色立繪的背景色", "Toggles the shake animation when a character speaks on character sprites": "切換角色立繪對話時的震動動畫", "Toggles sprite shadows on character sprites": "切換角色立繪的陰影效果", "Toggles world/character tint on the VN UI": "切換視覺小說模式中的世界／角色色調", "Toggles world tint on the VN UI": "切換視覺小說模式中的世界色調", "Toggles character tint on the VN UI": "切換視覺小說模式中的角色色調", "Toggles sharing world tint with character sprites (This will override Character Tint)": "切換角色立繪是否與世界色調共享色調（此操作將覆蓋角色色調設定）", "Sets the expression of the user sprite": "設定使用者立繪的表情", "Sets the user sprite set to use for the user sprite": "設定使用者立繪所使用的立繪集", "Toggles the user sprite on the VN UI": "切換視覺小說模式中使用者立繪的顯示狀態", "Close": "關閉", "View this current chat's chat history.": "檢視本次聊天的聊天記錄。", "WARNING: Functions in this category are for advanced users only. Don't click anything if you're not sure about the consequences.": "警告：此類功能僅適用於進階使用者。若您不確定使用後果，請勿點選任何按鈕。", "Enter a new display name:": "輸入新的顯示名稱：", "Enter Checkpoint Name:": "輸入檢查點名稱：", "(Leave empty to auto-generate)": "（留空將自動命名）", "The currently existing checkpoint will be unlinked and replaced with the new checkpoint, but can still be found in the Chat Management.": "此檢查點將取消連結並替換為新的檢查點，但仍可在「管理聊天檔案」中找到。", "Enter the Git URL of the extension to install": "輸入欲安裝的擴充功能 Git URL", "Disclaimer:": "免責聲明：", "Please be aware that using external extensions can have unintended side effects and may pose security risks. Always make sure you trust the source before importing an extension. We are not responsible for any damage caused by third-party extensions.": "請注意，使用外部擴充功能可能會導致意想不到的副作用並存在安全風險。匯入前，請務必確保您信任其來源。我們對於第三方擴充功能所引起的任何損害概不負責。", "Prompt Itemization": "提示詞項目化", "API/Model": "API／模型", "Preset": "預設設定檔", "Only the white numbers really matter. All numbers are estimates. Grey color items may not have been included in the context due to certain prompt format settings.": "所有數字均為估算值，僅白色數字真正重要。灰色項目可能因提示詞格式設定未納入上下文。", "System Info:": "系統資訊：", "Bias:": "Bias:", "World Info:": "世界資訊：", "Chat History:": "聊天記錄：", "Extensions:": "擴充功能：", "Total Tokens in Prompt:": "提示詞中的總符元數：", "Max Context": "最大上下文長度", "(Context Size - Response Length)": "（上下文長度 - 回應長度）", ":": "：", "API/Model:": "API／模型：", "Preset:": "預設設定檔：", "Tokenizer:": "分詞器：", "Choose what to export": "選擇匯出內容", "Text Completion Preset": "文字補全預設設定檔", "Choose what to import": "選擇匯入內容", "Enter your password below to confirm:": "請在下方輸入密碼以完成確認：", "Unique to this chat.": "此設定僅適用於本次聊天。", "The following scenario text will be used instead of the value set in the character card.": "以下場景內容將覆蓋角色卡中的設定值。", "Checkpoints inherit the scenario override from their parent, and can be changed individually after that.": "檢查點將繼承父項的場景覆蓋值，但仍可獨立修改。", "Are you sure you want to delete the theme?": "您確定要刪除介面主題嗎？", "This will delete all your settings and data. There will be no undo button. Make sure you have a backup before proceeding.": "此操作將刪除所有設定與資料，且無法還原。進行重設前請務必完成備份。", "Account reset code has been posted to the server console.": "帳號重設驗證碼已傳送至伺服器控制台。", "Prompt Tokens:": "提示詞符元數：", "All group members will use the following scenario text instead of what is specified in their character cards.": "所有群組聊天成員將使用以下場景內容，取代原有角色卡中指定的內容。", "Toggle sidebar": "切換側邊欄", "Show connection profiles": "顯示連線設定檔", "View chat files": "檢視聊天檔案", "New chat": "新聊天", "Rename chat": "重新命名聊天", "Delete chat": "刪除聊天", "Are you sure?": "你確定嗎？", "Enter new chat name": "輸入新的聊天名稱", "No chat selected": "未選擇聊天", "Draggable template not found. Side bar will not be added.": "未找到可拖動範本。側邊欄將不被新增。", "Failed to find draggable or close button. Side bar will not be added.": "未找到可拖動項或關閉按鈕。側邊欄將不被新增。", "Sidebar or toggle button not found": "未找到側邊欄或切換按鈕", "Switch connection profile": "切換連線設定檔", "Failed to get current API": "取得 API 失敗", "Failed to get current model": "取得模型失敗", "Aborting populateSideBar due to process id mismatch": "由於 populateSideBar ID 不匹配，中止填充側邊欄", "Bronya Rand": "Bronya Rand（布洛妮婭·蘭德）", "Toggles Prome, VN Mode and other Prome features.": "切換 Prome、視覺小說模式和其他 Prome 功能。", "Only Show Last Message in Chat (Requires Prome to be enabled).": "僅顯示聊天中的最後一條訊息（需啟用 Prome）。", "Emulates the character card of a character to be a sprite. (Requires Prome to be enabled).": "將角色的角色卡圖片模擬為角色立繪（需啟用 Prome）。", "Shakes the character sprite when the character is speaking (Only works if Streaming is enabled in Preset Settings).": "當角色說話時，震動角色的立繪（僅在預設設定檔中啟用「串流」時有效）。", "Focuses the current speaking character in chat. (Requires Prome to be enabled).": "聚焦聊天中目前正在說話的角色（需啟用 Prome）。", "Darkens non-speaking (unfocused) characters. (Requires Prome to be enabled).": "使未說話（未聚焦）的角色變暗（需啟用 Prome）。", "Auto-hides characters from the screen that haven't been in the conversation for a while up to X characters. (Requires Prome to be enabled).": "自動隱藏未參與會話一段時間的角色，最多 X 個角色（需啟用 Prome）。", "Enables the ability to use a user sprite for your persona.": "啟用後，將為使用者的角色使用角色立繪功能。", "Applies the world tint to character sprites (Requires Prome to be enabled. This will override your character tint settings).": "將世界色調應用於角色立繪（需啟用 Prome，這將覆蓋角色色調設定）。", "Tints the world background.": "為世界背景新增色調。", "Tints the character sprites.": "為角色立繪新增色調（需啟用 Prome）。", "Auto-Hide Sprites": "自動隱藏立繪", "Max Visible Sprites": "最大顯示數", "Set the maximum number of visible sprites that appears in the VN screen.": "設定視覺小說模式中，畫面可顯示的最大立繪數量。", "Hide the message box (sheld) in the ST UI.": "隱藏 SillyTavern 介面中的聊天訊息欄位。", "Enter the name of your sprite": "輸入立繪名稱", "Start new chat?": "開始新聊天？", "Delete the character?": "您確定要刪除角色嗎？", "It will overwrite the World/Lorebook with the same name.": "它將覆蓋同名的世界資訊或知識書。", "All information associated with its linked persona will be lost.": "與這位使用者相關的所有資訊都將遺失。", "Are you sure you want to duplicate this persona?": "您確定要複製使用者角色嗎？", "(If empty name is provided, this will unbind the name from this avatar)": "（若留空，則解除該名稱與頭像的綁定）", "Enter a name for this persona:": "輸入使用者角色的名稱：", "Are you sure you want to set \"${0}\" as the default persona?": "您確定要將「${0}」設為預設角色嗎？", "This name and avatar will be used for all new chats, as well as existing chats where the user persona is not locked.": "此名稱和頭像將用於所有新對話，以及目前尚未綁定使用者角色的聊天。", "Are you sure you want to remove the default persona?": "您確定要取消目前的使用者角色預設嗎？", "Install for all users": "為所有使用者安裝", "Install just for me": "為自己安裝", "No Icon": "不使用圖示", "Remove Quick Reply": "移除快速回覆", "Are you sure you want to remove this Quick Reply?": "你確定要移除此快速回覆嗎？", "Middle-out Transform": "中間向外變換", "Auto": "自動", "Allow": "允許", "Forbid": "禁止", "Aphrodite only. Determines the order of samplers. Skew is always applied post-softmax, so it's not included here.": "僅限 Aphrodite 使用。決定取樣器的順序。偏移總是在 softmax 後應用，因此不包括在此。", "Aphrodite only. Determines the order of samplers.": "僅限 Aphrodite 使用。決定取樣器的順序。", "Request model reasoning": "請求模型思維鏈", "Allows the model to return its thinking process.": "讓模型回傳其思考過程。", "Generic (OpenAI-compatible) [LM Studio, LiteLLM, etc.]": "通用（相容 OpenAI）[LM Studio, LiteLLM 等]", "Model ID (optional)": "模型 ID（可選）", "DeepSeek API Key": "DeepSeek API 金鑰", "DeepSeek Model": "DeepSeek 模型", "prompt_post_processing_merge": "合併連續角色", "prompt_post_processing_semi": "半嚴格（交替角色）", "prompt_post_processing_strict": "嚴格（使用者優先，交替角色）", "Background Fitting": "背景填充", "Classic": "經典", "Cover": "覆蓋", "Contain": "自適應", "Stretch": "拉伸", "Center": "置中", "Persona Lore Alt+Click to open the lorebook": "「Alt+點選」可開啟角色知識書", "Chat Lore Alt+Click to open the lorebook": "「Alt+點選」可開啟聊天知識書", "Function Tool": "功能工具", "Functions in this category are for advanced users only. Don't click anything if you're not sure about the consequences.": "此類功能僅供高階使用者使用。若不確定後果，請勿點選任何內容。", "Are you sure you want to delete this user?": "確定要刪除該使用者嗎？", "help_macros_isMobile": "目前是否在行動端使用：\"true\" 表示是，\"false\" 表示否", "Persona Lorebook for": "角色知識書適用於", "persona_world_template_txt": "選中的世界資訊將綁定到此角色。生成 AI 回覆時，會結合全域、角色及聊天知識書中的內容。", "Key saved; press \"Test Message\" to verify.": "金鑰已儲存；請點選「測試訊息」進行驗證。", "Preset name:": "預設設定檔名稱：", "Hint: Use a character/group name to bind preset to a specific chat.": "提示：使用角色／群組名稱將綁定預設設定檔至特定對話。", "Your preset contains proxy and/or custom endpoint settings.": "此預設設定檔包含代理和／或自訂端點設定。", "Do you want to remove these fields before exporting?": "是否要在匯出前移除這些欄位？", "Delete the preset? This action is irreversible and your current settings will be overwritten.": "確定刪除此預設設定檔？刪除後無法復原，且此設定將被覆蓋。", "Update all": "全部更新", "Automatically chooses an alternative provider if chosen providers can't serve your request.": "當所選供應商無法滿足您的請求時，自動選擇替代供應商。", "Use extension settings": "使用擴充功能設定", "To use instruct formatting, switch to OpenRouter under Text Completion API.": "若要使用指令格式，請在文字補全 API 下切換至 OpenRouter。", "Automatically 'continue' a response if the model stopped before reaching a certain amount of tokens.": "如果模型在達到一定數量的符元前停止，則自動繼續生成回應。", "Toggle entry's active state.": "切換條目的啟用狀態。", "Non-sticky": "無黏性", "No cooldown": "無冷卻時間", "No delay": "無延遲", "Included settings:": "包含設定：", "Click on the setting name to omit it from the profile.": "點選設定名稱以從設定檔中省略。", "Tints the chat background and/or character sprites.": "調整聊天背景或角色圖片的色調。", "Only chunk on custom boundary": "僅在自定邊界進行分塊（chunk）", "help_macros_firstDisplayedMessageId": "載入到可見聊天中的第一則訊息的 ID。", "Couldn't import tags:": "無法匯入標籤：", "Select providers. No selection = all providers.": "選擇供應商。未選擇＝所有供應商。", "Select a model": "選擇模型", "Search models...": "搜尋模型⋯", "[Currently loaded]": "[目前載入]", "Search providers...": "搜尋供應商⋯", "No-sticky": "無固定", "Create a new World Info": "建立新世界資訊", "Enter a name for the new file:": "輸入新檔案的名稱：", "Valid World Info file name is required": "需要有效的世界資訊檔案名稱", "World Info file has an invalid format": "世界資訊檔案格式無效", "World Info file has no entries": "世界資訊檔案中無任何條目", "Character not found.": "未找到該角色。", "Open a chat to get a name of the chat-bound lorebook": "開啟聊天以取得綁定聊天的知識書名稱。", "File is not valid: ${0}": "檔案無效或格式不正確：${0}", "The world with ${0} is invalid or corrupted.": "世界 ${0} 無效或已損壞。", "Deactivated all worlds": "已停用所有世界", "No world found named: ${0}": "未找到名為 ${0} 的世界", "Activated world: ${0}": "已啟用世界：${0}", "Deactivated world: ${0}": "已停用世界：${0}", "World was not active: ${0}": "世界 ${0} 未啟用", "The world '${0}' has been imported and linked to the character successfully.": "世界「${0}」已成功匯入並綁定至角色。", "World/Lorebook imported": "世界／知識書已匯入", "Are you sure you want to import '${0}'?": "確定要匯入「${0}」嗎？", "Built-in Extensions:": "內建擴充功能：", "Installed Extensions:": "已安裝的擴充功能：", "Loading third-party extensions... Please wait...": "正在載入第三方擴充功能，請稍候⋯", "The page will be reloaded shortly...": "頁面即將重新載入⋯", "Extensions state changed": "擴充功能狀態已更改", "Error loading extensions. See browser console for details.": "載入擴充功能時出現錯誤。詳細資訊請檢視瀏覽器控制台。", "You don't have permission to update global extensions.": "您無權更新全域擴充功能。", "Extension update failed": "擴充功能更新失敗", "Extension ${0} updated to ${1}": "擴充功能 ${0} 已更新至 ${1}", "Reload the page to apply updates": "重新載入頁面以使用更新", "You don't have permission to delete global extensions.": "您無權刪除全域擴充功能。", "Are you sure you want to delete ${0}?": "確定要刪除 ${0} 嗎？", "You don't have permission to move extensions.": "您無權移動擴充功能。", "Are you sure you want to move ${0} to your local extensions? This will make it available only for you.": "確定要將 ${0} 移至本機擴充功能嗎？此後僅您可使用。", "Are you sure you want to move ${0} to the global extensions? This will make it available for all users.": "確定要將 ${0} 移至全域擴充功能嗎？此後所有使用者皆可使用。", "Extension ${0} moved.": "擴充功能 ${0} 已移動。", "Extension ${0} deleted": "擴充功能 ${0} 已刪除。", "Please wait...": "請稍候⋯", "Installing extension": "正在安裝擴充功能", "Extension installation failed": "擴充功能安裝失敗", "Extension '${0}' by ${1} (version ${2}) has been installed successfully!": "擴充功能 '${0}' 由 ${1} 提供（版本 ${2}）已成功安裝！", "Extension installation successful": "擴充功能安裝成功", "Extension updates available": "有可用的擴充功能更新", "Auto-updating extensions. This may take several minutes.": "正在自動更新擴充功能。這可能需要幾分鐘時間。", "Install": "安裝", "Modules provided by your Extras API:": "由您的 Extras API 提供的模組：", "Not connected to the API!": "未連線到 API！", "ext_type_system": "這是內建的擴充功能，無法刪除，且會跟隨系統更新。", "Valid": "已驗證", "Request Model Reasoning": "請求模型推理", "Global list": "全域列表", "Preset-specific list": "特定預設設定檔列表", "Constrains effort on reasoning for reasoning models.": "限制推理模型的推理耗費。\n目前支援的值為低、中和高。\n降低推理耗費可加快回應速度，並減少推理所使用的符元數量。", "Reasoning Effort": "推理耗費", "openai_reasoning_effort_low": "低", "openai_reasoning_effort_medium": "中", "openai_reasoning_effort_high": "高", "Reasoning": "推理", "reasoning_auto_parse": "自動解析主要內容中推理區塊，需定義且不為空的前綴與後綴欄位。", "Auto-Parse": "自動解析", "reasoning_auto_expand": "自動展開推理區塊。", "Auto-Expand": "自動展開", "reasoning_show_hidden": "顯示隱藏推理功能模型的推理時間", "Show Hidden": "顯示隱藏內容", "reasoning_add_to_prompts": "將現有推理區塊新增至提示詞中。若需新增推理區塊，請使用訊息編輯選單。", "Add to Prompts": "新增至提示詞", "reasoning_max_additions": "從最後一則訊息起算，每則提示詞中可新增的最大推理區塊數量。", "Max": "最大值", "Reasoning Formatting": "推理格式", "reasoning_prefix": "插入於推理內容之前。", "Prefix": "前綴", "reasoning_suffix": "插入於推理內容之後。", "Suffix": "後綴", "reasoning_separator": "插入於推理內容與訊息內容之間。", "Separator": "分隔符號", "Character details are hidden.": "角色詳細資訊已隱藏。", "Add a reasoning block": "新增推理區塊", "Thought for some time": "思考了一段時間", "Confirm Edit": "確認", "Remove reasoning": "移除推理", "Cancel edit": "取消編輯", "Copy reasoning": "複製推理", "Edit reasoning": "編輯推理", "extension_install_1": "若要從此頁面下載擴充功能，您需要安裝", "extension_install_2": "已安裝。", "extension_install_3": "點選", "extension_install_4": "圖示以存取擴充功能的儲存庫，檢視使用技巧。", "Use the selected API from Chat Translation extension settings.": "使用擴充功能設定中，「聊天翻譯」所選的翻譯提供者（API）。", "A single expression can have multiple sprites. Whenever the expression is chosen, a random sprite for this expression will be selected.": "單個同名表情可以有多張角色立繪。每次使用該表情時，會隨機擇一顯示。", "Allow multiple sprites per expression": "允許單一表情使用多張立繪", "If the same expression is used again, re-roll the sprite. This only applies to expressions that have multiple available sprites assigned.": "若再次使用相同的表情，將重新隨機選擇。此功能僅適用於分配了多張立繪的表情。", "Re-roll if same expression is used again": "重複使用同名表情時，隨機選用其他立繪", "upload_expression_request": "請輸入角色立繪名稱（不含副檔名）。", "upload_expression_naming_1": "角色立繪名稱必須符合所選表情的命名規則：{{expression}}", "upload_expression_naming_2": "對於多個表情，名稱必須包含表情名稱和有效的後綴，允許的分隔符號為「-」或「.」。", "upload_expression_replace": "點選「取代」以取代現有表情：", "ext_regex_reasoning_desc": "推理區塊內容。當「僅格式化提示詞」已勾選時，這也會影響新增至提示詞的推理內容。", "Token Counter": "符元計數器", "Type / paste in the box below to see the number of tokens in the text.": "在下框中輸入或貼上文字以檢視符元（Token）數量。", "Selected tokenizer:": "選擇的分詞器：", "Input:": "輸入：", "Tokens:": "符元數：", "Tokenized text:": "已符元化的文字：", "Token IDs:": "符元 ID：", "Narrate by paragraphs (when not streaming)": "按段落朗讀（不使用「串流」時）", " folder (typically in ": "資料夾（通常位於 ", "Copy to Clipboard": "複製到剪貼簿", "Reset to Defaults": "重設為預設值", "Toggles Guinevere features.": "切換 Guinevere 功能。", "Update customCSS": "更新 customCSS", "Apply Theme": "套用主題", "Enable Guinevere": "啟用 Guinevere", "Note: Themes can be made/applied by going to the ": "注意：主題可通過前往以下位置進行創建／應用", "Theme Name": "主題名稱", "An unknown error occurred while counting tokens. Further information may be available in console.": "計算符元時發生未知錯誤。更多資訊可能可在主控台（console）中查看。", "Type the folder name of the theme you want to apply.": "輸入您想套用的主題資料夾名稱。", "Place your theme data in a folder.": "請將主題資料存於該資料夾內。", "Unsure where to start? Type ": "不確定如何開始？輸入：", " to apply the default Google Messages theme or click ": " 即可使用預設主題 Google Messages，或點選", "here": "這裡", " to learn how to create your own theme.": " 以了解如何創建個人化主題。", "Guinevere (UI Theme Extension)": "G<PERSON>evere（進階自定義 UI 主題）", "and Guinaifen.": "和 Guinaifen（桂乃芬）呈獻。", "(Requires Prome to be enabled)": "（需啟用 Prome）", "[ None ]": "[無]", "Group is empty.": "群組為空。", "No characters available": "無可用角色", "Adds a shadow to the character sprite.": "為角色立繪新增陰影。", "Allocates a portion of the response length for thinking (low: 10%, medium: 25%, high: 50%). Other options are model-dependent.": "分配部分回應長度用於推理功能（低：10%、中：25%、高：50%）。其他選項依模型而異。", "Attach a file or image to a current chat.": "附加檔案或圖片至目前的聊天。", "Change Persona Image": "變更使用者角色頭像", "Click to lock your selected persona to the current character. Click again to remove the lock.": "點選以將所選使用者角色與目前聊天的角色綁定。再次點選可解除綁定。", "Click to select this as default persona for the new chats. Click again to remove it.": "點選以將此設為新聊天的預設使用者角色，再次點選可取消設定。", "Connected Personas": "已連結的使用者角色", "Create a new prompt": "新增新提示詞", "Delete a prompt": "刪除提示詞", "Delete Persona": "刪除使用者角色", "Duplicate Persona": "複製使用者角色", "Expand and zoom": "展開並縮放", "Move/Copy Entry to Another Lorebook": "將條目移動/複製到其他知識書", "Persona is locked to the current character": "使用者角色已綁定至目前角色", "Persona is locked to the current chat": "使用者角色已綁定至目前聊天", "Rename a prompt": "重新命名提示詞", "Rename Persona": "重新命名使用者角色", "Select your current Reasoning Template": "選擇你目前使用的推理範本", "Toggles sprite scaling for character sprites.": "切換角色立繪的縮放比例", "When multiple personas are connected to a character, a popup will appear to select which one to use.": "當多個使用者角色連接至一個角色時，將顯示選擇彈窗。", "When using LLM or WebLLM classifier, only show and use expressions that have sprites assigned to them.": "使用 LLM 或 WebLLM 分類器時，僅顯示並使用已指派的表情立繪。", "Whenever a persona is selected, it will be locked and automatically selected when the chat is opened.": "若已選擇使用者角色，將在開啟聊天時自動進行綁定。", "Additional Matching Sources": "額外匹配來源", "All Classes": "所有類別", "Allow multiple persona connections per character": "允許每個角色連接多個使用者角色", "Allows the model to return image attachments.": "允許模型返回圖片附件。", "Auto-Hide Sprite Settings": "自動隱藏立繪設定", "Auto-lock a chosen persona to the chat": "自動綁定使用者角色至聊天中", "Character": "角色", "Character Personality": "角色個性", "Character Tint/Share World Tint With Characters requires Prome to be enabled.": "角色著色／與角色共享世界著色功能需啟用 Prome。", "Chat": "聊天", "Click_space": "點選", "Connections": "連接", "Context": "上下文", "Current Persona": "目前使用者角色", "Date Asc": "日期升序", "Date Desc": "日期降序", "Enable Sprite Scale": "啟用立繪縮放", "Enable web search": "啟用網路搜尋", "Filter expressions for available sprites": "篩選可用的立繪表情", "Focus Settings": "焦點設定", "General Sprite Settings": "通用立繪設定", "Global Settings": "全域設定", "Go back": "返回", "Hide Sheld": "隱藏聊天欄位（#Sheld）", "in this group": "在此群組中", "Letterbox Configuration (Requires Prome to be enabled)": "信箱設定（需啟用 Prome）", "openai_reasoning_effort_auto": "自動", "openai_reasoning_effort_maximum": "最大", "openai_reasoning_effort_minimum": "最小", "openrouter_web_search_fee": "需付費。每次提示詞將額外收取 0.02 美元費用。", "Pooled order": "合併順序", "Request inline images": "請求內嵌圖片", "Request inline images_desc_2": "與以下功能不相容：函數調用、網路搜尋、系統提示詞。", "Response": "回應", "Secondary Embedding endpoint URL": "次要嵌入端點 URL", "Set the size scale of the character sprites.": "設定角色立繪的大小比例。", "Sprite Scale": "立繪縮放", "Sprite Shadow Settings": "立繪陰影設定", "tag_entries": "條目", "Use search capabilities provided by the backend.": "使用後端提供的搜尋功能。", "Use secondary URL": "使用次要 URL", "User Sprite Settings": "[測試版] 使用者立繪設定", "xAI API Key": "xAI API 密鑰", "xAI Model": "xAI 模型", "關閉": "關閉", "Reset custom sampler selection": "重設自定取樣器選擇", "Here you can toggle the display of individual samplers. (WIP)": "可在此切換各取樣器的顯示狀態（開發中）", "Tie this entry to specific characters or characters with specific tags": "將此項綁定至特定角色或標籤", "There are no other lorebooks to move to.": "暫無其他知識書可供移動。", "Select Target Lorebook": "選擇目標知識書", "Move/Copy '${0}' to:": "將「${0}」移動/複製至：", "Please select a target lorebook.": "請選擇目標知識書。", "Scan depth cannot be negative": "掃描深度不能為負值", "Scan depth cannot exceed ${0}": "掃描深度不能超過 ${0}", "Reasoning Template": "推理範本", "OpenAI-style options: low, medium, high. Minimum and maximum are aliased to low and high. Auto does not send an effort level.": "OpenAI 支援程度：低、中、高。Minimum 與 Maximum 分別等同於低與高。選擇 Auto（自動）不會傳送推理耗費等級。", "Branch or tag name (optional)": "分支／標籤名稱（可留空）", "Update enabled": "僅更新啟用項目", "Sort: Loading Order": "排序：載入順序", "Sort: Display Name": "排序：顯示名稱", "Multiple personas are connected to this character.\nSelect a persona to use for this chat.": "此角色已綁定多個使用者角色，請選擇用於本次對話的角色。", "Select Persona": "選擇使用者角色", "Move ${0} to:": "將 ${0} 移至：", "Delete Tag": "刪除標籤", "Do you want to delete the tag": "確定要刪除此標籤嗎？", "If you want to merge all references to this tag into another tag, select it below:": "若想將所有引用合併至其他標籤，請於下方選擇目標標籤：", "Open Folder (Show all characters even if not selected)": "開啟資料夾（顯示所有角色，即使未被選取）", "Closed Folder (Hide all characters unless selected)": "關閉資料夾（僅顯示選取的角色）", "No Folder": "無資料夾", "Show only favorites": "僅顯示最愛角色", "Show only groups": "僅顯示群組", "Show only folders": "僅顯示資料夾", "Manage tags": "標籤管理", "Show Tag List": "顯示標籤列表", "Clear all filters": "清除所有篩選器", "There are no items to display.": "暫無可顯示的項目。", "Characters and groups hidden by filters or closed folders": "部分角色與群組因篩選或關閉資料夾而未顯示", "Otterly empty": "萬獺俱寂", "Here be dragons": "此處有龍", "Kiwibunga": "這是鴿子嗎？", "Pump-a-Rum": "啪，沒了。", "Croak it": "PLAY 蝦咪 GAME？", "${0} character hidden.": "隱藏了 ${0} 名角色。", "${0} characters hidden.": "隱藏了 ${0} 名角色。", "/ page": "／頁", "Context Length": "上下文長度", "Added On": "新增日期", "Class": "分類", "Next page": "下一頁", "Previous page": "上一頁", "Group: ${0}": "群組：${0}", "You deleted a character/chat and arrived back here for safety reasons! Pick another character!": "角色或對話已被刪除。系統為安全考量導回此頁，請重新選擇角色。", "Could not connect to API": "無法連線至 API", "Connected to API": "已成功連線至 API", "help_macros_charDepthPrompt": "角色的 @ 深度註記", "Bind presets to API connections": "將預設綁定至 API 連線", "Allow compressing requests by removing messages from the middle of the prompt.": "允許透過移除提示中間的訊息來壓縮請求。", "Unknown": "未知", "Min Keep": "最小保留", "enable_functions_desc_4": "當使用「無工具」提示後處理時不支援！", "Send inline videos": "傳送內嵌影片", "video_inlining_hint_1": "若模型支援，則在提示中傳送影片。使用", "video_inlining_hint_2": "操作於任何訊息或使用", "video_inlining_hint_3": "選單以上傳影片檔案到聊天中。", "video_inlining_hint_4": "影片大小必須小於 20 MB 且長度低於 1 分鐘", "This setting affects visibility only.": "此設定僅影響可見性。", "Allocates a portion of the response length for thinking (min: 1024 tokens, low: 10%, medium: 25%, high: 50%, max: 95%), but minimum 1024 tokens. Auto does not request thinking.": "為推理部分分配回應長度（最大回應長度至少需 1024 符元，低：10%、中：25%、高：50%、最高：95%）。選擇「自動」將不請求推理。", "Allocates a portion of the response length for thinking (Flash 2.5/Pro 2.5) (min: 0/128 tokens, low: 10%, medium: 25%, high: 50%, max: 24576/32768 tokens). Auto lets the model decide.": "為推理部分分配回應長度（Flash 2.5/Pro 2.5）（最少：0/128 符元，低：10%、中：25%、高：50%、最高：24576/32768 符元）。選擇「自動」將由模型決定。", "Save and connect": "儲存並連線", "Manage API keys": "管理 API 金鑰", "For privacy reasons, your API key will be hidden after you click 'Connect'.": "出於隱私考量，點選「連線」後，將隱藏您的 API 金鑰。", "Experimental feature. Use at your own risk.": "實驗性功能。請自行斟酌使用風險。", "suffix will be added automatically.": "後綴將自動新增。", "Google Vertex AI Configuration": "Google Vertex AI 設定", "Authentication Mode": "驗證模式：", "Express Mode (API Key)": "快速模式（API 金鑰）", "Full Version (Service Account)": "完整版（服務帳號）", "Google Vertex AI API Key": "Google Vertex AI API 金鑰", "(Express mode)": "（快速模式）", "Service Account Configuration": "服務帳號設定", "Region": "區域：", "View available regions and models": "檢視可用區域和模型", "Service Account JSON Content": "服務帳號 JSON 內容：", "For privacy reasons, your Service Account JSON content will be hidden after you click 'Validate JSON'.": "出於隱私考量，點選「驗證 JSON」將隱藏您的服務帳號 JSON 內容。", "Validate JSON": "驗證 JSON", "Pollinations Model": "Pollinations 模型", "Provided free of charge by Pollinations.AI": "由 Pollinations.AI 免費提供", "Avoid sending sensitive information. Provider's outputs may include ads.": "請避免傳送敏感資訊。供應商的輸出可能包含廣告。", "prompt_post_processing_merge_tools": "合併連續角色（含工具）", "prompt_post_processing_semi_tools": "半嚴格（角色交替；含工具）", "prompt_post_processing_strict_tools": "嚴格（使用者優先，角色交替；含工具）", "prompt_post_processing_single": "單一使用者訊息（無工具）", "Rounded": "圓角", "Notifications:": "系統通知：", "Top Left": "左上", "Top Center": "上方置中", "Top Right": "右上", "Bottom Left": "左下", "Bottom Center": "下方置中", "Bottom Right": "右下", "Click the message text in the chat log to edit it.": "點選對話訊息文字後即可編輯內容。", "Click to Edit": "點選以編輯聊天訊息", "Make animated backgrounds static in the selection menu. This will reduce UI and network lag if you have a large amount. This is only a change for the selection menu.": "將選單中的動畫背景靜態化，以減少背景過多所造成的介面與網路延遲。此設定僅影響選單。", "Animated background thumbnails": "動畫背景縮圖", "Find and delete backups, unused chats, files, images, etc.": "搜尋並刪除備份、未使用的聊天、檔案、圖片等。", "Clean-Up": "清理", "Always render style tags from greetings, even if the message is unloaded due to lazy loading.": "始終渲染問候訊息中的樣式標籤，即使該訊息因延遲載入而未顯示。", "Pin greeting message styles": "固定問候訊息樣式", "Visibility": "可見性", "Don't show": "不顯示", "Input length > 1": "輸入長度 > 1", "Always show": "始終顯示", "(DEPRECATED)": "（已淘汰）", "When multiple personas are connected to a character, a popup will appear to select which one to use": "當多個使用者角色綁定至同一角色時，將顯示選單以擇一。", "Whenever a persona is selected, it will be locked to the current chat and automatically selected when the chat is opened.": "您所選的使用者角色將綁定至目前聊天，並於開啟此聊天檔案時自動套用。", "Set / Unset as Welcome Page Assistant": "設定／取消為歡迎頁面助理", "Allow / Forbid the use of global styles for this character.": "允許／禁止此角色使用全域樣式。", "This character will be used as a welcome page assistant.": "此角色將用作歡迎頁面助理。", "prompt_manager_order": "順序", "prompt_manager_order_note": "來自其他來源（世界資訊、作者備註等）的提示注入預設順序為 100。", "Ordered from low/top to high/bottom, and at same order: Assistant, User, System.": "順序從低（上）至高（下）；同順序時，排列為助理 > 使用者 > 系統。", "Source:": "來源：", "Secondary captioning endpoint URL": "次要說明文字端點 URL", "ext_regex_bulk_edit": "批次編輯", "Export": "匯出", "No scripts found": "未找到腳本", "ext_regex_flags_help": "點選此處了解更多關於正規表達式（regex）標誌的資訊。", "ext_regex_other_options_desc": "預設情況下，正規表達式腳本會直接且不可逆地變更聊天檔案。\r\n啟用以下任一（或兩者）選項將防止更改聊天檔案，同時仍會變更指定項目。", "This character has embedded regex script(s).": "此角色包含嵌入式正規表達式腳本。", "Would you like to allow using them?": "您想允許使用它們嗎？", "If you want to do it later, select \"Regex\" from the extensions menu.": "您可稍後從擴充功能選單中啟用 「正規表達式（Regex）」。", "SillyTavern Settings": "SillyTavern 設定", "Documentation": "文件", "Import from JSONL": "從 JSONL 匯入", "Load": "載入", "Export as JSONL": "匯出為 JSONL", "Delete all items in this category": "刪除此分類中的所有項目", "View item content": "檢視項目內容", "Download item": "下載項目", "Delete this item": "刪除此項目", "Once deleted, the files will be gone forever!": "一旦刪除，檔案將永久消失！", "Make sure to back up your data in advance.": "請先備份您的資料。", "Scan": "掃描", "No results yet. Tap 'Scan' to start scanning.": "尚無結果。點選「掃描」以開始掃描。", "Do you want to export connection data with the preset?": "您想將連線資料與預設一起匯出嗎？", "This includes the selected source, models, and other preferences set in the API Connections panel.": "這包括在 API 連線面板中設定的來源、模型及其他偏好設定。", "Your stored API keys are never exported.": "您的 API 金鑰將不會被匯出。", "Export connection data": "匯出連線資料", "Do not export connection data": "不匯出連線資料", "Creator's Notes contain CSS style tags. Do you want to apply them just to Creator's Notes or to the entire application?": "此創作者備註內含 CSS 樣式。請選擇是否僅套用於備註區域，或應用至整體介面。", "CAUTION: Malformed styles may cause issues.": "注意：樣式錯誤可能導致問題。", "To change the preference later, use the": "若想稍後更改偏好設定，請使用", "button in the Creator's Notes block.": "創作者備註區塊中的按鈕。", "Class names will be automatically prefixed with 'custom-'.": "CSS 類別名稱將自動套用「custom-」前綴。", "Choose how to apply CSS style tags if they are defined in Creator's Notes of this character:": "請選擇如何套用此角色創作者備註中的 CSS 樣式：", "Just to Creator's Notes": "僅限創作者備註", "Apply to the entire app": "應用於整個應用程式", "API:": "API：", "Key:": "金鑰：", "Add Secret": "新增密鑰", "No secrets saved.": "尚未儲存任何密鑰。", "Copy ID": "複製 ID", "Select": "選擇", "Show recent chats": "顯示最近的聊天", "Hide recent chats": "隱藏最近的聊天", "Recent Chats": "最近的聊天", "Docs": "文件", "GitHub": "GitHub", "Discord": "Discord", "Temporary Chat": "臨時聊天", "No recent chats": "沒有最近的聊天", "(Message Box)": "（訊息框）", "[title]Create a new": "新增", "[title]Delete a ": "刪除 ", "[title]Rename a ": "重新命名 ", "completions note prefix": "完成提示前綴"}