{"clickslidertips": "Кликайте по цифрам под ползунками, чтобы менять их вручную.", "kobldpresets": "Пресеты для Kobold", "guikoboldaisettings": "Настройки из интерфейса KoboldAI", "openaipresets": "Пресеты для OpenAI", "response legth(tokens)": "Ответ (в токенах)", "context size(tokens)": "Контекст (в токенах)", "unlocked": "Неограниченный", "rep.pen": "Штра<PERSON> за повтор", "rep.pen range": "Окно для штрафов за повтор", "Temperature controls the randomness in token selection": "Температура контролирует процесс выбора токена:\n- при низкой температуре (<1.0) предпочтение отдаётся наиболее вероятным токенам, текст получается предсказуемым.\n- при высокой температуре (>1.0) повышаются шансы у токенов с низкой вероятностью, текст получается более креативным.\nУстановите значение 1.0, чтобы вероятности не менялись.", "Top_K_desc": "Top K задает жёсткое ограничение на количество рассматриваемых токенов.\nЕсли Top-K равен 20, это означает, что будут сохранены только 20 наиболее вероятных токенов (распределение их вероятностей в расчёт не берётся)\nУстановите значение 0, чтобы отключить.", "Top_P_desc": "Top P (он же nucleus sampling) складывает все верхние токены, пока их суммарные вероятности не достигнут целевого процента.\nТо есть, если 2 верхних токена составляют 25%, а Top-P равен 0.50, учитываются только эти 2 верхних токена.\nУстановите значение 1.0, чтобы отключить.", "Typical_P_desc": "Сэмплер Typical P определяет приоритет токенов на основе их отклонения от средней энтропии набора.\nОстаются токены, чья кумулятивная вероятность близка к заданному порогу (например, 0,5), выделяя те, которые имеют среднее информационное содержание.\nУстановите значение 1.0, чтобы отключить.", "Min_P_desc": "Min P устанавливает базовую минимальную вероятность. Она масштабируется в зависимости от вероятности верхнего токена.\nЕсли вероятность верхнего токена составляет 80%, а Min P - 0.1, будут рассматриваться только токены с вероятностью выше 8%.\nУстановите значение 0, чтобы отключить.", "Top_A_desc": "Top A устанавливает порог для отбора токенов на основе квадрата наибольшей вероятности токена.\nЕсли значение Top A равно 0.2, а вероятность верхнего токена равна 50%, то токены с вероятностью ниже 5% (0.2 * 0.5^2) будут исключены.\nУстановите значение 0, чтобы отключить.", "Tail_Free_Sampling_desc": "Tail-Free Sampling (TFS) ищет хвост маловероятных токнов в распределении,\n анализируя скорость изменения вероятностей токенов с помощью производных. Он сохраняет токены до порога (например, 0.3), основанного на нормированной второй производной.\nЧем ближе к 0, тем больше отброшенных токенов. Установите значение 1.0, чтобы отключить.", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "Epsilon cutoff устанавливает уровень вероятности, ниже которого токены исключаются из выборки.\nВ единицах 1e-4; разумное значение - 3.\nУстановите 0, чтобы отключить.", "Scale Temperature dynamically per token, based on the variation of probabilities": "Динамическое масштабирование температуры для каждого токена, основанное на изменении вероятностей.", "Minimum Temp": "Мин. температура", "Maximum Temp": "Макс. температура", "Exponent": "Экспонента", "Mirostat Mode": "Режим", "Mirostat_Tau_desc": "Controls variability of Mirostat outputs", "Mirostat_Eta_desc": "Controls learning rate of Mirostat", "Temperature Last": "Температура последней", "LLaMA / Mistral / Yi models only": "Только для моделей LLaMA / Mistral / Yi. Перед этим обязательно выберите подходящий токенизатор.\nПоследовательности, которых не должно быть на выходе.\nОдна на строку. Текст или [идентификаторы токенов].\nМногие токены имеют пробел впереди. Используйте счетчик токенов, если не уверены, что правильно их определяете.", "Example: some text [42, 69, 1337]": "Пример:\nкакой-то текст\n[42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "Classifier Free Guidance. Чуть позже опишем более подробно", "Scale": "Scale", "GBNF Grammar": "Грамматика GBNF", "Usage Stats": "Статистика исп.", "Click for stats!": "Нажмите для получения статистики!", "Backup": "Бэка<PERSON>", "Backup your personas to a file": "Создать бэкап (резервную копию) персоны в виде файла", "Restore": "Восстановить", "Restore your personas from a file": "Восстановление персон из файла", "Type in the desired custom grammar": "Введите нужную пользовательскую грамматику", "Encoder Rep. Pen.": "Штраф за повтор для кодировщика", "Smoothing Factor": "Коэффициент сглаживания", "No Repeat Ngram Size": "Размер no_repeat_ngram", "Min Length": "<PERSON>и<PERSON>. длина", "Alternative server URL (leave empty to use the default value).": "URL реверс-прокси (оставьте пустым для стандартного значения)", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "Удалите свой личный OAI API Key из панели API, и ТОЛЬКО ПОСЛЕ ЭТОГО вводите что-то сюда", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "Мы не сможем предоставить помощь с проблемами, с которыми вы столкнетесь при использовании неофициальных прокси для OpenAI", "Context Size (tokens)": "Размер контекста (в токенах)", "Max Response Length (tokens)": "Макс. длина ответа (в токенах)", "Temperature": "Температура", "Frequency Penalty": "Штраф за частоту", "Presence Penalty": "Штраф за присутствие", "Top A": "Top А", "Tail Free Sampling": "Tail Free Sampling", "Rep. Pen. Slope": "Рост штрафа за повтор к концу промпта", "Top K": "Top K", "Top P": "Top P", "Do Sample": "Включить сэмплинг", "Add BOS Token": "Добавлять BOS-токен", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "Добавлять BOS-токен в начале промпта. Если выключить, ответы могут стать более креативными.", "Ban EOS Token": "Запретить EOS-токен", "Ban the eos_token. This forces the model to never end the generation prematurely": "Запрет EOS-токена не позволит модели завершить генерацию самостоятельно (только при достижении лимита токенов)", "Skip Special Tokens": "Пропускать спец. токены", "Beam search": "<PERSON>am <PERSON>", "Length Penalty": "Штра<PERSON> за длину", "Early Stopping": "Прекращать сразу", "Penalty Alpha": "Penalty Alpha", "Seed": "Зерно", "Epsilon Cutoff": "Epsilon Cutoff", "Eta Cutoff": "<PERSON><PERSON>", "Negative Prompt": "Отрицательный промпт", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (режим=1 предназначен только для llama.cpp)", "Add text here that would make the AI generate things you don't want in your outputs.": "Добавьте сюда текст, который заставит ИИ генерировать то, что вы не хотите видеть в его текстах", "Phrase Repetition Penalty": "Штраф за повтор фразы", "Preamble": "Преамбула", "Use style tags to modify the writing style of the output.": "Используйте теги стиля, чтобы изменить стиль написания выходного текста.", "Banned Tokens": "Запрещённые токены", "Sequences you don't want to appear in the output. One per line.": "Строки, которых не должно быть в выходном тексте. По одной на строчку.", "AI Module": "Модуль ИИ", "Changes the style of the generated text.": "Изменяет стиль создаваемого текста.", "Used if CFG Scale is unset globally, per chat or character": "Используется, если CFG Scale не установлен глобально, для каждого чата или персонажа.", "Streaming": "Стриминг текста", "Dynamic Temperature": "Динамическая температура", "Restore current preset": "Восстановить текущий пресет", "Neutralize Samplers": "Нейтрализовать сэмплеры", "Text Completion presets": "Пресеты для Text Completion", "Documentation on sampling parameters": "Документация по параметрам сэмплеров", "Set all samplers to their neutral/disabled state.": "Установить все сэмплеры в нейтральное/отключенное состояние.", "Only enable this if your model supports context sizes greater than 8192 tokens": "Включайте эту опцию, только если ваша модель поддерживает размер контекста более 8192 токенов.\nУвеличивайте только если вы понимаете, что делаете.", "Wrap in Quotes": "Заключать в кавычки", "Wrap entire user message in quotes before sending.": "Перед отправкой заключать всё сообщение пользователя в кавычки.", "Leave off if you use quotes manually for speech.": "Оставьте выключенным, если вручную выставляете кавычки для прямой речи.", "Impersonation prompt": "Промпт для перевоплощения", "Prompt that is used for Impersonation function": "Промпт, применяемый при генерации действий от лица пользователя", "Logit Bias": "Смещение логитов", "Helps to ban or reenforce the usage of certain words": "Запрещает или поощряет использование определенных слов", "View / Edit bias preset": "Просмотр / Редактирование пресета смещения", "Add bias entry": "Добавить правило смещения", "Connect": "Подключиться", "Test Message": "Тестовое сообщение", "KoboldAI": "KoboldAI", "API url": "URL-адрес API", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (Режим обёртки API OpenAI)", "Register a Horde account for faster queue times": "Заведите учетную запись Horde для ускорения генерации", "Adjust context size to worker capabilities": "Подстраивать размер контекста под возможности рабочих машин", "Adjust response length to worker capabilities": "Подстраивать длину ответа под возможности рабочих машин", "API key": "API-к<PERSON><PERSON><PERSON>", "Tabby API key": "Ключ от Tabby API", "Get it here:": "Получите здесь:", "Register": "Зарегистрироваться", "TogetherAI Model": "Модель TogetherAI", "Example: http://127.0.0.1:5001": "Пример: http://127.0.0.1:5001", "ggerganov/llama.cpp": "ggerganov/llama.cpp (сервер вывода)", "Example: http://127.0.0.1:8080": "Пример: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "Пример: http://127.0.0.1:11434", "Ollama Model": "М<PERSON>д<PERSON><PERSON><PERSON> Ollama", "Download": "Скачать", "TogetherAI API Key": "TogetherAI API-ключ", "-- Connect to the API --": "-- Подключитесь к API --", "View my Kudos": "Посмотреть мой рейтинг(Kudos)", "Enter": "Введите", "to use anonymous mode.": "чтобы использовать анонимный режим.", "Models": "Модели", "Not connected...": "Нет подключения...", "Novel API key": "API-ключ для NovelAI", "Follow": "Следуйте", "these directions": "данным инструкциям", "Enter it in the box below": "Введите его в окошко ниже", "Novel AI Model": "Модель NovelAI", "Make sure you run it with": "Обязательно запускайте его с флагом", "flag": "", "API key (optional)": "Ключ API (необязательно)", "Server url": "URL-адрес сервера", "Custom model (optional)": "Пользовательская модель (необязательно)", "Bypass API status check": "Обход проверки статуса API", "Example: http://127.0.0.1:5000": "Пример:  http://127.0.0.1:5000", "Bypass status check": "Обход проверки статуса", "Mancer API key": "Ключ от Mancer API", "to get your OpenAI API key.": "для получения ключа от API OpenAI", "Window AI Model": "Модель Window AI", "OpenAI Model": "Модель OpenAI", "Get your key from": "Получите ключ в", "Anthropic's developer console": "консоли разработчик<PERSON> Anthropic", "Claude Model": "Мод<PERSON><PERSON>ь Claude", "AI21 API Key": "Ключ от API AI21", "AI21 Model": "Модель AI21", "View API Usage Metrics": "Посмотреть статистику использования API", "Show External models (provided by API)": "Показать \"сторонние\" модели (предоставленные API)", "Allow fallback routes": "Разрешить резервные маршруты", "Allow fallback routes Description": "Автоматически выбирает альтернативную модель, если выбранная модель не может обслужить ваш запрос.", "OpenRouter API Key": "Ключ от OpenRouter API", "OpenRouter Model": "Модель OpenRouter", "View Remaining Credits": "Посмотреть оставшиеся кредиты", "Click Authorize below or get the key from": "Нажмите «Авторизоваться» ниже или получите ключ от", "Auto-connect to Last Server": "Автоматическое подключение к последнему серверу", "View hidden API keys": "Посмотреть скрытые API-ключи", "Advanced Formatting": "Расширенное форматирование", "Context Template": "<PERSON>а<PERSON><PERSON><PERSON>н контекста", "Replace Macro in Stop Strings": "Заменять макросы в стоп-строках", "Story String": "Общий шаблон", "Example Separator": "Разделитель примеров сообщений", "Chat Start": "Начало чата", "Activation Regex": "Рег. выражение для активации", "Instruct Mode": "Режим Instruct", "Wrap Sequences with Newline": "Каждая строка из шаблона на новой строке", "Include Names": "Добавлять имена", "Force for Groups and Personas": "Также для групп и персон", "System Prompt": "Системный промпт", "Instruct Sequences": "Строки для Instruct-режима", "Stop Sequence": "Стоп-строка", "Context Formatting": "Форматирование контекста", "(Saved to Context Template)": "(Сохраняется в шаблоне контекста)", "Tokenizer": "Токенайзер", "Token Padding": "Кол-во добавочных токенов", "Save preset as": "Сохранить пресет как...", "Always add character's name to prompt": "Всегда добавлять имя персонажа в промпт", "Use as Stop Strings": "Использовать в качестве стоп-строк", "Bind to Context": "Привязка к контексту", "Generate only one line per request": "Генерировать одну строку на запрос", "Misc. Settings": "Доп. настройки", "Auto-Continue": "Авто-продолжение", "Collapse Consecutive Newlines": "Сворачивать неск. новых строк в одну", "Allow for Chat Completion APIs": "Разрешить для Chat Completion API", "Target length (tokens)": "Целевая длина (в токенах)", "World Info": "Информация о мире", "Scan Depth": "Глу<PERSON>ина сканирования", "Case-Sensitive": "С учетом регистра", "Match Whole Words": "Только полное совпадение", "Use global setting": "Глоб. настройка", "Yes": "Да", "No": "Нет", "Context %": "Процент контекста", "Budget Cap": "<PERSON>имит бюджета", "(0 = disabled)": "(0 = отключено)", "None": "Отсутствует", "User Settings": "Настройки пользователя", "UI Language": "Язык интерфейса", "Avatar Style:": "Ава<PERSON><PERSON><PERSON><PERSON>и", "Circle": "Круглые", "Rectangle": "Прямоугольные", "Square": "Квадратные", "Default": "По умолчанию", "Bubbles": "Пузыри", "No Blur Effect": "Отключить размытие", "No Text Shadows": "Отключить тень текста", "Waifu Mode": "Режим вайфу", "Message Timer": "Таймер сообщений", "Model Icon": "Значки моделей", "Advanced Character Search": "Расширенный поиск по персонажам", "Allow {{char}}: in bot messages": "Показывать {{char}}: в ответах", "Allow {{user}}: in bot messages": "Показать {{user}}: в ответах", "Show tags in responses": "Показывать <теги> в ответах", "Lorebook Import Dialog": "Показывать окно импорта лорбука", "MUI Preset": "Пресет MUI:", "If set in the advanced character definitions, this field will be displayed in the characters list.": "Если это поле задано в расширенных параметрах персонажа, оно будет отображаться в списке персонажей.", "Relaxed API URLS": "Смягчённые адреса API", "Custom CSS": "Пользовательский CSS", "Mancer Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "API Type": "Тип <PERSON>", "Aphrodite API key": "Ключ от API Aphrodite", "Relax message trim in Groups": "Мягкая обрезка сообщений в группах", "Characters Hotswap": "HotSwap (смена персонажей на лету)", "Request token probabilities": "Запрашивать вероятность токена", "Movable UI Panels": "Подвижные панели UI", "Main Text": "Основной текст", "Italics Text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Quote Text": "Текст в кавычках", "Shadow Color": "Цвет теней", "Font Scale": "Размер текста", "Blur Strength": "Сила размытия", "Text Shadow Width": "Размер теней текста", "Swipes": "Свайпов сделано", "Miscellaneous": "Разное", "Background Sound Only": "Только фоновый звук", "Auto-load Last Chat": "Автозагрузка последнего чата", "Auto-save Message Edits": "Автоматически сохранять отредактированные сообщения", "Auto-fix Markdown": "Автоисправление разметки", "Auto-scroll Chat": "Автоматическая прокрутка чата", "Send on Enter": "Отправка на Enter", "Debug Menu": "Меню отладки", "Restore User Input": "Восстанавливать введённый текст", "Character Handling": "Обработка персонажа", "Example Messages Behavior": "Примеры сообщений:", "Gradual push-out": "Постепенное выталкивание", "Chat/Message Handling": "Обработка чата и сообщений", "Always include examples": "Всегда применять примеры", "Never include examples": "Никогда не применять примеры", "Forbid External Media": "Запрет внешних медиа", "System Backgrounds": "Системные фоны", "Name": "Имя", "Auto-connect": "Подключаться автоматически", "First message": "Первое сообщение", "Group Controls": "Управление группой", "Group reply strategy": "Персонажи отвечают...", "Natural order": "По алфавиту", "List order": "По списку", "Allow self responses": "Разрешить ответ себе", "Auto Mode": "Авто-режим", "Add Members": "Добавить участников", "Current Members": "Текущие участники", "Delete": "Удалить", "Cancel": "Отменить", "Advanced Definitions": "Расширенное описание", "Personality summary": "Резюме по личности", "Scenario": "Сценарий", "Talkativeness": "Разговорчивость", "group chats!": "групповых чатах!", "Shy": "Застенчивый", "Normal": "Обычный", "Chatty": "Разговорчивый", "Examples of dialogue": "Примеры диалога", "Save": "Сохранить", "Chat History": "История чатов", "Content": "Содержание", "Selective": "Выборочно", "Disable": "От<PERSON><PERSON>.", "Back to parent chat": "Вернуться в основной чат", "Convert to group": "Сделать групповым", "Start new chat": "Начать новый чат", "Delete messages": "Удалить сообщения", "Impersonate": "Перевоплощение", "Regenerate": "Повторная генерация", "Message Sound": "Звук сообщения", "Author's Note": "Авторские заметки", "Replace empty message": "Заменять пустые сообщения", "Send this text instead of nothing when the text box is empty.": "Этот текст будет отправлен в случае отсутствия текста на отправку.", "Unrestricted maximum value for the context slider": "Убрать потолок для ползунка контекста. Включайте только если точно понимаете, что делаете", "Chat Completion Source": "Источник для Chat Completion", "Avoid sending sensitive information to the Horde.": "Избегайте отправки личной информации Horde.", "Review the Privacy statement": "Ознакомьтесь с заявлением о конфиденциальности", "Trusted workers only": "Только доверенные рабочие машины", "For privacy reasons, your API key will be hidden after you reload the page.": "Из соображений безопасности ваш API-ключ будет скрыт после перезагрузки страницы.", "-- Horde models not loaded --": "--<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Horde не загружена--", "Example: http://127.0.0.1:5000/api ": "Пример: http://127.0.0.1:5000/api", "No connection...": "Нет соединения...", "Get your NovelAI API Key": "Получите свой API-ключ для NovelAI", "AI Horde": "AI Horde", "NovelAI": "NovelAI", "OpenAI API key": "Ключ для API OpenAI", "Trim spaces": "Обрезать пробелы в начале и конце", "Trim Incomplete Sentences": "Удалять неоконченные предложения", "Include Newline": "Добавлять новую строку", "Non-markdown strings": "Строки без разметки", "Replace Macro in Sequences": "Заменять макросы в строках", "Presets": "Пресеты", "Start Reply With": "Начинать ответ с", "Show reply prefix in chat": "Показывать префикс ответов в чате", "Worlds/Lorebooks": "Миры и лорбуки", "Sorted Evenly": "Равномерная сортировка", "Active World(s) for all chats": "Активные миры для всех чатов", "-- World Info not found --": "-- Информация о мире не найдена --", "--- Pick to Edit ---": "--- Выберите для редактирования ---", "or": "или", "New": "Новый", "Priority": "Приоритет", "Custom": "Пользовательский", "Title A-Z": "Название от A до Z", "Title Z-A": "Название от Z до A", "Tokens ↗": "Токены ↗", "Tokens ↘": "Токены ↘", "Depth ↗": "Глубина ↗", "Depth ↘": "Глубина ↘", "Order ↗": "Порядок ↗", "Order ↘": "Порядок ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "% срабатываний ↗", "Trigger% ↘": "% срабатываний ↘", "Depth:": "Глубина:", "Character Lore First": "Сначала лор персонажа", "Global Lore First": "Сначала глобальный лор", "Recursive Scan": "Рекурсивное сканирование", "Case Sensitive": "Учитывать регистр", "Alert On Overflow": "Оповещение о переполнении", "Use Probability": "Использовать вероятность", "Exclude from recursion": "Исключить из рекурсии", "Entry Title/Memo": "Название или заметка о записи", "Position:": "Положение:", "T_Position": "↑Перс: Перед описанием персонажа\n↓Перс: После описания персонажа\n↑ПС: Перед примерами сообщений\n↓ПС: После примеров сообщений\n↑АЗ: Перед авторскими заметками\n↓АЗ: После авторских заметок\nНа глуб. ⚙️: на глубине (система)\nНа глуб. 👤: на глубине (пользователь)\nНа глуб. 🤖: на глубине (ассистент)", "Before Char Defs": "↑Перс.", "After Char Defs": "↓Перс.", "Before AN": "↑АЗ", "After AN": "↓АЗ", "Before EM": "↑ПС", "After EM": "↓ПС", "at Depth System": "На глуб. ⚙️", "at Depth User": "На глуб. 👤", "at Depth AI": "На глуб. 🤖", "Order": "Приоритет:", "Update a theme file": "Обновить файл темы", "Save as a new theme": "Сохранить как новую тему", "Minimum number of blacklisted words detected to trigger an auto-swipe": "Минимальное количество обнаруженных запрещённых слов, при котором срабатывает авто-свайп.", "User Message Blur Tint": "Ваши сообщения", "AI Message Blur Tint": "Сообщения ИИ", "Chat Backgrounds": "Фоны чата", "Chat Background": "Фон чата", "UI Background": "Фон U<PERSON>", "Mad Lab Mode": "Режим безумца", "Show Message Token Count": "Счетчик токенов сообщения", "Compact Input Area (Mobile)": "Компактная зона ввода", "Zen Sliders": "Дзен слайдеры", "UI Border": "Границы UI", "Chat Style:": "Стиль чата", "Chat Timestamps": "Метки времени в чате", "Tags as Folders": "Теги как папки", "Streaming FPS": "FPS для стриминга", "Gestures": "Жесты", "Message IDs": "ID сообщений", "Prefer Character Card Prompt": "Приоритет промпту из карточки персонажа", "Prefer Character Card Jailbreak": "Приоритет джейлбрейку из карточки персонажа", "Press Send to continue": "Кнопка отправки продолжает сообщение", "Quick 'Continue' button": "Быстрое продолжение", "Log prompts to console": "Выводить промпты в консоль", "Never resize avatars": "Не менять размер аватарок", "Show avatar filenames": "Показывать названия файлов аватарок", "Import Card Tags": "Импортировать теги карточки", "Confirm message deletion": "Подтверждение удаления сообщений", "Spoiler Free Mode": "Режим без спойлеров", "Auto-swipe": "Автоматические свайпы", "Minimum generated message length": "Минимальная длина сгенерированных сообщений", "Blacklisted words": "Запрещенные слова", "Blacklisted word count to swipe": "Количество запрещенных слов для свайпа", "Reload Chat": "Перезагрузить чат", "Search Settings": "Поиск по настройкам", "Disabled": "Отключено", "Automatic (PC)": "Автоматическое (ПК)", "Enabled": "Включено", "Simple": "Простой", "Advanced": "Рас<PERSON>иренный", "Disables animations and transitions": "Отключение анимаций и переходов.", "removes blur from window backgrounds": "Убрать размытие с фона окон, чтобы ускорить рендеринг.", "Remove text shadow effect": "Удаление эффекта тени от текста.", "Reduce chat height, and put a static sprite behind the chat window": "Уменьшить высоту чата и поместить статичный спрайт за окном чата.", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "Всегда показывать полный список действий с сообщением, а не прятать их за '...'.", "Alternative UI for numeric sampling parameters with fewer steps": "Уменьшить кол-во шагов для параметров, регулируемых слайдерами.", "Entirely unrestrict all numeric sampling parameters": "Снять ограничения со всех числовых сэмплеров.", "Time the AI's message generation, and show the duration in the chat log": "Время генерации сообщений ИИ и его показ в журнале чата.", "Show a timestamp for each message in the chat log": "Показывать временную метку для каждого сообщения в журнале чата.", "Show an icon for the API that generated the message": "Показать значок API, сгенерировавшего сообщение.", "Show sequential message numbers in the chat log": "Показывать порядковые номера сообщений в журнале чата.", "Show the number of tokens in each message in the chat log": "Показать количество токенов в каждом сообщении в журнале чата.", "Single-row message input area. Mobile only, no effect on PC": "Однорядная область ввода сообщений. Только для мобильных устройств, на ПК не работает.", "In the Character Management panel, show quick selection buttons for favorited characters": "На панели управления персонажами будут отображены кнопки быстрого выбора для избранных персонажей.", "Show tagged character folders in the character list": "Отобразить теговые папки с персонажами в списке персонажей.", "Play a sound when a message generation finishes": "Воспроизведение звука при завершении генерации сообщения.", "Only play a sound when ST's browser tab is unfocused": "Воспроизводить звук только тогда, когда вкладка браузера ST не выбрана.", "Reduce the formatting requirements on API URLs": "Снижение требований к форматированию URL-адресов API.", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "Спрашивать разрешение на импорт лорбука для всех персонажей со встроенным лорбуком. При выключенной опции вместо этого будет показываться короткое сообщение", "Restore unsaved user input on page refresh": "Восстановление несохраненного пользовательского запроса при обновлении страницы.", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "Позволяет перемещать некоторые элементы интерфейса путем их перетаскивания. Только для ПК, на телефонах не работает.", "MovingUI preset. Predefined/saved draggable positions": "Пресет для MovingUI. Предопределенные/сохраненные позиции для перетаскивания.", "Save movingUI changes to a new file": "Сохранение изменений MovingUI в новый файл.", "Apply a custom CSS style to all of the ST GUI": "Применять пользовательский CSS ко всему интерфейсу Таверны.", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "Искать персонажей по всем полям данных, а не только по имени.", "If checked and the character card contains a prompt override (System Prompt), use that instead": "При включении этой опции, системный промпт будет заменяться кастомным промптом из карточки (при его наличии).", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "При включении этой опции, пользовательский джейлбрейк будет заменяться кастомным джейлбрейком из карточки (при его наличии).", "Show actual file names on the disk, in the characters list display only": "Отображение названий файлов персонажей на диске, только в списке персонажей.", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "Запрашивать разрешения на импорт встроенных тегов карт при импорте персонажей. В противном случае встроенные теги игнорируются.", "Hide character definitions from the editor panel behind a spoiler button": "Спрятать описания персонажей из панели редактора за кнопку спойлера.", "Show a button in the input area to ask the AI to continue (extend) its last message": "Показывать на форме ответа кнопку, по нажатии на которую ИИ продолжит своё предыдущее сообщение.", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "Показывать кнопки со стрелками на последнем сообщении в чате, чтобы генерировать альтернативные ответы ИИ. Как для ПК, так и для мобильных устройств.", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "Позволяет использовать жесты смахивания на последнем сообщении в чате, чтобы вызвать альтернативную генерацию. Только для мобильных устройств, на ПК не работает.", "Save edits to messages without confirmation as you type": "Сохранять правки в сообщениях без подтверждения по мере ввода текста.", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "Не кодировать символы < и > в тексте сообщения, что позволяет использовать подмножество HTML-разметки, а также Markdown.", "Allow AI messages in groups to contain lines spoken by other group members": "Разрешить ИИ в группах генерировать строчки за других участников группы в своих сообщениях.", "Requests logprobs from the API for the Token Probabilities feature": "Запросить логпробы из API для функции Token Probabilities.", "Automatically reject and re-generate AI message based on configurable criteria": "Автоматическое отклонение и повторная генерация сообщений AI на основе настраиваемых критериев.", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "Включить авто-свайп. Настройки в этом разделе действуют только при включенном авто-свайпе.", "If the generated message is shorter than these many characters, trigger an auto-swipe": "Если сгенерированное сообщение короче этого значения, срабатывает авто-свайп.", "Reload and redraw the currently open chat": "Перезагрузить и перерисовать открытый в данный момент чат.", "Auto-Expand Message Actions": "Развернуть действия", "Persona Management": "Управление персоной", "Persona Description": "Описание персоны", "Your Persona": "Ваша персона", "Show notifications on switching personas": "Показывать уведомления при смене персоны", "In Story String / Prompt Manager": "В общем шаблоне / Менеджере промптов", "Top of Author's Note": "Сверху от авторских заметок", "Bottom of Author's Note": "Снизу от авторских заметок", "How do I use this?": "Как пользоваться?", "More...": "Ещё...", "Link to World Info": "Ссылка на информацию о мире", "Import Card Lore": "Импортировать лор карточки", "Scenario Override": "Перезапись сценария", "Rename": "Переименовать", "Character Description": "Описание персонажа", "Creator's Notes": "Примечание от создателя", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "Сначала новые", "Oldest": "Сначала старые", "Favorites": "Избранные", "Recent": "Последние", "Most chats": "Больше всего чатов", "Least chats": "Меньше всего чатов", "Prompt Overrides": "Индивидуальные промпты", "Insert {{original}} into either box to include the respective default prompt from system settings.": "Введите {{original}} в любое поле, чтобы вставить соответствующий промпт из системных настроек", "Main Prompt": "Основной промпт", "Jailbreak": "Джейлбрейк", "Everything here is optional": "Все поля необязательные", "Created by": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Character Version": "Версия персонажа", "Tags to Embed": "Встраиваемые теги", "Important to set the character's writing style.": "Серьёзно влияет на стиль письма персонажа.", "Samplers Order": "Порядок сэмплеров", "Samplers will be applied in a top-down order. Use with caution.": "Сэмплеры будут применяться в порядке от верхнего к нижнему. Используйте с осторожностью.", "Repetition Penalty": "Штраф за повторы", "Rep. Pen. Range.": "Диапазон поиска повторов", "Rep. Pen. Freq.": "Частота штрафа за повторы", "Rep. Pen. Presence": "Наличие штрафа за повторы", "separate with commas w/o space between": "разделять запятыми без пробела", "Document": "Документ", "Continue": "Продолжить", "CFG Scale": "CFG Scale", "AI reply prefix": "Префикс для ответа ИИ", "Custom Stopping Strings": "Стоп-строки", "JSON serialized array of strings": "Список строк в формате JSON", "words you dont want generated separated by comma ','": "слова, которые вы не хотите генерировать, через запятую ','", "Enter your name": "Введите свое имя", "Name this character": "Назовите этого персонажа", "Search / Create Tags": "Искать / Создать тэги", "Describe your character's physical and mental traits here.": "Опишите характер персонажа и его внешность", "This will be the first message from the character that starts every chat.": "Это будет первое сообщение от персонажа при начале нового чата", "Chat Name (Optional)": "Название чата (необязательно)", "Search...": "Поиск...", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "Все содержимое этого поля будет заменять стандартный промпт", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "Все содержимое этого поля будет заменять стандартный джейлбрейк", "(Botmaker's name / Contact Info)": "(Имя автора, контакты)", "(If you want to track character versions)": "Если вы хотите отслеживать версии персонажа", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(Описание персонажа, советы по использованию, список моделей, на которых он тестировался. Информация будет отображаться в списке персонажей)", "(Write a comma-separated list of tags)": "(Список тегов через запятую)", "(A brief description of the personality)": "(Краткое описание личности)", "(Circumstances and context of the interaction)": "(Обстоятельства и контекст этого диалога)", "(Examples of chat dialog. Begin each example with START on a new line.)": "(Примеры диалога. Начинайте каждый пример со START и на новой строке.)", "Type here...": "Пишите здесь...", "Comma separated (required)": "Через запятую (обязательное поле)", "What this keyword should mean to the AI, sent verbatim": "Объясните ИИ, что он должен знать об этом ключевом слове", "Filter to Character(s)": "Фильтр по персонажу(ам)", "Character Exclusion": "Исключить персонажей", "Inclusion Group": "Группа записей", "Only one entry with the same label will be activated": "Будет активна только одна запись с одинаковой меткой", "-- Characters not found --": "-- Перс<PERSON>нажей не найдено --", "(This will be the first message from the character that starts every chat)": "(Это будет первое сообщение от персонажа, когда вы начинаете новый чат)", "AI Response Configuration": "Настройка ответа ИИ", "AI Configuration panel will stay open": "Панель настройки ИИ останется открытой", "Update current preset": "Обновить текущий пресет", "Import preset": "Импортировать пресет", "Export preset": "Экспортировать пресет", "Delete the preset": "Удалить пресет", "Auto-select this preset for Instruct Mode": "Автоматический выбор этого пресета для режима Instruct.", "Auto-select this preset on API connection": "Автоматический выбор этого пресета при подключении к API.", "Wrap user messages in quotes before sending": "Перед отправкой заключать ответ пользователя в кавычки", "Restore default prompt": "Восстановить станндартный промпт", "New preset": "Новый пресет", "Delete preset": "Удалить пресет", "API Connections": "Соединение с API", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "Может помочь при плохих ответах, делая запросы только к доверенным рабочим машинам. Может замедлить время ответа.", "Clear your API key": "Стереть ключ от API", "Refresh models": "Обновить модели", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "Получите свой OpenRouter API токен используя OAuth. У вас будет открыта вкладка openrouter.ai", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "Проверка работоспособности вашего соединения с API путём отправки короткого тестового сообщения. Внимание: оно будет отправлено от вашего имени.", "Edit": "Редактировать", "Locked = World Editor will stay open": "Закреплено = Редактор мира останется открытым", "Entries can activate other entries by mentioning their keywords": "Записи могут активировать другие записи, если в них содержатся ключевые слова", "Lookup for the entry keys in the context will respect the case": "Большая буква имеет значение при активации ключевого слова", "If the entry key consists of only one word, it would not be matched as part of other words": "Если ключевое слово состоит только из одного слова, оно не будет активироваться как часть других слов", "Open all Entries": "Открыть все записи", "Close all Entries": "Закрыть все записи", "Create": "Создать", "Import World Info": "Импортировать мир", "Export World Info": "Экспортировать мир", "Delete World Info": "Удалить мир", "Duplicate World Info": "Дуб<PERSON>ировать мир", "Rename World Info": "Переименовать мир", "Refresh": "Обновить", "Primary Keywords": "Основные ключевые слова", "Logic": "Логика", "AND ANY": "И ЛЮБОЙ", "AND ALL": "И ВСЕ", "NOT ALL": "НЕ ВСЕ", "NOT ANY": "НЕ ЛЮБОЙ", "Optional Filter": "Дополнительный фильтр", "New Entry": "Новая запись", "Fill empty Memo/Titles with Keywords": "Заполнить пустые названия ключевыми словами", "AI Response Formatting": "Формат ответа ИИ", "Change Background Image": "Изменить фон", "Extensions": "Расширения", "Click to set a new User Name": "Нажмите, чтобы задать новое имя пользователя.", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "Нажмите, чтобы закрепить выбранную персону для текущего чата. Нажмите еще раз, чтобы открепить.", "Click to set user name for all messages": "Нажмите, чтобы задать имя пользователя для всех сообщений.", "Create a dummy persona": "Создать пустую персону", "Character Management": "Управление персонажами", "Locked = Character Management panel will stay open": "Закреплено = Панель управление персонажами останется открытой", "Select/Create Characters": "Выбрать/Создать персонажа", "Token counts may be inaccurate and provided just for reference.": "Счетчик токенов может быть неточным, используйте как ориентир", "Click to select a new avatar for this character": "Нажмите чтобы выбрать новый аватар для этого персонажа", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "Пример:\n [{{user}} is a 28-year-old Romanian cat girl.]", "Toggle grid view": "Сменить вид сетки", "Add to Favorites": "Добавить в Избранное", "Advanced Definition": "Расширенное описание", "Character Lore": "Лор персонажа", "Export and Download": "Экспортировать и скачать", "Duplicate Character": "Клонировать персонажа", "Create Character": "Создать персонажа", "Delete Character": "Удалить персонажа", "View all tags": "Показать все тэги", "Click to set additional greeting messages": "Нажмите чтобы создать дополнительное вступительное сообщение", "Show / Hide Description and First Message": "Показать / убрать описание и первое сообщение", "Click to select a new avatar for this group": "Нажмите чтобы выбрать новый аватар для этой группы", "Set a group chat scenario": "Задать сценарий для группового чата", "Restore collage avatar": "Восстановить аватар-коллаж", "Create New Character": "Создать нового персонажа", "Import Character from File": "Импортировать персонажа из файла", "Import content from external URL": "Импортировать содержимое из внешнего URL", "Create New Chat Group": "Создать новый групповой чат", "Characters sorting order": "Порядок сортировки персонажей", "Remove": "Убрать", "Select a World Info file for": "Выбрать файл с миром для", "Primary Lorebook": "Основной лорбук", "A selected World Info will be bound to this character as its own Lorebook.": "Данный мир будет привязан к персонажу как его собственный лорбук.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "При генерации ответа, данный лорбук будет работать вместе с глобально выбранным лорбуком.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "При экспорте персонажа вместе с ним также выгрузится выбранный лорбук в виде JSON.", "Additional Lorebooks": "Вспомогательные лорбуки", "Associate one or more auxillary Lorebooks with this character.": "Привязать к этому персонажу один или больше вспомогательных лорбуков.", "NOTE: These choices are optional and won't be preserved on character export!": "ВНИМАНИЕ: вспомогательные лорбуки не будут выгружены при экспорте персонажа!", "Rename chat file": "Переименовать чат", "Export JSONL chat file": "Экспортировать чат в формате JSONL", "Download chat as plain text document": "Скачать чат в формате .txt", "Delete chat file": "Удалить файл этого чата", "Delete tag": "Удалить тэг", "Translate message": "Перевести сообщение", "Generate Image": "Создать изображение", "Narrate": "Повествовать", "Prompt": "Промпт", "Copy": "Скопировать", "Confirm": "Подтвердить", "Copy this message": "Продублировать сообщение", "Delete this message": "Удалить сообщение", "Move message up": "Переместить сообщение вверх", "Move message down": "Переместить сообщение вниз", "Enlarge": "Увеличить", "Temporarily disable automatic replies from this character": "Временно отключить автоматические сообщения от этого персонажа", "Enable automatic replies from this character": "Включить автоматическую отправку сообщения этого персонажа", "Trigger a message from this character": "Запросить сообщение от этого персонажа", "Move up": "Переместить вверх", "Move down": "Переместить вниз", "View character card": "Посмотреть карточку персонажа", "Remove from group": "Убрать из группы", "Add to group": "Добавить в группу", "Add": "Добавить", "Abort request": "Прекратить генерацию", "Send a message": "Отправить сообщение", "Ask AI to write your message for you": "Попросить ИИ написать сообщение за вас", "Continue the last message": "Продолжить текущее сообщение", "Bind user name to that avatar": "Закрепить имя за этим аватаром", "Select this as default persona for the new chats.": "Выбирать эту персону по умолчанию для всех новых чатов.", "Change persona image": "Сменить аватар персоны", "Delete persona": "Удалить персону", "Reduced Motion": "Урезать анимации", "Auto-select": "Авто-выбор", "Automatically select a background based on the chat context": "Автоматический выбор фона в зависимости от контекста чата", "Filter": "Фильтр", "Exclude message from prompts": "Исключить сообщение из промпта", "Include message in prompts": "Включить сообщение в промпт", "Create checkpoint": "Создать чекпоинт", "Create Branch": "Создать ветку", "Embed file or image": "Вставить файл или изображение", "UI Theme": "Тема UI", "This message is invisible for the AI": "Это сообщение невидимо для ИИ", "Sampler Priority": "Приоритет сэмплеров", "Ooba only. Determines the order of samplers.": "Только для oobabooga. Определяет порядок сэмплеров.", "Load default order": "Загрузить стандартный порядок", "Max Tokens Second": "Макс. кол-во токенов в секунду", "CFG": "CFG", "Extras API key (optional)": "Ключ от Extras API (необязательно)", "Notify on extension updates": "Уведомлять об обновлениях расширений", "Toggle character grid view": "Изменить вид грида персонажей", "Bulk delete characters": "Массовое удаление персонажей", "Favorite characters to add them to HotSwaps": "Чтобы включить персонажа в HotSwaps, добавьте его в Избранное", "Underlined Text": "Подчёркнутый", "Token Probabilities": "Вероятности токенов", "Close chat": "Закрыть чат", "Manage chat files": "Все чаты", "Import Extension From Git Repo": "Импортировать расширение из Git-репозитория.", "Install extension": "Установить расширение", "Manage extensions": "Управление расширениями", "Tokens persona description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Most tokens": "Больше всего токенов", "Least tokens": "Меньше всего токенов", "Random": "Случайно", "Skip Example Dialogues Formatting": "Пропустить форматирование примеров диалогов", "Import a theme file": "Импортировать файл темы", "Export a theme file": "Экспортировать файл темы", "Unlocked Context Size": "Неограниченный размер контекста", "Display the response bit by bit as it is generated.": "Выводить текст последовательно по мере его генерации.", "When this is off, responses will be displayed all at once when they are complete.": "Если параметр выключен, ответы будут отображаться сразу целиком, и только после полного завершения генерации.", "Quick Prompts Edit": "Быстрое редактирование промптов", "Enable OpenAI completion streaming": "Включить стриминг OpenAI", "Main": "Основной", "Utility Prompts": "Служебные промпты", "Continue prefill": "Префилл для продолжения", "Continue sends the last message as assistant role instead of system message with instruction.": "Продолжение отправляет последнее сообщение в роли ассистента, вместо системного сообщения с инструкцией.", "Squash system messages": "Склеивать сообщения системы", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "Объединяет последовательные системные сообщения в одно (за исключением примеров диалогов). У некоторых моделей может улучшить логичность ответов.", "Send inline images": "Отправлять inline-картинки", "Assistant Prefill": "Префилл для ассистента", "Start Claude's answer with...": "Начать ответ Клода с...", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "Отправлять системный промпт для поддерживаемых моделей. Если отключено, в начало промпта добавляется сообщение пользователя.", "Prompts": "Промпты", "Total Tokens:": "Всего токенов:", "Insert prompt": "Вставить промпт", "Delete prompt": "Удалить промпт", "Import a prompt list": "Импортировать список промптов", "Export this prompt list": "Экспортировать этот список промптов", "Reset current character": "Сбросить текущего персонажа", "New prompt": "Новый промпт", "Tokens": "<PERSON>о<PERSON><PERSON><PERSON><PERSON>", "Want to update?": "Хотите обновиться?", "How to start chatting?": "Как начать общение?", "and select a": " и выберите ", "Chat API": "API чата", "in the chat bar": " в поле чата", "Confused or lost?": "Не можете в чём-то разобраться?", "click these icons!": "нажмите на эти значки!", "SillyTavern Documentation Site": "Сайт документации SillyTavern", "Extras Installation Guide": "Руководство по установке Extras", "Still have questions?": "Остались вопросы?", "Join the SillyTavern Discord": "Заходите на сервер SillyTavern в Discord", "Post a GitHub issue": "Опубликуйте GitHub issue", "Contact the developers": "Свяжитесь с разработчиками", "Nucleus Sampling": "<PERSON><PERSON><PERSON><PERSON>", "Typical P": "Typical P", "Top K Sampling": "Top K", "Top A Sampling": "Top A", "Off": "Выключено", "Very light": "Очень легкий", "Light": "Легкий", "Medium": "Средний", "Aggressive": "Агрессивный", "Very aggressive": "Очень агрессивный", "Eta_Cutoff_desc": "Eta cutoff - основной параметр специальной техники сэмплинга под названием Eta Sampling.\nВ единицах 1e-4; разумное значение - 3.\nУстановите в 0, чтобы отключить.\nСм. статью Truncation Sampling as Language Model Desmoothing от Хьюитт и др. (2022) для получения подробной информации.", "Learn how to contribute your idle GPU cycles to the Horde": "Узнайте, как использовать время простоя вашего GPU для помощи Horde", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "Используйте соответствующий токенизатор для моделей Google через их API. Медленная обработка подсказок, но предлагает намного более точный подсчет токенов.", "Load koboldcpp order": "Загрузить порядок из koboldcpp", "Use Google Tokenizer": "Использовать токенизатор Google", "koboldcpp API key (optional)": "Ключ от API koboldcpp (необязательно)", "Reverse Proxy": "Прокси", "Use Proxy password field instead. This input will be ignored.": "Используйте поле \"Пароль от прокси\". Значение в этом окошке игнорируется", "Proxy Password": "Пароль от прокси", "Will be used as a password for the proxy instead of API key.": "Будет использован в качестве пароля от прокси вместо ключа от API", "Proxy Presets": "Пресеты для прокси", "Saved addresses and passwords.": "Сохранённые адреса и пароли.", "Save Proxy": "Сохранить прокси", "Delete Proxy": "Удалить прокси", "Proxy Name": "Название прокси", "This will show up as your saved preset.": "Будет отображено в вашем списке пресетов.", "Proxy Server URL": "Адрес прокси-сервера", "MistralAI Model": "Модель MistralAI", "MistralAI API Key": "Ключ от API MistralAI", "Google AI Studio API Key": "Ключ от API Google AI Studio", "Google Model": "Модель Google", "Cohere API Key": "Ключ от API Cohere", "Cohere Model": "Модель Cohere", "Model Order": "Сортировка моделей OpenRouter", "Alphabetically": "По алфавиту", "Price": "По цене (наиболее низкая)", "Context Size": "По размеру контекста", "Group by vendors": "Сгруппировать по владельцу", "Group by vendors Description": "Модели от OpenAI попадут в одну группу, от Anthropic - в другую, и т.д. Можно комбинировать с сортировкой.", "Allow Jailbreak": "Разрешить джейлбрейк", "System Prompt Wrapping": "Обрамление для системного промпта", "System Prompt Prefix": "Префикс системного промпта", "System Prompt Suffix": "Постфикс системного промпта", "Chat Messages Wrapping": "Обрамление для сообщений в чате", "User Prefix": "Префикс сообщения пользователя", "User Suffix": "Постфикс сообщения пользователя", "Assistant Prefix": "Префикс сообщения ассистента", "Assistant Suffix": "Постфикс сообщения ассистента", "System Prefix": "Префикс сообщения системы", "System Suffix": "Постфикс сообщения системы", "System same as User": "Для системы то же самое, что и для пользователя", "Misc. Sequences": "Прочие строки", "First Assistant Prefix": "Первый префикс ассистента", "Last Assistant Prefix": "Последний префикс ассистента", "System Instruction Prefix": "Префикс системной инструкции", "User Filler Message": "Принудительное сообщение пользователя", "Permanent": "постоянных", "Alt. Greetings": "Др. вариа<PERSON><PERSON>ы", "Smooth Streaming": "Плавный стриминг", "Save checkpoint": "Сохранить чекпоинт", "Min Activations": "Мин. число активаций", "Scan chronologically until reached min entries or token budget.": "Сканировать в хронологической последовательности, пока не будет достигнуто мин. число вхождений или не израсходуется бюджет токенов.", "Max Depth": "Макс. глубина", "(0 = unlimited, use budget)": "(0 = безграничная, использовать бюджет)", "Ext. Media": "Внешн. медиа", "Multiple swipes per generation": "Несколько свайпов на генерацию", "Set to get deterministic results. Use -1 for random seed.": "Используется для получения предсказуемого результата. Введите -1 для случайного зерна", "Most tokens have a leading space.": "У большинства токенов в начале пробел.", "Text or token ids": "Текст или [идентификаторы токенов]", "World Info Format Template": "Шаблон оформления информации о мире", "Wraps activated World Info entries before inserting into the prompt.": "Дополняет информацию об активном на данный момент мире перед её отправкой в промпт.", "Doesn't work? Try adding": "Не работает? Попробуйте добавить в конце", "at the end!": "!", "Authorize": "Авторизоваться", "No persona description": "[Нет описания]", "Not connected to API!": "Нет соединения с API!", "Type a message, or /? for help": "Введите сообщение, или /? для получения справки", "Welcome to SillyTavern!": "Добро пожаловать в SillyTavern!", "Won't be shared with the character card on export.": "Не попадут в карточку персонажа при экспорте.", "Persona Name:": "Имя персоны:", "User first message": "Первое сообщение пользователя", "extension_token_counter": "Токенов:", "Character's Note": "Заметка о персонаже", "(Text to be inserted in-chat @ designated depth and role)": "Этот текст будет вставлен в чат на заданную глубину и с определённой ролью", "@ Depth": "Гл<PERSON><PERSON><PERSON><PERSON>", "Role": "Роль", "System": "Система", "User": "Пользователь", "Assistant": "Ассистент", "How often the character speaks in": "Как часто персонаж говорит в", "Creator's Metadata": "Метаданные", "(Not sent with the AI Prompt)": "(не отправляются ИИ)", "New Chat": "Новый чат", "Import Chat": "Импорт чата", "Chat Lore": "<PERSON>ор чата", "Chat Lorebook for": "Лорбук для чата", "Missing key": "❌ Ключа нет", "Key saved": "✔️ Ключ сохранён", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "Использовать токенайзер для моделей Jurassic, эффективнее GPT-токенайзера", "Experimental feature. May not work for all backends.": "Экспериментальная возможность, на некоторых бэкендах может не работать.", "Avatar Hover Magnification": "Зум аватарки по наведению", "Enable magnification for zoomed avatar display.": "Добавляет возможность приближать увеличенную версию аватарки.", "Unique to this chat": "Только для текущего чата", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "Чекпоинты наследуют заметки от родительского чата, но впоследствие их всегда можно изменить.", "Include in World Info Scanning": "Учитывать при сканировании лорбуком", "Before Main Prompt / Story String": "Перед основным промптом / общим шаблоном", "After Main Prompt / Story String": "После основного промпта / общего шаблона", "In-chat @ Depth": "Встав. на глуб.", "as": "роль:", "Insertion Frequency": "Частота вставки", "(0 = Disable, 1 = Always)": "(0 = никогда, 1 = всегда)", "User inputs until next insertion:": "Ваших сообщений до след. вставки:", "Character Author's Note (Private)": "Авторские заметки для персонажа (личные)", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "Автоматически применятся к этому персонажу в качестве авторских заметок. Будут использоваться в группах, но при активном групповом чате к редактированию недоступны.", "Use character author's note": "Использовать авторские заметки для персонажа", "Replace Author's Note": "Вместо авторских заметок", "Default Author's Note": "Стандартные авторские заметки", "Will be automatically added as the Author's Note for all new chats.": "Будут автоматически добавляться во все новые чаты в качестве авторских заметок", "1 = disabled": "1 = откл.", "write short replies, write replies using past tense": "пиши короткие ответы, пиши в настоящем времени", "Positive Prompt": "Положительный промпт", "Character CFG": "CFG для персонажа", "Will be automatically added as the CFG for this character.": "Автоматически применится к персонажу как его CFG.", "Global CFG": "Глобальный CFG", "Will be used as the default CFG options for every chat unless overridden.": "Будет применяться как стандартный CFG для всех чатов, если не указаны индивидуальные настройки.", "CFG Prompt Cascading": "Совмещение CFG-промптов", "Combine positive/negative prompts from other boxes.": "Комбинировать различные положительные и негативные промпты.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "К примеру, если отметить галочки с чатом, персонажем и глобальной настройкой, то все эти негативы соберутся в одну строку, разделённую запятыми.", "Always Include": "Всегда применять", "Chat Negatives": "Негативы от чата", "Character Negatives": "Негативы от персонажа", "Global Negatives": "Глобальные негативы", "Custom Separator:": "Кастомный разделитель:", "Insertion Depth:": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> вставки:", "Chat CFG": "CFG для чата", "bg_chat_hint_1": "Здесь будут появляться фоны, сгенерированные расширением", "bg_chat_hint_2": ".", "Prevent further recursion": "Пресечь дальнейшую рекурсию", "Alert if your world info is greater than the allocated budget.": "Оповещать, если ваш мир выходит за выделенный бюджет.", "Convert to Persona": "Преобразовать в персону", "Link to Source": "Ссылка на источник", "Replace / Update": "Заменить / Обновить", "Smoothing Curve": "Кривая сглаживания", "Message Actions": "Действия с сообщением", "SillyTavern is aimed at advanced users.": "SillyTavern рассчитана на продвинутых пользователей.", "If you're new to this, enable the simplified UI mode below.": "Если вы новичок, советуем включить упрощённый UI.", "Enable simple UI mode": "Включить упрощённый UI", "welcome_message_part_1": "Ознакомьтесь с", "welcome_message_part_2": "официальной документацией", "welcome_message_part_3": ".", "welcome_message_part_4": "Введите", "welcome_message_part_5": "в чате, чтобы получить справку по командам и макросам.", "welcome_message_part_6": "Заходите на наш", "Discord server": "Discord-сервер,", "welcome_message_part_7": "там публикуется много разной полезной информации, в том числе анонсы.", "Before you get started, you must select a persona name.": "Для начала вам следует выбрать имя своей персоны.", "welcome_message_part_8": "Его можно будет изменить в любое время через иконку", "welcome_message_part_9": ".", "Ignore EOS Token": "Игнорировать EOS-токен", "Ignore the EOS Token even if it generates.": "Игнорировать EOS-токен, даже если он сгенерировался.", "Hide Muted Member Sprites": "Скрыть спрайты заглушенных участников", "Group generation handling mode": "Генерировать ответы путём...", "Swap character cards": "Подмены карточки персонажа", "Join character cards (exclude muted)": "Совмещения карточек (кроме заглушенных)", "Join character cards (include muted)": "Совмещения карточек (включая заглушенных)", "Click to allow/forbid the use of external media for this group.": "Нажмите, чтобы разрешить/запретить использование внешних медиа в этой группе.", "Scenario Format Template": "Шаб<PERSON>он оформления сценария", "scenario_format_template_part_1": "Используйте", "scenario_format_template_part_2": "чтобы указать, куда именно вставляется основное содержимое.", "Personality Format Template": "Шаб<PERSON>он оформления характера", "Group Nudge Prompt Template": "Шаблон промпта-подсказки для групп", "Sent at the end of the group chat history to force reply from a specific character.": "Добавляется в конец истории сообщений в групповом чате, чтобы запросить ответ от конкретного персонажа.", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "Добавляется в начале истории сообщений в качестве указания на то, что дальше начнётся новый чат.", "New Group Chat": "Новый групповой чат", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "Добавляется в начале истории сообщений в качестве указания на то, что дальше начнётся новый групповой чат.", "New Example Chat": "Новый образец чата", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "Добавляется в начале примеров диалогов в качестве указания на то, что дальше начнётся новый чат-пример.", "Continue nudge": "Подсказка для продолжения", "Set at the end of the chat history when the continue button is pressed.": "Добавляется в конец истории чата, когда отправлен запрос на продолжение текущего сообщения.", "Continue Postfix": "Постфикс для продолжения", "Space": "Пробел", "Newline": "Новая строка", "Double Newline": "Две новые строки", "The next chunk of the continued message will be appended using this as a separator.": "Используется в качестве разделителя между уже имеющимся сообщением и его новым отрывком, при генерации продолжения", "Regex Editor": "Редактор рег. выражений", "ext_regex_import_script": "Импорт скрипта", "ext_regex_desc": "Regex - это инструмент, позволяющий находить и изменять строки, используя регулярные выражения. Для более подробной информации нажмите ? рядом с заголовком.", "Input": "Поле ввода", "ext_regex_test_input_placeholder": "Введите текст...", "Output": "Результат", "ext_regex_output_placeholder": "Пусто", "Script Name": "Название скрипта", "Find Regex": "Рег. выражение для поиска", "Replace With": "Замена", "ext_regex_replace_string_placeholder": "Чтобы вставить всё вхождение рег. выражения, используйте {{match}}. Чтобы вставить группу символов, используйте $1, $2 и т.д.", "Trim Out": "Усечение", "ext_regex_trim_placeholder": "Удалить перед обработкой ненужные части текста. Каждый элемент с новой строки.", "Slash Commands": "Слэш-команды", "Min Depth": "Ми<PERSON>. глубина", "ext_regex_min_depth_desc": "При форматировании затрагивать только те сообщения, которые находятся как минимум на глубине N. 0 = последнее сообщение, 1 = предпоследнее и т.д. Учитываются только видимые сообщения, т.е. не скрытые и не системные.", "ext_regex_max_depth_desc": "При форматировании затрагивать только те сообщения, которые находятся на глубине не более N. 0 = последнее сообщение, 1 = предпоследнее и т.д. Учитываются только видимые сообщения, т.е. не скрытые и не системные.", "ext_regex_min_depth_placeholder": "Неогр<PERSON><PERSON><PERSON><PERSON>.", "ext_regex_other_options": "Другие опции", "Only Format Display": "Только визуально", "ext_regex_only_format_prompt_desc": "История чата не изменится, замена будет осуществляться только в промпте (при генерации)", "Only Format Prompt (?)": "Только промпт", "Run On Edit": "Выполнять при редактировании", "Substitute Regex": "Заменить в рег. выражении", "ext_regex_substitute_regex_desc": "Перед выполнением заменять {{макросы}} в рег. выражении", "Test Mode": "Протестировать", "ext_regex_affects": "Затрагивает", "ext_regex_user_input": "Ваши сообщения", "ext_regex_ai_output": "Ответы ИИ", "ext_regex_disable_script": "Выключить этот скрипт", "ext_regex_enable_script": "Включить скрипт", "ext_regex_edit_script": "Редактировать", "ext_regex_export_script": "Экспортировать", "ext_regex_delete_script": "Удалить", "ext_sum_with": "Для пересказа использовать:", "ext_sum_main_api": "Основное API", "ext_sum_current_summary": "Текущий пересказ:", "ext_sum_restore_previous": "Восстановить предыдущий", "ext_sum_memory_placeholder": "Сгенерированный пересказ будет здесь...", "ext_sum_force_text": "Пересказать сейчас", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "Отключить авто-обновление пересказа. Пересказ всё время будет фиксированным. Однако останется возможность принудительно обновить пересказ через кнопку \"Пересказать сейчас\" (доступно только через Основное API)", "ext_sum_pause": "Приостановить", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "Исключать из пересказа Информацию о мире и Авторские заметки. Работает только для Основного API. Extras API всегда их исключает.", "ext_sum_no_wi_an": "Без мира и заметок", "ext_sum_settings_tip": "Изменить промпт пересказа, место для инжекта и т.д.", "ext_sum_settings": "Настройки пересказа", "ext_sum_prompt_builder": "Алгоритм формирования промпта", "ext_sum_prompt_builder_1_desc": "Расширение само составит промпт с учётом непересказанных сообщений. Во время генерации чат недоступен.", "ext_sum_prompt_builder_1": "Прям<PERSON>й, блокирующий", "ext_sum_prompt_builder_2_desc": "Расширение само составит промпт с учётом непересказанных сообщений. Во время генерации чат доступен. Может не поддерживаться некоторыми бэкендами.", "ext_sum_prompt_builder_2": "Прямой, неблокирующий", "ext_sum_prompt_builder_3_desc": "Расширение будет использовать стандартные основные настройки промпта, и добавит свой промпт в качестве последнего системного сообщения.", "ext_sum_prompt_builder_3": "Классический, блокирующий", "Summary Prompt": "Промпт для пересказа", "ext_sum_restore_default_prompt_tip": "Восстановить стандартный промпт", "ext_sum_prompt_placeholder": "Этот промпт будет отправлен ИИ при запросе на генерацию пересказа. Макрос {{words}} будет заменён на значение параметра \"Количество слов\".", "ext_sum_target_length_1": "Целевая длина пересказа (слов):", "ext_sum_target_length_2": "", "ext_sum_target_length_3": "", "ext_sum_api_response_length_1": "Длина ответа от API (токенов):", "ext_sum_api_response_length_2": "", "ext_sum_api_response_length_3": "   ", "ext_sum_0_default": "по умолчанию = 0", "ext_sum_raw_max_msg": "[Прямое форматирование] Макс. сообщений в запросе", "ext_sum_0_unlimited": "неограничено = 0", "Update frequency": "Частота обновления", "ext_sum_update_every_messages_1": "Интервал обновления (кол-во сообщений):", "ext_sum_update_every_messages_2": "", "ext_sum_update_every_words_1": "Интервал обновления (кол-во слов):", "ext_sum_update_every_words_2": "", "ext_sum_0_disable": "для отключения поставьте 0", "ext_sum_auto_adjust_desc": "Попытаться автоматически рассчитать значение интервала, исходя из статистики чата", "ext_sum_both_sliders": "Если оба ползунка отличны от нуля, то оба будут триггерить генерацию пересказа с соответствующей периодичностью.", "ext_sum_injection_template": "Шаблон для инжекта", "ext_sum_memory_template_placeholder": "Макрос {{summary}} будет заменён на содержимое пересказа", "ext_sum_injection_position": "Куда инжектить", "How many messages before the current end of the chat.": "Сколько сообщений от конца чата.", "Change it later in the 'User Settings' panel.": "Его можно будет выключить в меню \"Настройки пользователя\"", "Looking for AI characters?": "Ищете ИИ-персонажей?", "onboarding_import": "Импортируйте", "from supported sources or view": "из источника или посмотрите", "Sample characters": "Стандартных персонажей", "popup-button-save": "Сохранить", "popup-button-yes": "Да", "popup-button-no": "Нет", "popup-button-cancel": "Отмена", "popup-button-import": "Импортировать", "Enter the URL of the content to import": "Введите URL-адрес импортируемого контента", "Supported sources:": "Поддерживаются следующие источники:", "char_import_example": "Пример:", "char_import_1": "Персона<PERSON> с Chub (прямая ссылка или ID)", "char_import_2": "Лорбук с Chub (прямая ссылка или ID)", "char_import_3": "Персонаж с JanitorAI (прямая ссылка или UUID)", "char_import_4": "Персонаж с Pygmalion.chat (прямая ссылка или UUID)", "char_import_5": "Персонаж с AICharacterCards.com (прямая ссылка или ID)", "char_import_6": "Прямая ссылка на PNG-файл (список разрешённых хостов находится в", "char_import_7": ")", "char_import_10": "Персонаж с <PERSON> (прямая ссылка или UUID + .gz)", "Grammar String": "Грамматика", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF или EBNF, зависит от бэкенда. Если вы это используете, то, скорее всего, сами знаете, какой именно.", "Account": "Аккаунт", "Hi,": "Привет,", "To enable multi-account features, restart the SillyTavern server with": "Чтобы активировать систему аккаунтов, перезапустите SillyTavern, выставив", "set to true in the config.yaml file.": "в файле config.yaml в положение true.", "Account Info": "Об аккаунте", "Handle:": "Хэндл:", "Role:": "Роль:", "Created:": "Создан:", "Password:": "Пароль:", "This account is password protected.": "Аккаунт защищён паролем.", "This account is not password protected.": "Аккаунт не защищён паролем.", "Account Actions": "Действия", "Settings Snapshots": "Снимки настроек", "Manage your settings snapshots.": "Управление снимками настроек.", "Download Backup": "Скачать бэкап", "Download a complete backup of your user data.": "Скачать полный бэкап данных пользователя.", "Danger Zone": "Опасно", "Reset Settings": "Сбросить настройки", "Reset your settings to factory defaults.": "Сбросить настройки до заводских.", "Reset Everything": "Сбросить всё", "Wipe all user data and reset your account to factory settings.": "Стереть все данные пользователя и сбросить аккаунт до заводских настроек.", "Set your custom avatar.": "Установить аватарку", "Remove your custom avatar.": "Сбросить аватарку", "Make a Snapshot": "Сделать снимок", "never_resize_avatars_tooltip": "Не менять размер картинок у импортируемых персонажей. При отключении все картинки будут приводиться к размеру 512х768", "Char List Subheader": "Доп. заголовок в списке персонажей", "# Messages to Load": "Сколько сообщений загружать", "(0 = All)": "(0 = все)", "Theme Colors": "Цвета темы", "Specify colors for your theme.": "Настройте собственные цвета для вашей темы.", "Update speed of streamed text.": "Скорость обновления текста при стриминге.", "The number of chat history messages to load before pagination.": "Кол-во сообщений чата, загружаемых перед пагинацией.", "Chat Width": "<PERSON><PERSON><PERSON><PERSON><PERSON> чата", "Width of the main chat window in % of screen width": "Ширина окна с чатом, в % от ширины экрана", "Blur strength on UI panels.": "Сила размытия на панелях UI.", "Font size": "Размер шрифта", "Strength of the text shadows": "Размер теней, отбрасываемых текстом", "prompt_manager_edit": "Редактирование", "prompt_manager_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prompt_manager_name": "Имя", "A name for this prompt.": "Имя данного промпта.", "To whom this message will be attributed.": "От чьего лица будет отправляться сообщение.", "AI Assistant": "ИИ-ассистент", "prompt_manager_position": "Точка инжекта", "Next to other prompts (relative) or in-chat (absolute).": "Она может располагаться по отношению к другим промптам (относительная) либо по отношению к чату (абсолютная).", "prompt_manager_relative": "Относительная", "prompt_manager_depth": "Гл<PERSON><PERSON><PERSON><PERSON>", "0 = after the last message, 1 = before the last message, etc.": "0 = после последнего сообщения, 1 = перед последним сообщением, и т.д.", "The prompt to be sent.": "Текст промпта.", "prompt_manager_forbid_overrides": "Запретить перезапись", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "Карточка персонажа не сможет перезаписать этот промпт, даже если настройки отдают приоритет именно ей.", "image_inlining_hint_1": "Отправлять картинки как часть промпта, если позволяет модель. Чтобы добавить в чат изображение, используйте на нужном сообщении действие", "image_inlining_hint_2": ". Также это можно сделать через меню", "image_inlining_hint_3": ".", "Contest Winners": "Победители конкурса", "Rename background": "Переименовать фон", "Lock": "Закрепить", "Unlock": "Открепить", "Delete background": "Удалить фон", "Export all": "Экспортировать всё", "Export all your prompts to a file": "Экспортировать все промпты в виде файла", "Don't add character names.": "Не добавлять имя персонажа", "Add character names to completion objects.": "Добавлять имя персонажа как часть Completion object", "Message Content": "Внутри сообщения", "Prepend character names to message contents.": "Предварять сообщения именем персонажа.", "Character Names Behavior": "Вставка имени персонажа", "and pick a character.": "и выберите персонажа.", "Record a snapshot of your current settings.": "Сделать снимок текущих настроек.", "Restore this snapshot": "Откатиться к этому снимку", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "Чтобы сменить аватарку, используйте кнопки ниже, либо выберите персону по умолчанию в меню управления персоной.", "These characters are the winners of character design contests and have outstandable quality.": "Персонажи наивысшего качества, одержавшие победу в конкурсе персонажей.", "Featured Characters": "Рекомендуемые персонажи", "These characters are the finalists of character design contests and have remarkable quality.": "Персонажи отличного качества, финалисты конкурса персонажей.", "Inline Image Quality": "Качество inline-изображений", "openai_inline_image_quality_auto": "Автоопределение", "openai_inline_image_quality_low": "Низкое", "openai_inline_image_quality_high": "Высокое", "Assistant Impersonation Prefill": "Префилл для ассистента при перевоплощении", "Hide Chat Avatars": "Не показывать аватарки в чате", "Hide avatars in chat messages.": "Скрыть аватарки сбоку от сообщений в чате", "Flat": "Стандартный", "Toggle character info panel": "Показать / скрыть инфо-панель", "(For Chat Completion and Instruct Mode)": "(для Chat Completion и режима Instruct)", "help_1": "Приветствуем! Выберите тему, которая вас интересует:", "help_2": "Слэш-команды", "help_or": "также", "help_3": "Разметка", "help_4": "Горячие клавиши", "help_5": "{{Макрос<PERSON>}}", "help_6": "Не нашли ответа на свой вопрос? Загляните в", "help_7": "официальную документацию SillyTavern", "help_8": ", там система описана гораздо более подробно!", "help_format_1": "Команды для разметки текста:", "help_format_2": "*текст*", "help_format_3": "добавляет ", "help_format_4": "кур<PERSON><PERSON>в", "help_format_5": "**текст**", "help_format_6": "делает текст ", "help_format_7": "полужирным", "help_format_8": "***текст***", "help_format_9": "добавляет ", "help_format_10": "полужирный курсив", "help_format_11": "__текст__", "help_format_12": "добавляет ", "help_format_13": "подчёркивание", "help_format_14": "~~текст~~", "help_format_15": "делает текст ", "help_format_16": "зачёркнутым", "help_format_17": "[текст](url)", "help_format_18": "добавляет в текст ", "help_format_19": "ссылку", "help_format_20": "![текст](url)", "help_format_21": "добавляет в текст картинку", "help_format_22": "```текст```", "help_format_23": "оформляет текст как кусок кода (символ новой строки также допустим)", "help_format_like_this": "вот так", "help_format_24": "`текст`", "help_format_25": "отображ<PERSON>ет кусок кода ", "help_format_26": "внутри строки", "help_format_27": "> текст", "help_format_28": "отображ<PERSON><PERSON>т текст как цитату (обратите внимание на пробел после >)", "help_format_29": "# текст", "help_format_30": "создаёт крупный заголовок (обратите внимание на пробел)", "help_format_32": "## текст", "help_format_33": "создаёт средний заголовок (обратите внимание на пробел)", "help_format_35": "### текст", "help_format_36": "создаёт малый заголовок (обратите внимание на пробел)", "help_format_38": "$$ текст $$", "help_format_39": "отрендерить в виде формулы LaTeX (если включено в настройках)", "help_format_40": "$ текст $", "help_format_41": "отрендерить в виде формулы AsciiMath (если включено в настройках)", "help_hotkeys_1": "Стрелка вверх", "help_hotkeys_2": "Редактировать последнее сообщение в чате", "help_hotkeys_3": "Ctrl+Стрелка вверх", "help_hotkeys_4": "Редактировать ВАШЕ последнее сообщение в чате", "help_hotkeys_5": "Стрелка влево", "help_hotkeys_6": "Свайп влево", "help_hotkeys_7": "Стрелка вправо", "help_hotkeys_8": "Свайп вправо (ОБРАТИТЕ ВНИМАНИЕ: если в окошке ввода что-то есть, клавиши для свайпов не работают)", "help_hotkeys_10": "(при фокусе на окошке ввода)", "help_hotkeys_10_1": "Отправить сообщение", "help_hotkeys_12": "Повторно сгенерировать последний ответ ИИ", "help_hotkeys_14": "Продолжить последний ответ ИИ", "help_hotkeys_16": "Остановить генерацию ответа, скрыть UI-панель, отменить редактирование сообщения", "help_hotkeys_17": "Ctrl+Shift+Вверх", "help_hotkeys_18": "Прокрутить до линии контекста", "help_hotkeys_19": "Ctrl+Shift+Вниз", "help_hotkeys_20": "Горячие клавиши для разметки", "help_hotkeys_0": "Горячие клавиши", "You can browse a list of bundled characters in the": "Комплектных персонажей можно найти в меню", "Download Extensions & Assets": "Загрузить расширения и ресурсы", "menu within": "в меню", "Assets URL": "URL с описанием ресурсов", "Custom (OpenAI-compatible)": "Кастомный (совместимый с OpenAI)", "Custom Endpoint (Base URL)": "Кастомный эндпоинт (базовый URL)", "Example: http://localhost:1234/v1": "Пример: http://localhost:1234/v1", "Custom API Key": "Ключ от кастомного API", "(Optional)": "(необязательно)", "Enter a Model ID": "Введите идентификатор модели", "Example: gpt-4o": "Пример: gpt-4o", "Available Models": "Доступные модели", "Prompt Post-Processing": "Постобработка промпта", "Applies additional processing to the prompt before sending it to the API.": "Позволяет обработать промпт перед отправкой в API.", "prompt_post_processing_none": "Отсутствует", "Additional Parameters": "Доп. параметры", "Include Body Parameters": "Добавить параметры в тело", "custom_include_body_desc": "Эти параметры будут добавлены в тело запроса к API Chat Completion (YAML-объект)\n\nПример:\ntop_k: 20\nrepetition_penalty: 1.1", "Exclude Body Parameters": "Исключить параметры из тела", "custom_exclude_body_desc": "Эти параметры будут исключены из тела запроса к API Chat Completion (YAML-массив)\n\nПример:\n- frequency_penalty\n- presence_penalty", "Include Request Headers": "Добавить заголовки запроса", "custom_include_headers_desc": "Дополнительные заголовки, добавляемые к запросу в API Chat Completion (YAML-объект)\n\nПример:\nCustomHeader: custom-value\nAnotherHeader: custom-value", "Groq API Key": "Ключ от API Groq", "Groq Model": "Модель Groq", "Perplexity API Key": "Ключ от API Perplexity", "Perplexity Model": "Модель Perplexity", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "Создаёт эффект приближения, когда вы наводитесь мышкой на изображение, отображаемое по клику на аватарку в чате.", "STscript Settings": "Настройки STscript", "Sets default flags for the STscript parser.": "Зада<PERSON>т стандартные значения флагов для парсера STscript.", "Parser Flags": "Флаги парсера", "ext_regex_new_global_script": "+ Глобальный", "ext_regex_new_scoped_script": "+ Локальный", "ext_regex_new_global_script_desc": "Создать глобальный regex-скрипт", "ext_regex_new_scoped_script_desc": "Создать локальный regex-скрипт", "ext_regex_global_scripts": "Глобальные скрипты", "ext_regex_global_scripts_desc": "Распространяются на всех персонажей. Сохраняются как часть настроек.", "ext_regex_scoped_scripts": "Локальные скрипты", "ext_regex_scoped_scripts_desc": "Распространяются только на этого персонажа. Сохраняются в его карточку.", "ext_regex_allow_scoped": "Включить локальные скрипты", "ext_regex_disallow_scoped": "Отключить локальные скрипты", "ext_regex_move_to_global": "Сделать глобальным", "openai_logit_bias_no_items": "Правил нет", "Enable function calling": "Включить функции", "enable_functions_desc_1": "Включает возможность ", "enable_functions_desc_2": "вызова функций", "enable_functions_desc_3": "Используется расширениями для добавления нового функционала.", "MAD LAB MODE ON": "ВКЛЮЧЕН РЕЖИМ БЕЗУМЦА", "api_no_connection": "Нет соединения...", "Sampler Select": "Выбор сэмплеров", "Customize displayed samplers or add custom samplers.": "Выберите, какие сэмплеры хотите здесь отображать, или добавьте собственные сэмплеры", "ext_translate_auto_mode": "Автоперевод", "ext_translate_mode_none": "От<PERSON><PERSON>.", "ext_translate_mode_responses": "Переводить ответы ИИ", "ext_translate_mode_inputs": "Переводить ваши сообщения", "ext_translate_mode_both": "Переводить всё", "ext_translate_mode_provider": "Переводчик", "ext_translate_target_lang": "На какой язык переводить", "ext_translate_clear": "Очистить переводы", "ext_translate_btn_chat": "Перевести чат", "ext_translate_btn_input": "Перевести моё сообщение", "ext_translate_delete_confirm_1": "Вы уверены?", "ext_translate_delete_confirm_2": "Будет удалён текст всех имеющихся в этом чате переводов. Отменить это действие невозможно.", "Favorite": "Избранное", "Tag": "Тег", "Duplicate": "Клонировать", "Persona": "Персона", "novelaipresets": "Пресеты NovelAI", "No Module": "No Module", "Instruct": "Instruct", "Prose Augmenter": "Prose Augmenter", "Text Adventure": "Text Adventure", "Streaming_desc": "Выводить текст последовательно по мере его генерации.\rЕсли параметр выключен, ответы будут отображаться сразу целиком, и только после полного завершения генерации.", "Max prompt cost:": "Макс. стоимость промпта:", "TFS": "TFS", "Count Penalty": "Count Penalty", "Min P": "<PERSON>", "NSFW": "NSFW", "Restore default format": "Restore default format", "Restore new chat prompt": "Restore new chat prompt", "Restore new group chat prompt": "Restore default prompt", "Mirostat": "Mirostat", "Mode": "Режим", "Mirostat_Mode_desc": "0 = отключить полностью. 1 = включить Mirostat 1.0. 2 = включить Mirostat 2.0.", "Tau": "Tau", "Eta": "Eta", "Ban_EOS_Token_desc": "Запретить токен конца последовательности (EOS) (актуально для KoboldCpp, но KoboldAI может запрещать ещё и другие токены).\rПодходит для написания историй, но не рекомендуется для режимов Chat и Instruct.", "Mirostat LR": "Mirostat LR", "rep.pen decay": "Rep <PERSON>", "Skew": "Skew", "Smooth Sampling": "Smooth Sampling", "Smooth_Sampling_desc": "Изменяет распределение с помощью квадратичных и кубических преобразований. Снижение Коэффициента сглаживания даёт более креативные ответы, обычно идеальное значение находится в диапазоне 0.2-0.3 (при кривой сглаживания=1.0). Повышение значения Кривой сглаживания сделает кривую круче, что приведёт к более агрессивной фильтрации маловероятных вариантов. Установив Кривую сглаживания = 1.0, вы фактически нейтрализуете этот параметр и будете работать только с Коэффициентом", "DRY_Repetition_Penalty_desc": "DRY налагает штраф на токены, генерация которых приведёт к появлению строки, которая уже была в тексте раньше. Установите множитель = 0, чтобы отключить.", "DRY Repetition Penalty": "DRY Штраф за повтор", "DRY_Multiplier_desc": "Поставьте в положение > 0, что<PERSON><PERSON> включить DRY. Определяет величину штрафа для кратчайшей \"штрафуемой\" строки.", "Multiplier": "Множитель", "DRY_Base_desc": "Определяет, насколько быстро возрастает штраф с увеличением длины строки.", "Base": "Основание", "DRY_Allowed_Length_desc": "Длина повторяющейся строки, при превышении которой DRY начинает налагать штраф.", "Allowed Length": "Допустимая длина", "Penalty Range": "Окно для штрафа", "DRY_Sequence_Breakers_desc": "Токены, которые прерывают сопоставление/поиск строк. Вводятся через запятую, каждый брейкер в отдельных кавычках.", "Sequence Breakers": "Брейкеры для строк", "JSON-serialized array of strings.": "Список строк в формате JSON.", "Mirostat_desc": "Mirostat - своего рода термометр, измеряющий перплексию для выводимого текста.\nMirostat подгоняет перплексию генерируемого текста к перплексии входного текста, что позволяет избежать повторов.\n(когда по мере генерации текста авторегрессионным инференсом, перплексия всё больше приближается к нулю)\n а также ловушки перплексии (когда перплексия начинает уходить в сторону)\nБолее подробное описание в статье Mirostat: A Neural Text Decoding Algorithm that Directly Controls Perplexity by Basu et al. (2020).\nРежим выбирает версию Mirostat. 0=отключить, 1=Mirostat 1.0 (только llama.cpp), 2=Mirostat 2.0.", "Helpful tip coming soon.": "Подсказку скоро добавим.", "Speculative Ngram": "Speculative Ngram", "Use a different speculative decoding method without a draft model": "Use a different speculative decoding method without a draft model.\rUsing a draft model is preferred. Speculative ngram is not as effective.", "Spaces Between Special Tokens": "Spaces Between Special Tokens", "JSON Schema": "JSON Schema", "Type in the desired JSON schema": "Type in the desired JSON schema", "Top P & Min P": "Top P & Min P", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.", "Helps the model to associate messages with characters.": "Помогает модели связывать сообщения с персонажами.", "character_names_default": "Добавлять префиксы для групповых чатов и предыдущих персон. В остальных случаях указывайте имена в промпте иными способами.", "Completion": "Completion Object", "character_names_completion": "Только латинские буквы, цифры и знак подчёркивания. Работает не для всех бэкендов, в частности для Claude, MistralAI, Google.", "Use AI21 Tokenizer": "Использовать токенайзер AI21", "(Gemini 1.5 Pro/Flash only)": "(только Gemini 1.5 Pro/Flash)", "Merges_all_system_messages_desc_1": "Объединяет все системные сообщения до первого не-системного, и отсылает их в поле", "Merges_all_system_messages_desc_2": ".", "Restore User first message": "Restore User first message", "Human message": "Сообщение, инструкция и т.п. от лица человека.\nПри пустом поле ничего не отправляется, тогда требуется самостоятельно отправить промт от лица пользователя.", "Text Completion": "Text Completion", "Chat Completion": "Chat Completion", "Default (completions compatible)": "Default [OpenAI /completions compatible: oobabooga, LM Studio, etc.]", "Model Providers": "Провайдеры моделей", "InfermaticAI API Key": "Ключ от API InfermaticAI", "InfermaticAI Model": "Модель InfermaticAI", "DreamGen API key": "Ключ от API DreamGen", "DreamGen Model": "Модель DreamGen", "vllm-project/vllm": "vllm-project/vllm (режим враппера OpenAI API)", "vLLM API key": "Ключ от API vLLM", "vLLM Model": "Модель vLLM", "Aphrodite Model": "Модель Aphrodite", "Peek a password": "Посмотреть пароль", "Add Chat Start and Example Separator to a list of stopping strings.": "Использовать Начало чата и Разделитель примеров сообщений в качестве стоп-строк.", "Context Order": "Context Order", "Summary": "Summary", "Example Dialogues": "Примеры диалогов", "Hint": "Hint:", "In-Chat Position not affected": "Summary and Author's Note orders are only affected when they don't have an In-Chat position set.", "instruct_bind_to_context": "При включении этой опции Шаблон контекста будет выбираться, исходя из выбранного в текущий момент Шаблона Instruct-режима, либо по вашему желанию.", "Inserted before a System prompt.": "Вставляется перед системным промптом.", "Inserted after a System prompt.": "Вставляется после системного промпта.", "Inserted before a User message and as a last prompt line when impersonating.": "Вставляется перед сообщением пользователя в качестве последней строки промпта при перевоплощении.", "Inserted after a User message.": "Вставляется после сообщения пользователя.", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "Вставляется перед сообщением ассистента и в качестве последней строки промпта во время генерации ответа ИИ.", "Inserted after an Assistant message.": "Вставляется после сообщения ассистента.", "Inserted before a System (added by slash commands or extensions) message.": "Вставляется перед сообщением системы (может быть добавлено слэш-командой или расширением).", "Inserted after a System message.": "Вставляется после сообщения системы.", "If enabled, System Sequences will be the same as User Sequences.": "Для сообщений системы будет использоваться то же обрамление, что и для пользователя.", "Inserted before the first Assistant's message.": "Вставляется перед первым сообщением ассистента.", "instruct_last_output_sequence": "Вставляется перед последним сообщением ассистента либо в качестве последней строки промпта во время генерации ответа ИИ (если роль не системная / нейтральная).", "Will be inserted as a last prompt line when using system/neutral generation.": "Will be inserted as a last prompt line when using system/neutral generation.", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "Если ИИ генерирует стоп-строку, то всё после неё будет вырезано из ответа (включая и саму стоп-строку).", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "Вставляется в начале истории чата, если она начинается не с сообщения пользователя.", "Global World Info/Lorebook activation settings": "Глобальные настройки активации лорбука / Информации о мире", "Click to expand": "Щёлкните, чтобы развернуть", "Insertion Strategy": "Как инжектить", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "До групповых фильтров будут допущены только записи с наибольшим кол-вом совпадений", "Use Group Scoring": "Использовать Group Scoring", "Search": "Поиск", "Admin Panel": "Admin Panel", "Logout": "Logout", "Delete a theme": "Delete a theme", "Tags_as_Folders_desc": "Чтобы тег отображался как папка, его нужно отметить таковым в меню управления тегами. Нажмите сюда, чтобы открыть его.", "Slow": "Slow", "Fast": "Fast", "Reset MovingUI panel sizes/locations.": "Сбросить расположение и размеры панелей MovingUI.", "Expand the editor": "Expand the editor", "Disallow embedded media from other domains in chat messages": "Disallow embedded media from other domains in chat messages.", "AutoComplete Settings": "AutoComplete Settings", "Automatically hide details": "Automatically hide details", "Determines how entries are found for autocomplete.": "Determines how entries are found for autocomplete.", "Autocomplete Matching": "Matching", "Starts with": "Starts with", "Includes": "Includes", "Fuzzy": "Fuzzy", "Sets the style of the autocomplete.": "Sets the style of the autocomplete.", "Autocomplete Style": "Style", "Follow Theme": "Follow Theme", "Dark": "Dark", "Sets the font size of the autocomplete.": "Sets the font size of the autocomplete.", "Sets the width of the autocomplete.": "Sets the width of the autocomplete.", "Autocomplete Width": "<PERSON><PERSON><PERSON>", "chat input box": "chat input box", "entire chat width": "entire chat width", "full window width": "full window width", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.", "REPLACE_GETVAR": "REPLACE_GETVAR", "Extras API:": "Extras API:", "Extras API URL": "Extras API URL", "Total tokens": "Total tokens", "Calculating...": "Calculating...", "Permanent tokens": "Permanent tokens", "About Token 'Limits'": "About Token 'Limits'", "Import Tags": "Импортировать теги", "Click to allow/forbid the use of external media for this character.": "Нажмите, чтобы разрешить/запретить использование внешних медиа для этого персонажа.", "Inserted before each part of the joined fields.": "Inserted before each part of the joined fields.", "Join Prefix": "Join <PERSON>", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "When 'Join character cards' is selected, all respective fields of the characters are being joined together.\rThis means that in the story string for example all character descriptions will be joined to one big text.\rIf you want those fields to be separated, you can define a prefix or suffix here.\r\rThis value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)", "Inserted after each part of the joined fields.": "Inserted after each part of the joined fields.", "Join Suffix": "Join <PERSON>", "Auto Mode delay": "Задержка авто-режима", "Bulk_edit_characters": "Массовое редактирование персонажей\n\nЛКМ, чтобы выделить либо отменить выделение персонажа\nShift+ЛКМ, чтобы массово выделить либо отменить выделение персонажей\nПКМ, чтобы выбрать действие", "Bulk select all characters": "Выбрать всех персонажей", "Copy to system backgrounds": "Copy to system backgrounds", "Chat Scenario Override": "Перезапись сценария чата", "Chat Lorebook": "Лорбук для чата", "chat_world_template_txt": "Выбранный мир будет привязан к этому чату. Будет работать наряду с глобальным лорбуком и лором персонажа.", "Use tag as folder": "Тег-папка", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized": "Статус записи:\n 🔵 Постоянная\n 🟢 Обычная\n 🔗 Векторизованная", "WI_Entry_Status_Constant": "Постоянная", "WI_Entry_Status_Normal": "Обычная", "WI_Entry_Status_Vectorized": "Векторизованная", "WI_Entry_Status_Disabled": "Отключена", "Depth": "Гл<PERSON><PERSON><PERSON><PERSON>", "Probability": "Вероятность", "Duplicate world info entry": "Дублировать запись", "Delete world info entry": "Удалить запись", "Keywords or Regexes": "Ключевые слова или регулярки", "Comma separated list": "Comma separated list", "Switch to plaintext mode": "Вкл/выкл режим чистого текста", "(ignored if empty)": "(пустое поле игнорируется)", "Keywords or Regexes (ignored if empty)": "Ключевые слова или регулярки (пустое поле игнорируется)", "Comma separated list (ignored if empty)": "Список через запятую (пустой игнорируется)", "Can be used to automatically activate Quick Replies": "Используется для автоматической активации быстрых ответов (Quick Replies)", "Automation ID": "Automation ID", "( None )": "(Отсутствует)", "Add Memo": "Add Memo", "reset": "сбросить", "save": "сохранить", "Open checkpoint chat": "Открыть чат из чекпоинта", "Alternate Greetings": "Варианты первого сообщения", "Alternate_Greetings_desc": "Будут отображаться в виде свайпов для первого сообщения нового чата.\n                Участники групповых чатов могут выбирать одно из них при начале беседы.", "alternate_greetings_hint_1": "Чтобы начать, нажмите на кнопку", "alternate_greetings_hint_2": ".", "Forbid Media Override explanation": "Способность текущего персонажа/группы использовать в чате внешние медиа.", "Forbid Media Override subtitle": "Под медиа подразумеваются картинки, видео, аудио. Под внешними - любые, не хостящиеся на локальном сервере.", "Always forbidden": "Всегда запрещены", "Always allowed": "Всегда разрешены", "View contents": "View contents", "Remove the file": "Удалить файл", "Use character CFG scales": "Use character CFG scales", "Select a token to see alternatives considered by the AI.": "Select a token to see alternatives considered by the AI.", "Continue script execution": "Continue script execution", "Pause script execution": "Pause script execution", "Abort script execution": "Abort script execution", "Toggle Panels": "Toggle Panels", "Attach a File": "Приложить файл", "Open Data Bank": "Open Data Bank", "Enter a URL or the ID of a Fandom wiki page to scrape:": "Enter a URL or the ID of a Fandom wiki page to scrape:", "Examples:": "Examples:", "Example:": "Пример:", "Single file": "Single file", "All articles will be concatenated into a single file.": "All articles will be concatenated into a single file.", "File per article": "File per article", "Each article will be saved as a separate file.": "Not recommended. Each article will be saved as a separate file.", "Data Bank": "Банк данных", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "These files will be available for extensions that support attachments (e.g. Vector Storage).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "Drag and drop files here to upload.", "Date (Newest First)": "Date (Newest First)", "Date (Oldest First)": "Date (Oldest First)", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Size (Smallest First)": "<PERSON>ze (Smallest First)", "Size (Largest First)": "<PERSON><PERSON> (Largest First)", "Bulk Edit": "Bulk Edit", "Select All": "Select All", "Select None": "Select None", "Enable": "<PERSON><PERSON><PERSON>.", "Global Attachments": "Global Attachments", "These files are available for all characters in all chats.": "These files are available for all characters in all chats.", "Character Attachments": "Character Attachments", "These files are available for the current character in all chats they are in.": "These files are available for the current character in all chats they are in.", "Saved locally. Not exported.": "Saved locally. Not exported.", "Chat Attachments": "Chat Attachments", "These files are available for all characters in the current chat.": "These files are available for all characters in the current chat.", "Enter a base URL of the MediaWiki to scrape.": "Enter a base URL of the MediaWiki to scrape.", "Don't include the page name!": "Don't include the page name!", "Enter web URLs to scrape (one per line):": "Enter web URLs to scrape (one per line):", "Enter a video URL to download its transcript.": "Enter a video URL or ID to download its transcript.", "Expression API": "Local\nExtras\nLLM", "Trigger a summary update right now.": "Сгенерировать пересказ прямо сейчас.", "ext_regex_title": "Regex", "ext_regex_import_target": "Импортировать в:", "ext_regex_move_to_scoped": "Сделать локальным", "Image Generation": "Image Generation", "sd_auto_url": "Example: {{auto_url}}", "Authentication (optional)": "Данные для аутентификации (необязательно)", "Example: username:password": "Example: username:password", "Important:": "Важно:", "sd_auto_auth_warning_1": " запускайте Stable Diffusion с флагом", "sd_auto_auth_warning_2": "! Адрес SD должен быть доступен с сервера SillyTavern.", "sd_drawthings_url": "Example: {{drawthings_url}}", "sd_drawthings_auth_txt": "run DrawThings app with HTTP API switch enabled in the UI! The server must be accessible from the SillyTavern host machine.", "sd_vlad_url": "Example: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": " Адрес должен быть доступен с сервера SillyTavern.", "Allow NSFW images from Horde": "Разрешить NSFW-картинки в Horde", "Avoid spending Anlas": "Avoid spending Anlas", "Opus tier": "(Opus tier)", "View my Anlas": "View my Anlas", "sd_comfy_url": "Example: {{comfy_url}}", "Open workflow editor": "Открыть редактор воркфлоу", "Create new workflow": "Создать новый воркфлоу", "Delete workflow": "Удалить воркфлоу", "Enhance": "<PERSON><PERSON>ce", "Refine": "Refine", "Decrisper": "<PERSON><PERSON><PERSON>", "Resolution": "Целевое разрешение", "Model": "Модель", "Sampling method": "Метод сэмплинга", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (not all samplers supported)", "SMEA versions of samplers are modified to perform better at high resolution.": "SMEA versions of samplers are modified to perform better at high resolution.", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.", "DYN": "DYN", "Scheduler": "Scheduler", "Upscaler": "Upscaler", "Upscale by": "Множитель апскейлинга", "Denoising strength": "Denoising strength", "Hires steps (2nd pass)": "Кол-во шаг<PERSON> Hires (на втором проходе)", "Preset for prompt prefix and negative prompt": "Пресет для префиксов промпта и для негативных промптов", "Style": "Стиль", "Save style": "Сохранить стиль", "Delete style": "Удалить стиль", "Common prompt prefix": "Фиксированный префикс для промптов", "sd_prompt_prefix_placeholder": "Use {prompt} to specify where the generated prompt will be inserted", "Character-specific prompt prefix": "Character-specific prompt prefix", "Won't be used in groups.": "Won't be used in groups.", "sd_character_prompt_placeholder": "Any characteristics that describe the currently selected character. Will be added after a common prompt prefix.\nExample: female, green eyes, brown hair, pink shirt", "Character-specific negative prompt prefix": "Character-specific negative prompt prefix", "sd_character_negative_prompt_placeholder": "Any characteristics that should not appear for the selected character. Will be added after a negative common prompt prefix.\nExample: jewellery, shoes, glasses", "Shareable": "Shareable", "Image Prompt Templates": "Image Prompt Templates", "ext_translate_title": "Chat Translation", "Vectors Model Warning": "It is recommended to purge vectors when changing the model mid-chat. Otherwise, it will lead to sub-par results.", "Translate files into English before processing": "Translate files into English before processing", "Manager Users": "Manage Users", "New User": "New User", "Status:": "Status:", "Display Name:": "Отображаемое имя:", "User Handle:": "Хэндл пользователя:", "Confirm Password:": "Подтвердждение пароля:", "This will create a new subfolder...": "В папке /data/ будет создана новая подпапка, названная согласно хэндлу пользователя.", "Current Password:": "Текущий пароль:", "New Password:": "Новый пароль:", "Confirm New Password:": "Подтвердите новый пароль:", "Debug Warning": "Functions in this category are for advanced users only. Don't click anything if you're not sure about the consequences.", "Execute": "Execute", "Are you sure you want to delete this user?": "Вы точно хотите удалить этого пользователя?", "Deleting:": "Удаляется:", "Also wipe user data.": "Также стереть данные пользователя.", "Warning:": "Предупреждение:", "This action is irreversible.": "Данное действие невозможно отменить.", "Type the user's handle below to confirm:": "Для подтверждения введите хэндл пользователя:", "help_hotkeys_9": "Enter", "help_hotkeys_11": "Ctrl+Enter", "help_hotkeys_13": "Alt+Enter", "help_hotkeys_15": "Escape", "Import Characters": "Импорт персонажей", "char_import_8": "Персонаж с RisuRealm (прямая ссылка)", "Supports importing multiple characters.": "Можно импортировать несколько персонажей за раз.", "Write each URL or ID into a new line.": "Запишите URL-адреса / идентификаторы на отдельных строчках.", "System-wide Replacement Macros (in order of evaluation):": "System-wide Replacement Macros (in order of evaluation):", "help_macros_1": "only for slash command batching. Replaced with the returned result of the previous command.", "help_macros_2": "just inserts a newline.", "help_macros_3": "trims newlines surrounding this macro.", "help_macros_4": "no operation, just an empty string.", "help_macros_5": "global prompts defined in API settings. Only valid in Advanced Definitions prompt overrides.", "help_macros_6": "the user input", "help_macros_7": "the Character's Main Prompt override", "help_macros_8": "the Character's Jailbreak Prompt override", "help_macros_9": "the Character's Description", "help_macros_10": "the Character's Personality", "help_macros_11": "the Character's <PERSON><PERSON><PERSON>", "help_macros_12": "your current Persona Description", "help_macros_13": "the Character's Dialogue Examples", "help_macros_14": "unformatted Dialogue Examples", "help_macros_15": "your current Persona username", "help_macros_16": "the Character's name", "help_macros_17": "the Character's version number", "help_macros_18": "a comma-separated list of group member names or the character name in solo chats. Alias: {{charIfNotGroup}}", "help_macros_19": "a text generation model name for the currently selected API.", "help_macros_20": "the text of the latest chat message.", "help_macros_21": "index # of the latest chat message. Useful for slash command batching.", "help_macros_22": "the ID of the first message included in the context. Requires generation to be ran at least once in the current session.", "help_macros_23": "the 1-based ID of the current swipe in the last chat message. Empty string if the last message is user or prompt-hidden.", "help_macros_24": "the number of swipes in the last chat message. Empty string if the last message is user or prompt-hidden.", "help_macros_25": "you can leave a note here, and the macro will be replaced with blank content. Not visible for the AI.", "help_macros_26": "the current time", "help_macros_27": "the current date", "help_macros_28": "the current weekday", "help_macros_29": "the current ISO time (24-hour clock)", "help_macros_30": "the current ISO date (YYYY-MM-DD)", "help_macros_31": "the current date/time in the specified format, e. g. for German date/time:", "help_macros_32": "the current time in the specified UTC time zone offset, e.g. UTC-4 or UTC+2", "help_macros_33": "the time difference between time1 and time2. Accepts time and date macros. (Ex: {{timeDiff::{{isodate}} {{time}}::2024/5/11 12:30:00}})", "help_macros_34": "the time since the last user message was sent", "help_macros_35": "sets a behavioral bias for the AI until the next user input. Quotes around the text are important.", "help_macros_36": "rolls a dice. (ex:", "help_macros_37": "returns a random item from the list. (ex:", "help_macros_38": "alternative syntax for random that allows to use commas in the list items.", "help_macros_39": "picks a random item from the list. Works the same as {{random}}, with the same possible syntax options, but the pick will stay consistent for this chat once picked and won't be re-rolled on consecutive messages and prompt processing.", "help_macros_40": "dynamically add text in the quotes to banned words sequences, if Text Generation WebUI backend used. Do nothing for others backends. Can be used anywhere (Character description, WI, AN, etc.) Quotes around the text are important.", "Instruct Mode and Context Template Macros:": "Instruct Mode and Context Template Macros:", "(enabled in the Advanced Formatting settings)": "(enabled in the Advanced Formatting settings)", "help_macros_41": "max allowed prompt length in tokens = (context size - response length)", "help_macros_42": "context template example dialogues separator", "help_macros_43": "context template chat start line", "help_macros_44": "main system prompt (either character prompt override if chosen, or instructSystemPrompt)", "help_macros_45": "instruct system prompt", "help_macros_46": "instruct system prompt prefix sequence", "help_macros_47": "instruct system prompt suffix sequence", "help_macros_48": "instruct user prefix sequence", "help_macros_49": "instruct user suffix sequence", "help_macros_50": "instruct assistant prefix sequence", "help_macros_51": "instruct assistant suffix sequence", "help_macros_52": "instruct assistant first output sequence", "help_macros_53": "instruct assistant last output sequence", "help_macros_54": "instruct system message prefix sequence", "help_macros_55": "instruct system message suffix sequence", "help_macros_56": "instruct system instruction prefix", "help_macros_57": "instruct first user message filler", "help_macros_58": "instruct stop sequence", "Chat variables Macros:": "Chat variables Macros:", "Local variables = unique to the current chat": "Local variables = unique to the current chat", "Global variables = works in any chat for any character": "Global variables = works in any chat for any character", "Scoped variables = works in STscript": "Scoped variables = works in STscript", "help_macros_59": "replaced with the value of the local variable \"name\"", "help_macros_60": "replaced with empty string, sets the local variable \"name\" to \"value\"", "help_macros_61": "replaced with empty strings, adds a numeric value of \"increment\" to the local variable \"name\"", "help_macros_62": "replaced with the result of the increment of value of the variable \"name\" by 1", "help_macros_63": "replaced with the result of the decrement of value of the variable \"name\" by 1", "help_macros_64": "replaced with the value of the global variable \"name\"", "help_macros_65": "replaced with empty string, sets the global variable \"name\" to \"value\"", "help_macros_66": "replaced with empty string, adds a numeric value of \"increment\" to the global variable \"name\"", "help_macros_67": "replaced with the result of the increment of value of the global variable \"name\" by 1", "help_macros_68": "replaced with the result of the decrement of value of the global variable \"name\" by 1", "help_macros_69": "replaced with the value of the scoped variable \"name\"", "help_macros_70": "replaced with the value of item at index (for arrays / lists or objects / dictionaries) of the scoped variable \"name\"", "Export for character": "Export for character", "Export prompts for this character, including their order.": "Export prompts for this character, including their order.", "Are you sure you want to reset your settings to factory defaults?": "Вы точно хотите сбросить настройки до заводских?", "Don't forget to save a snapshot of your settings before proceeding.": "Настоятельно советуем перед этим сохранить снимок текущих настроек.", "Change Password": "Сменить пароль", "Reset Code:": "Reset Code:", "Click _space": "Нажмите ", "Alternate Greeting #": "Вариант #", "Defines on importing cards which action should be chosen for importing its listed tags. 'Ask' will always display the dialog.": "Выберите, какие действия следует предпринять по отношению к тегам импортируемой карточки. При выборе опции \"Спрашивать\" вы будете решать это индивидуально для каждой карточки.", "Ask": "Спрашивать", "tag_import_all": "Все", "tag_import_existing": "Только существующие", "tag_import_none": "Не импортировать", "Using a proxy that you're not running yourself is a risk to your data privacy.": "Помните, что используя чужую прокси, вы подвергаете риску конфиденциальность своих данных.", "ANY support requests will be REFUSED if you are using a proxy.": "НЕ РАССЧИТЫВАЙТЕ на нашу поддержку, если используете прокси.", "Do not proceed if you do not agree to this!": "Не продолжайте, если не согласны с этими условиями!", "Relative (to other prompts in prompt manager) or In-chat @ Depth.": "Относительно других промтов в менеджере, либо на опред. глубину в чате.", "prompt_manager_in_chat": "На глубине в чате", "Load a custom asset list or select": "Загрузите набор внешних ресурсов или выберите", "Install Extension": "Установить расширение", "to install 3rd party extensions.": ", чтобы установить стороннее расширение.", "Load an asset list": "Загрузить набор ресурсов", "load_asset_list_desc": "Загрузить набор ресурсов и/или расширений из определённого списка.\n\nДефолтный URL содержит описание набора стандартных ресурсов, идущих в комплекте.\nЕсли хотите скачать ресурсы из стороннего набора, вставьте в это поле свой URL.\n\nЧтобы установить одиночное расширение от стороннего разработчика, воспользуйтесь кнопкой \"Установить расширение\" в левом верхнем углу.", "Show group chat queue": "Показывать очерёдность в групповых чатах", "In group chat, highlight the character(s) that are currently queued to generate responses and the order in which they will respond.": "Подсвечивать персонажей, которые скоро будут генерировать ответ в групповом чате, а также порядок, в котором они будут это делать", "ext_regex_user_input_desc": "Отправленные вами сообщения.", "ext_regex_ai_output_desc": "Сообщения, полученные от API", "ext_regex_sts_desc": "Сообщения, отправленные с помощью команд STscript", "ext_regex_wi_desc": "Содержимое лорбуков/информации о мире. Работает только со включенной галочкой \"Только промпт\"!", "ext_regex_only_format_display_desc": "История чата не изменится, замена будет осуществляться только в отображаемом сообщении (в UI)", "Prefer Character Card Instructions": "Приоритет инструкциям из карточек", "If checked and the character card contains a Post-History Instructions override, use that instead": "Если в карточке персонажа имеется собственная инструкция после истории, в промпт попадёт именно она", "Auto-select Input Text": "Автовыделение вводимого текста", "Enable auto-select of input text in some text fields when clicking/selecting them. Applies to popup input textboxes, and possible other custom input fields.": "Автоматически выделять вводимый текст в некоторых текстовых полях при клике/выборе. Работает для вспл. окон и различных кастомных полей ввода.", "mui_reset": "Сброс", "Quick 'Impersonate' button": "Быстрое перевоплощение", "Show a button in the input area to ask the AI to impersonate your character for a single message": "Показать в поле ввода кнопку, по нажатии на которую ИИ сгенерирует одно сообщение от лица вашего персонажа.", "Separators as Stop Strings": "Разделители в качестве стоп-строк", "Names as Stop Strings": "Имена в качестве стоп-строк", "Add Character and User names to a list of stopping strings.": "Добавлять имена персонажа и пользователя в список стоп-строк.", "context_allow_post_history_instructions": "Добавлять в конец промпта инструкции после истории. Работает только при наличии таких инструкций в карточке И при включенной опции ''Приоритет инструкциям из карточек''.\nНЕ РЕКОМЕНДУЕТСЯ ДЛЯ МОДЕЛЕЙ TEXT COMPLETION, МОЖЕТ ПОРТИТЬ ВЫХОДНОЙ ТЕКСТ.", "First User Prefix": "Первый префикс пользователя", "Inserted before the first User's message.": "Вставляется перед первым сообщением пользователя.", "Last User Prefix": "Последний префикс пользователя", "instruct_last_input_sequence": "Вставляется перед последним сообщением пользователя.", "Load Asset List": "Загрузить список ресурсов", "Never add character names.": "Не вставлять имя персонажа.", "Don't add character names unless necessary.": "Вставлять имя персонажа только когда это необходимо.", "character_names_none": "Не добавлять имена персонажей в качестве префикса. Может повредить качеству ответов в групповых чатах, используйте с осторожностью.", "Auxiliary": "Вспомогательный", "Post-History Instructions": "Инструкции после истории", "Current persona updated": "Текущая персона изменена", "Your messages will now be sent as ${0}": "Ваши сообщения будут отправляться от лица ${0}", "Copied!": "Скопировано!", "Are you sure you want to delete this message?": "Вы точно хотите удалить это сообщение?", "Delete Message": "Удалить сообщение", "Delete Swipe": "Удалить свайп", "Could not get a reply from API. Check your connection settings / API key and try again.": "Не удалось получить ответ от API. Проверьте настройки соединения и API-ключ и повторите попытку.", "Connecting To Proxy": "Подключиться к прокси", "Are you sure you want to connect to the following proxy URL?": "Вы точно хотите соединиться с прокси по этому адресу?", "API connection successful!": "Соединение с API установлено!", "Proxy Saved": "Прокси сохранена", "Proxy Deleted": "Прокси удалена", "Could not find proxy with name '${0}'": "Не удалось найти прокси с названием '${0}'", "Proxy preset '${0}' not found in proxies array.": "Пресет с названием '${0}' не найден в списке прокси.", "Please wait for the previous request to complete.": "Пожалуйста, дождитесь окончания обработки предыдущего запроса.", "Start new chat?": "Начать новый чат?", "If necessary, you can later restore this chat file from the /backups folder": "При необходимости этот чат можно будет восстановить из папки /backups", "Also delete the current chat file": "Также удалить текущий чат", "chat_rename_1": "Введите новое имя чата:", "chat_rename_2": "!!Не используйте имя уже существующего файла, это приведёт к ошибке!!", "chat_rename_3": "Будут разрушены связи между чатами-чекпоинтами.", "chat_rename_4": "Расширение '.jsonl' дописывать не нужно.", "If you're connected to an API, try asking me something!": "Есть соединение с API? Напишите мне что-нибудь!", "Connection Profile": "Профиль соединения", "View connection profile details": "Посмотреть параметры профиля соединения", "Create a new connection profile": "Создать новый профиль соединения", "Update a connection profile": "Обновить профиль соединения", "Rename a connection profile": "Переименовать профиль соединения", "Reload a connection profile": "Перезагрузить профиль соединения", "Delete a connection profile": "Удалить профиль соединения", "No profile selected": "Профиль не выбран", "Creating a Connection Profile": "Создать профиль соединения", "Settings Preset": "Пресет настроек", "Proxy Preset": "Пресет для прокси", "Enter a name:": "Введите название:", "Are you sure you want to delete the selected profile?": "Вы точно хотите удалить выбранный профиль?", "instruct_enabled": "Вкл/выкл Instruct-режим", "Instruct Template": "Шаб<PERSON>он Instruct-режима", "instruct_template_activation_regex_desc": "Автоматически активировать этот шаблон в момент подключения к API или выбора модели, если название модели соответствует этому рег. выражению.", "Master Import": "Глоб. импорт", "Import Advanced Formatting settings": "Импорт настроек Расширенного форматирования\nТакже принимает файлы старого формата с Шаблонами контекста и Шаблонами Instruct-режима.", "Master Export": "Глоб. экспорт", "Export Advanced Formatting settings": "Экспорт настроек Расширенного форматирования", "Select your current System Prompt": "Выберите текущий системный промпт", "Prompt Content": "Текст промпта", "Update current prompt": "Сохранить промпт", "Save prompt as": "Сохранить как...", "Import template": "Импорт шаблона", "Export template": "Экспорт шаблона", "Restore current prompt": "Восстановить промпт", "comma delimited,no spaces between": "через запятую,без пробелов в промежутках", "User Message Sequences": "Строки для сообщений пользователя", "Assistant Message Sequences": "Строки для сообщений ассистента", "System Message Sequences": "Строки для сообщений системы", "System Prompt Sequences": "Строки для системного промпта", "sysprompt_enabled": "Вкл/выкл системный промпт", "Are you sure you want to delete this alternate greeting?": "Вы точно хотите удалить этот вариант?", "Any contents here will replace the default Post-History Instructions used for this character. (v2 spec: post_history_instructions)": "Содержимое этого поля заменит стандартные Инструкции после истории, применяемые для этого персонажа. (v2 spec: post_history_instructions)", "None (disabled)": "Нигде (откл.)", "Markdown Hotkeys": "Горячие клавиши для разметки", "markdown_hotkeys_desc": "Включить горячие клавиши для вставки символов разметки в некоторых полях ввода. См. '/help hotkeys'.", "Save and Update": "Сохранить и обновить", "Profile name:": "Название профиля:", "API returned an error": "API ответило ошибкой", "Failed to save preset": "Не удалось сохранить пресет", "Preset name should be unique.": "Название пресета должно быть уникальным.", "Invalid file": "Невалидный файл", "No preset selected": "Пресет не выбран", "Invalid logit bias preset file.": "Файл пресета невалиден.", "Preset was not deleted from server": "Пресет не удалён с сервера", "Preset deleted": "Пресет удалён", "Delete the preset?": "Удалить пресет?", "Preset updated": "Пресет сохранён", "Entered reverse proxy address is not a valid URL": "Введённый адрес прокси невалиден", "An error occurred while counting tokens: Token budget exceeded.": "Ошибка при подсчёте токенов: Превышен бюджет токенов", "An error occurred while counting tokens: Invalid character name": "Ошибка при подсчёте токенов: Невалидное имя персонажа", "Not enough free tokens for mandatory prompts. Raise your token Limit or disable custom prompts.": "Недостаточно токенов для всех выбранных промптов. Повысьте лимит токенов или отключите часть промптов.", "The name of at least one character contained whitespaces or special characters. Please check your user and character name.": "В имени одного из персонажей содержится пробел или иной спецсимвол. Проверьте имена пользователя и персонажа.", "An unknown error occurred while counting tokens. Further information may be available in console.": "Неизвестная ошибка при подсчёте токенов. Проверьте консоль, возможно, подробная информация есть там.", "Encountered an error while processing your request.": "При обработке вашего запроса возникла ошибка.", "Check you have credits available on your": "Убедитесь, что на вашем", "OpenAI account quora_error": "аккаунте OpenAI", "dot quota_error": "имеется достаточно кредитов.", "If you have sufficient credits, please try again later.": "Если кредитов достаточно, то повторите попытку позднее.", "Proxy preset '${0}' not found": "Пресет '${0}' не найден", "Update or remove your reverse proxy settings.": "Измените или удалите ваши настройки прокси.", "An error occurred while importing prompts. More info available in console.": "В процессе импорта произошла ошибка. Подробную информацию см. в консоли.", "Could not import prompts. Export failed validation.": "Не удалось импортировать промпты. Не пройдена валидация при экспорте.", "Prompt import complete.": "Импорт заве<PERSON><PERSON><PERSON>н.", "Are you sure you want to delete this prompt?": "Вы точно хотите удалить этот промпт?", "Existing prompts with the same ID will be overridden. Do you want to proceed?": "Имеющиеся промпты с совпадающими идентификаторами будут перезаписаны. Продолжить?", "This will reset the prompt order for this character. You will not lose any prompts.": "Будет сброшен порядок промптов для этого персонажа. Сами промпты вы не потеряете.", "Note:": "Примечание:", "this chat is temporary and will be deleted as soon as you leave it.": "это временный чат, он будет удалён, как только вы из него выйдете.", "help_hotkeys_21": "Работают в окне ввода чата, а также в полях, отмеченных этим значком:", "help_hotkeys_22": "**полужирный**", "help_hotkeys_23": "*курсив*", "help_hotkeys_24": "__подчёркивание__", "help_hotkeys_25": "`inline-код`", "help_hotkeys_26": "~~за<PERSON><PERSON><PERSON><PERSON>нутый~~", "ext_regex_only_format_visual_desc": "Содержимое файла с историей чата останется нетронутым, изменения будут лишь визуальными (в UI).", "Could not convert file": "Не удалось сконвертировать файл", "Could not upload file": "Не удалось загрузить файл", "Could not download file": "Не удалось скачать файл", "File is too big. Maximum size is ${0}.": "Слишком большой файл. Максимальный размер: ${0}.", "Binary files are not supported. Select a text file or image.": "Бинарные файлы не поддерживаются. Выберите текстовый файл или изображение.", "No character or group selected": "Не выбрано ни одного персонажа или группы", "Could not delete file": "Не удалось удалить файл", "No attachments selected.": "Вложение не выбрано.", "No files were scraped.": "Скрапинг не выполнен.", "Scraped ${0} files from ${1} to ${2}.": "Соскраплено из ${0} файлов из ${1} в ${2}.", "Check browser console for details.": "Подробности см. в консоли браузера.", "Scraping failed": "Ошибка скрапинга", "External media has been blocked": "Внешние медиа отключены", "Use the 'Ext. Media' button to allow it. Click on this message to dismiss.": "Разрешить можно с помощью кнопки 'Внешн. медиа'. Нажмите на это сообщение, чтобы его скрыть.", "Couldn't get CSRF token. Please refresh the page.": "Не удалось получить CSRF токен. Попробуйте перезагрузить страницу.", "Error": "Ошибка", "API Error": "Ошибка API", "Please wait until the chat is saved before switching characters.": "Пожалуйста, дождитесь сохранения чата, прежде чем переключать персонажа.", "Your chat is still saving...": "Чат всё ещё сохраняется...", "Character ${0} not found in the list": "Персонаж ${0} не найден в списке", "Streaming is enabled, but the version of Kobold used does not support token streaming.": "Вклю<PERSON>ён стриминг текста, но ваша версия Kobold не поддерживает стриминг токенов.", "Verify that the server is running and accessible.": "Убедитесь, что сервер запущен и доступен по сети.", "ST Server cannot be reached": "Не удалось соединиться с сервером ST", "You must first select a character to duplicate!": "Вы не выбрали персонажа, которого хотите клонировать!", "Character Duplicated": "Персо<PERSON><PERSON> склонирован", "No character name provided.": "Вы не ввели имя персонажа.", "Rename Character": "Переименование", "No character selected.": "Не выбран персонаж.", "New name:": "Новое имя:", "Same character name provided, so name did not change.": "Введено то же самое имя, ничего не изменилось.", "Character renamed and past chats updated!": "Персонаж переименован, а чаты обновлены!", "Character renamed!": "Персонаж переименован!", "Something went wrong. The page will be reloaded.": "Что-то пошло не так. Страница будет перезагружена.", "Past chat could not be updated: ${0}": "Не удалось обновить чат ${0}", "Trying to save group chat with regular saveChat function. Aborting to prevent corruption.": "Произошла попытка сохранения группового чата функцией saveChat. Откатываем изменения, чтобы предотвратить потерю данных.", "Check the server connection and reload the page to prevent data loss.": "Проверьте связь с сервером и перезагрузите страницу, чтобы избежать потери данных.", "Chat could not be saved": "Не удалось сохранить чат", "Settings could not be loaded after multiple attempts. Please try again later.": "Не удалось загрузить настройки за несколько попыток. Попробуйте позднее.", "Settings could not be saved": "Не удалось сохранить настройки", "Could not load chat data. Try reloading the page.": "Не удалось загрузить чат. Попробуйте обновить страницу.", "Invalid process (no 'type')": "Невалидный процесс (нет параметра 'type')", "Character Deleted: ${0}": "Персонаж удалён: ${0}", "Character Created: ${0}": "Персонаж создан: ${0}", "Group Created": "Группа создана", "Group Deleted": "Группа удалена", "Character Imported: ${0}": "Персонаж импортирован: ${0}", "Invalid swipe ID: ${0}": "Некорректный идентификатор свайпа: ${0}", "No messages to delete swipes from.": "Сообщение, из которого требуется удалить свайп, не найдено.", "Can't delete the last swipe.": "Невозможно удалить единственный свайп.", "GUI Settings preset is not supported for Horde. Please select another preset.": "Для Horde не поддерживаются пресеты настроек GUI. Пожалуйста, выберите другой пресет.", "Embedded lorebook will be removed from this character.": "Встроенный лорбук будет удалён из персонажа.", "Name is required": "Введите имя", "Cannot create characters while generating. Stop the request and try again.": "Во время генерации ответа создать персонажа невозможно. Остановите запрос и повторите попытку.", "Creation aborted": "Процесс создания прерван", "Failed to create character": "Не удалось создать персонажа", "Something went wrong while saving the character, or the image file provided was in an invalid format. Double check that the image is not a webp.": "Что-то пошло не так в процессе сохранения персонажа, либо вы загрузили файл некорректного формата. Проверьте, что изображение точно не в формате webp.", "Context template '${0}' not found": "Ша<PERSON><PERSON><PERSON>н контекста '${0}' не найден", "Instruct template '${0}' not found": "Шаблон Instruct-режима '${0}' не найден", "Error: ${0} is not a valid API": "Ошибка: ${0} не является валидным API", "API set to ${0}, trying to connect..": "Установлено API ${0}, пробуем подключиться...", "Unsupported file type: ": "Неподдерживаемый тип файла: ", "Cannot import characters while generating. Stop the request and try again.": "Во время генерации ответа импорт персонажа невозможен. Остановите запрос и повторите попытку.", "Import aborted": "Процесс импорта прерван", "The file is likely invalid or corrupted.": "Вероятно, файл невалиден или повреждён.", "Could not import character": "Не удалось импортировать персонажа", "Cannot run /impersonate command while the reply is being generated.": "Во время генерации ответа выполнить /impersonate невозможно.", "Name must be provided as an argument to rename this chat.": "Для переименования чата необходимо предоставить новое имя в качестве аргумента.", "No chat selected that can be renamed.": "Чат, который требуется переименовать, не найден.", "Successfully renamed chat to: ${0}": "Чат успешно переименован в: ${0}", "Character ${0} not found. Skipping deletion.": "Персонаж ${0} не найден. Удаление пропускается.", "Failed to delete character": "При удалении персонажа произошло ошибка", "Are you sure you want to duplicate this character?": "Вы точно хотите клонировать этого персонажа?", "If you just want to start a new chat with the same character...": "Если вы хотите просто создать новый чат, воспользуйтесь кнопкой \"Начать новый чат\" в меню слева внизу.", "THIS IS PERMANENT!": "ОТМЕНИТЬ БУДЕТ НЕВОЗМОЖНО!", "Also delete the chat files": "Также удалить файлы чатов", "Delete the character?": "Удалить персонажа?", "Not a valid number": "Некорректное число", "Author's Note depth updated": "Глубина авторских заметок обновлена", "Author's Note frequency updated": "Частота авторских заметок обновлена", "Not a valid position": "Некорректная позиция", "Author's Note position updated": "Позиция авторских заметок обновлена", "Something went wrong. Could not save character's author's note.": "Что-то пошло не так. Не удалось сохранить авторские заметки для этого персонажа.", "Select a character before trying to use Author's Note": "Сначала необходимо выбрать персонажа", "Author's Note text updated": "Текст авторских заметок обновлён", "Group Validation": "Валидация группы", "Warning: Listed member ${0} does not exist as a character. It will be removed from the group.": "Предупреждение: персонаж ${0} не существует в виде карточки. Он будет удалён из группы.", "Group Chat could not be saved": "Не удалось сохранить групповой чат", "Deleted group member swiped. To get a reply, add them back to the group.": "Вы пытаетесь свайпнуть удалённого члена группы. Чтобы получить ответ, добавьте этого персонажа обратно в группу.", "Currently no group selected.": "В данный момент не выбрано ни одной группы.", "Not so fast! Wait for the characters to stop typing before deleting the group.": "Чуть помедленнее! Перед удалением группы дождитесь, пока персонажи закончат печатать.", "Delete the group?": "Удалить группу?", "This will also delete all your chats with that group. If you want to delete a single conversation, select a \"View past chats\" option in the lower left menu.": "Вместе с ней будут удалены и все её чаты. Если требуется удалить только один чат, воспользуйтесь кнопкой \"Все чаты\" в меню в левом нижнем углу.", "Can't peek a character while group reply is being generated": "Невозможно открыть карточку персонажа во время генерации ответа", "Threshold": "Порог", "Invalid data provided for master import": "Глоб. импорт не может быть выполнен по причине невалидности данных", "Importing instruct template...": "Импортируем...", "Instruct template detected": "Обнаружен шаблон Instruct-режима", "Importing as context template...": "Импортируем...", "Context template detected": "Обнаружен шаблон контекста", "Importing as system prompt...": "Импортируем...", "System prompt detected": "Обнаружен системный промпт", "Importing as settings preset...": "Импортируем...", "Text Completion settings detected": "Обнаружены настройки Text Completion", "No valid sections found in imported data": "Не найдено ни одной валидной секции данных", "No sections selected for import": "Не выбрано ни одной секции для импорта", "Import": "Импортировать", "Imported ${0} settings: ${1}": "Импортировано ${0} настроек: ${1}", "Export": "Экспортировать", "No sections selected for export": "Не выбрано ни одной секции для экспорта", "Cannot update GUI preset": "Пресет для GUI обновить невозможно", "Template updated": "Шаб<PERSON>он сохранён", "Hint: Use a character/group name to bind preset to a specific chat.": "Совет: введите имя персонажа/группы, чтобы привязать пресет к определённым чатам.", "Preset name:": "Название пресета:", "Template name:": "Название шаблона:", "Preset saved": "Пресет сохранён", "Template saved": "Шаб<PERSON>он сохранён", "Preset could not be saved": "Не удалось сохранить пресет", "Preset could not be renamed": "Не удалось обновить пресет", "Preset renamed": "Пресет переименован", "Template renamed": "Шаблон переименован", "Cannot delete GUI preset": "Пресет для GUI удалить невозможно", "Failed to restore default preset": "Не удалось восстановить пресет по умолчанию", "Failed to restore default template": "Не удалось восстановить шаблон по умолчанию", "Rename preset": "Переименовать пресет", "Rename template": "Переименовать шаблон", "Enter a new name:": "Введите новое название:", "Preset imported": "Пресет импортирован", "Template imported": "Шаблон импортирован", "Delete this preset?": "Удалить этот пресет?", "Delete this template?": "Удалить этот шаблон?", "This action is irreversible and your current settings will be overwritten.": "Отменить это действие невозможно. Ваши текущие настройки будут перезаписаны.", "Template deleted": "<PERSON>а<PERSON><PERSON><PERSON>н удалён", "Template was not deleted from server": "Шаблон не удалён с сервера", "Cannot restore GUI preset": "Пресет для Gui восстановить нельзя", "Default preset cannot be restored": "Невозможно восстановить пресет по умолчанию", "Default template cannot be restored": "Невозможно восстановить шаблон по умолчанию", "Resetting a <b>default preset</b> will restore the default settings.": "Сброс <b>комплектного пресета</b> восстановит настройки по умолчанию.", "Resetting a <b>default template</b> will restore the default settings.": "Сброс <b>комплектного шаблона</b> восстановит настройки по умолчанию.", "Are you sure?": "Вы уверены?", "Default preset restored": "Стандартный пресет восстановлен", "Default template restored": "Стандартный шаблон восстановлен", "Resetting a <b>custom preset</b> will restore to the last saved state.": "Сброс <b>пользовательского пресета</b> откатит его к последнему сохранённому состоянию.", "Resetting a <b>custom template</b> will restore to the last saved state.": "Сброс <b>пользовательского шаблона</b> откатит его к последнему сохранённому состоянию.", "Preset restored": "Пресет восстановлен", "Template restored": "Шаблон восстановлен", "Update current template": "Сохранить шаблон", "Rename current template": "Переименовать шаблон", "Save template as": "Сохранить как...", "Restore current template": "Восстановить шаблон", "Delete the template": "Удалить шаблон", "Rename current prompt": "Переименовать промпт", "Select your current Context Template": "Выберите активный шаблон контекста", "Select your current Instruct Template": "Выберите активный шаблон Instruct-режима", "and connect to an": "и подключитесь к", "You can add more": "Можете добавить больше", "from other websites": "с других сайтов.", "Go to the": "Заходите в", "to install additional features.": ", чтобы установить разные дополнительные ресурсы.", "or_welcome": "; также доступен", "Claude API Key": "Ключ от API Claude", "Block Entropy API Key": "Ключ от API Block Entropy", "Select a Model": "Выберите модель", "The content of this prompt is pulled from elsewhere and cannot be edited here.": "Эта часть промпта подтягивается откуда-то извне. Отредактировать её здесь невозможно.", "close": "закрыть", "Your preset contains proxy and/or custom endpoint settings.": "Ваш пресет содержит настройки прокси и/или настройки кастомного эндпоинта.", "Do you want to remove these fields before exporting?": "Желаете ли удалить эти поля перед экспортом?", "world_button_title": "Лор персонажа\n\nНажмите, чтобы загрузить\nShift + ЛКМ, чтобы открыть диалог привязки мира", "No auxillary Lorebooks set. Click here to select.": "Вспомогательный лорбук не выбран. Нажмите, чтобы выбрать.", "ext_regex_ai_input_desc": "Полученные от API ответы.", "ext_regex_slash_desc": "Сообщения, отправленные с помощью команд STscript.", "ext_regex_run_on_edit_desc": "Выполнять скрипт при редактировании принадлежащих обозначенной роли (ролям) сообщений.", "Convert to group chat": "Сделать чат групповым", "Are you sure you want to convert this chat to a group chat?": "Вы точно хотите сделать этот чат групповым?", "This cannot be reverted.": "Отменить будет невозможно.", "Enter a name for this persona:": "Введите имя для этой персоны:", "Cancel if you're just uploading an avatar.": "Отмените, если просто загружаете аватарку.", "Set the crop position of the avatar image": "Обозначьте зону, по которой будет обрезана аватарка", "popup-button-crop": "Обрезать", "Duplicate persona": "Клонировать персону", "Are you sure you want to duplicate this persona?": "Вы точно хотите клонировать эту персону?", "Enter a description for this persona:": "Введите описание для этой персоны:", "You can always add or change it later.": "Его можно будет добавить или изменить в любое время.", "You can now pick ${0} as a persona in the Persona Management menu.": "Теперь ${0} может быть выбран(а) в качестве персоны в меню \"Управление персоной\".", "Persona Created": "Персона создана", "Overwrite Existing Persona": "Перезаписать существующую персону", "This character exists as a persona already. Do you want to overwrite it?": "Этот персонаж уже существует в виде персоны. Желаете перезаписать?", "Persona Description Macros": "Макросы в описании персоны", "This character has a description that uses <code>{{char}}</code> or <code>{{user}}</code> macros. Do you want to swap them in the persona description?": "В описании персонажа присутствуют макросы <code>{{char}}</code> и/или <code>{{user}}</code>. Желаете поменять их местами?", "(If empty name is provided, this will unbind the name from this avatar)": "(если оставить пустым, то имя будет отвязано от этой аватарки)", "To permanently set \"${0}\" as the selected persona, unlock and relock it using the \"Lock\" button. Otherwise, the selection resets upon reloading the chat.": "Чтобы перманентно привязать персону \"${0}\" к этому чату, открепите текущую персону и затем закрепите новую кнопкой \"Закрепить\". Иначе при следующем заходе персона будет сброшена.", "This chat is locked to a different persona (${0}).": "К этому чату уже привязана другая персона (${0}).", "User persona is now unlocked for this chat. Click the \"Lock\" again to revert.": "Персона откреплена. Нажмите \"Закрепить\", чтобы снова зафиксировать её в этом чате.", "Persona unlocked": "Персона откреплена", "Creating a new persona for currently selected user name and avatar...": "Создаём новую персону для этого имени и аватарки...", "Persona not set for this avatar": "К этой аватарке не привязано ни одной персоны", "User persona is locked to ${0} in this chat": "${0} установлен(а) в качестве фиксированной персоны в этом чате", "You cannot delete the avatar you are currently using": "Невозможно удалить аватарку, которую вы используете прямо сейчас", "Warning": "Внимание", "Are you sure you want to delete this avatar?": "Вы точно хотите удалить эту аватарку?", "All information associated with its linked persona will be lost.": "Вся информация о связанной с ней персоне будет утеряна.", "The default persona was deleted. You will need to set a new default persona.": "Удалена персона по умолчанию. Вам будет необходимо выбрать новую вместо неё.", "Default persona deleted": "Удалена персона по умолчанию", "The locked persona was deleted. You will need to set a new persona for this chat.": "Удалена привязанная к чату персона. Вам будет необходимо выбрать новую фиксированную персону для этого чата.", "Persona deleted": "Персона удалена", "You must bind a name to this persona before you can set it as the default.": "Прежде чем установить эту персону в качестве персоны по умолчанию, ей необходимо присвоить имя.", "Persona name not set": "У персоны отсутствует имя", "Are you sure you want to remove the default persona?": "Вы точно хотите снять статус персоны по умолчанию?", "This persona will no longer be used by default when you open a new chat.": "Эта персона больше не будет автоматически выбираться при старте нового чата", "Default persona removed": "Персона по умолчанию снята", "Are you sure you want to set \"${0}\" as the default persona?": "Вы точно хотите установить \"${0}\" в качестве персоны по умолчанию?", "This name and avatar will be used for all new chats, as well as existing chats where the user persona is not locked.": "Это имя и аватарка будут автоматически выбираться при старте нового чата, а также в любом существующем чате, где нет фиксированной персоны.", "This persona will be used by default when you open a new chat.": "Эта персона будет выбираться автоматически при старте нового чата.", "Default persona set to ${0}": "${0} установлен(а) в качестве персоны по умолчанию", "Invalid file selected": "Загружен невалидный файл", "Invalid file format": "Некорректный формат файла", "Personas restored with warnings. Check console for details.": "Персона восстановлена, но были сгенерированы варнинги. Подробности см. в консоли.", "Personas restored successfully.": "Персона успешно восстановлена.", "All user-sent messages in this chat will be attributed to ${0}.": "Все ваши сообщения в этом чате будут приписаны ${0}.", "forbid_media_global_state_forbidden": "(запрещены)", "forbid_media_global_state_allowed": "(разрешены)", "Tag Management": "Управление тегами", "Save your tags to a file": "Сохранить ваши теги в файл", "Restore tags from a file": "Восстановить теги из файла", "Create a new tag": "Создать новый тег", "Drag handle to reorder. Click name to rename. Click color to change display.": "Перемещайте мышкой, чтобы менять порядок. Нажмите, чтобы переименовать. Нажмите на цвет, чтобы сменить его.", "Click on the folder icon to use this tag as a folder.": "Нажмите на иконку папки, чтобы использовать тег как папку.", "Use alphabetical sorting": "Сортировать по алфавиту", "tags_sorting_desc": "Автоматически сортировать теги по алфавиту после создания или переименования одного из них.\nПри выключении новые теги будут просто добавляться в конец.\n\nЕсли вручную перетащить один из тегов на другое место, автосортировка отключится.", "Imported tags:": "Импортируемые теги:", "Importing Tags": "Импорт тегов", "Couldn't import tags:": "Не удалось импортировать теги:", "Allow fallback models": "Разрешить резервные модели", "Allow fallback providers": "Разрешить fallback-провайдеров", "To use instruct formatting, switch to OpenRouter under Text Completion API.": "Переключитесь на OpenRouter в Text Completion API, чтобы использовать форматирование Instruct-режима.", "Select providers. No selection = all providers.": "Выберите провайдера. Нет выбранного = выбраны все.", "Select a model": "Выберите модель", "Search models...": "Искать по моделям...", "[Currently loaded]": "[Загруженная сейчас]", "Search providers...": "Искать по провайдерам...", "Automatically chooses an alternative provider if chosen providers can't serve your request.": "Автоматически переключаться на другого провайдера, если текущий не может обслужить запрос.", "Example: http://127.0.0.1:8000": "Пример: http://127.0.0.1:8000", "Edit a connection profile": "Редактировать профиль соединения", "System Prompt Name": "Название системного промпта", "Use System Prompt": "Использовать системный промпт", "Hint:": "Подсказка:", "Click on the setting name to omit it from the profile.": "Нажмите на название настройки, чтобы исключить её из профиля", "Included settings:": "Сохранённые параметры:", "Server URL": "Адрес сервера", "Electron Hub API Key": "Ключ от API Electron Hub", "Electron Hub Model": "Модель Electron Hub", "NanoGPT API Key": "Ключ от API NanoGPT", "NanoGPT Model": "Модель NanoGPT", "Use extension settings": "Использовать настройки из расширения", "DeepSeek API Key": "Ключ от API DeepSeek", "DeepSeek Model": "Модель DeepSeek", "prompt_post_processing_merge": "Объединять идущие подряд сообщения с одной ролью", "prompt_post_processing_semi": "Semi-strict (чередовать роли)", "prompt_post_processing_strict": "Strict (чередовать роли, сначала пользователь)", "Select Horde models": "Выберите модель из Horde", "Model ID (optional)": "Идентификатор модели (необязательно)", "Derive context size from backend": "Использовать бэкенд для определения размера контекста", "Rename current preset": "Переименовать пресет", "No Worlds active. Click here to select.": "Активных миров нет, ЛКМ для выбора.", "Title/Memo": "Название", "Strategy": "Статус", "Position": "Позиция", "Trigger %": "% срабатываний", "Use global": "Глоб. настройка", "Whole Words": "Целые слова", "Non-recursable": "Не рекурсивная", "Delay until recursion": "Рекурсивная", "Toggle entry's active state.": "Вкл/выкл запись.", "Prioritize": "Важная", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Важная запись получает приоритет среди всех выбранных. Если важных записей несколько, выбирается та, у которой выше \"Приоритет\".", "Group Weight": "Вес в группе", "A relative likelihood of entry activation within the group": "Относительная вероятность активации записи в рамках группы", "Sticky": "Липучка", "Sticky entries will stay active for N messages after being triggered.": "Запись-\"липучка\" останется активной в течение N сообщений после срабатывания.", "Cooldown": "К<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Entries with a cooldown can't be activated N messages after being triggered.": "Запись с заданным кулдауном не активируется следующие N сообщений после срабатывания.", "Delay": "Задержка", "Entries with a delay can't be activated until there are N messages present in the chat.": "Запись с заданной задержкой может активироваться только после того, как в чате наберётся N сообщений.", "Non-sticky": "Нет", "No cooldown": "Нет", "No delay": "Нет", "Filter to Characters or Tags": "Фильтровать по персонажам или тегам", "Exclude": "Режим исключения", "Switch the Character/Tags filter around to exclude the listed characters and tags from matching for this entry": "Инвертировать логику: для выбранных в фильтре персонажей/тегов данная запись активна НЕ БУДЕТ", "Apply current sorting as Order": "Выставить приоритеты по текущей сортировке", "Create a new World Info": "Создать новый мир", "Enter a name for the new file:": "Название нового файла:", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "Если сразу несколько записей из одной группы окажутся активированными, по факту сработает только одна. Одна запись может входить в несколько групп, отделяются запятыми. Раздел в документации: World Info - Inclusion Group", "Valid World Info file name is required": "Требуется корректное имя для файла мира", "World Info file has an invalid format": "Файл мира имеет неизвестный формат", "World Info file has no entries": "В файле нет ни одной записи", "Character not found.": "Персонаж не найден", "Open a chat to get a name of the chat-bound lorebook": "Чтобы получить название привязанного к чату лорбука, требуется открыть чат.", "File is not valid: ${0}": "Файл повреждён или имеет неизвестный формат: ${0}", "The world with ${0} is invalid or corrupted.": "Мир ${0} повреждён или имеет неизвестный формат.", "Deactivated all worlds": "Все миры отключены", "No world found named: ${0}": "Не найдено мира с название ${0}", "Activated world: ${0}": "Мир ${0} включён", "Deactivated world: ${0}": "Мир ${0} отключён", "World was not active: ${0}": "Мир ${0} не включён", "The world '${0}' has been imported and linked to the character successfully.": "Мир ${0} успешно импортирован и привязан к персонажу.", "World/Lorebook imported": "Мир/лорбук импортирован", "Are you sure you want to import '${0}'?": "Вы точно хотите импортировать '${0}'?", "It will overwrite the World/Lorebook with the same name.": "Существующий мир с таким же названием будет перезаписан.", "Automatically 'continue' a response if the model stopped before reaching a certain amount of tokens.": "Автоматически \"продолжать\" ответ, если он оказался короче, чем целевая длина (в токенах).", "None (not injected)": "Никуда", "ext_sum_injection_position_none": "Пересказ не будет вставляться в промпт. Однако его по-прежнему можно будет вставить с помощью макроса {{summary}}", "ext_sum_include_wi_scan": "Учитывать при сканировании лорбуком", "ext_sum_include_wi_scan_desc": "Учитывать актуальный пересказ при сканировании промпта лорбуком.", "ext_sum_force_tip": "Отправить запрос на создание пересказа прямо сейчас.", "ext_sum_restore_tip": "Откатиться к предыдущему пересказу; используйте несколько раз, чтобы очистить историю пересказов для чата", "Built-in Extensions:": "Комплектные расширения:", "Installed Extensions:": "Установленные расширения:", "Loading third-party extensions... Please wait...": "Загрузка сторонних расширений... Пожалуйста, подождите...", "The page will be reloaded shortly...": "Страница будет перезагружена...", "Extensions state changed": "Изменено состояние расширения", "Error loading extensions. See browser console for details.": "Не удалось загрузить расширения. Подробности см. в консоли браузера.", "You don't have permission to update global extensions.": "Отсутствуют права на обновление глобальных расширений.", "Extension update failed": "Не удалось обновить расширение", "Extension ${0} updated to ${1}": "Расширение ${0} обновлено до ${1}", "Reload the page to apply updates": "Чтобы изменения вступили в силу, обновите страницу", "You don't have permission to delete global extensions.": "Отсутствуют права на удаление глобальных расширений.", "Are you sure you want to delete ${0}?": "Вы точно хотите удалить ${0}?", "You don't have permission to move extensions.": "Отсутствуют права на перемещение расширений.", "Are you sure you want to move ${0} to your local extensions? This will make it available only for you.": "Вы точно хотите сделать ${0} локальным расширением? После этого оно будет доступно только вам.", "Are you sure you want to move ${0} to the global extensions? This will make it available for all users.": "Вы точно хотите сделать ${0} глобальным расширением? После этого оно будет доступно всем пользователям.", "Extension ${0} moved.": "Расширение ${0} перемещено.", "Extension ${0} deleted": "Расширение ${0} удалено.", "Please wait...": "Пожалуйста, подождите...", "Installing extension": "Идёт установка расширения", "Extension installation failed": "Не удалось установить расширение", "Extension '${0}' by ${1} (version ${2}) has been installed successfully!": "Расширение ${0} от автора ${1} (версия ${2}) успешно установлено!", "Extension installation successful": "Расширение установлено", "Extension updates available": "Доступных обновлений расширений", "Auto-updating extensions. This may take several minutes.": "Запущено автоматическое обновление расширений. Это может занять несколько минут.", "Install just for me": "Установить только мне", "Install": "Установить", "Install for all users": "Установить для всех пользователей", "Modules provided by your Extras API:": "Модули из Extras API:", "Not connected to the API!": "Нет соединения с API!", "ext_type_system": "Это комплектное расширение. Его нельзя удалить, а обновляется оно вместе со всей системой.", "Update all": "Обновить все", "Close": "Закрыть", "Optional modules:": "Необязательные модули:", "Sort: Display Name": "Сортировать: по названию", "Sort: Loading Order": "Сортировать: в порядке загрузки", "Click to toggle": "Нажмите, чтобы включить или выключить", "Loading Asset List": "Загрузить список ресурсов", "Don't ask again for this URL": "Запомнить выбор для этого адреса", "Are you sure you want to connect to the following url?": "Вы точно хотите подключиться к этому адресу?", "All": "Всё", "Characters": "Персо<PERSON><PERSON>и", "Ambient sounds": "Звуковой эмбиент", "Blip sounds": "Звуки уведомлений", "Background music": "Фоновая музыка", "extension_install_1": "Чтобы загружать расширения из этого списка, у вас должен быть установлен ", "extension_install_2": ".", "extension_install_3": "Нажмите на иконку ", "extension_install_4": ", чтобы перейти в репозиторий расширения и получить более подробную информацию о нём.", "Extension repo/guide:": "Репозиторий расширения:", "Preview in browser": "Предпросмотр", "Adds a function tool": "Частично или полностью работает через вызов функций", "Tool": "Функции", "Move extension": "Переместить расширение", "ext_type_local": "Это локальное расширение, доступно только вам", "ext_type_global": "Это глобальное расширение, доступно всем пользователям", "Move": "Переместить", "Enter the Git URL of the extension to install": "Введите Git-адрес расширения", "Please be aware that using external extensions can have unintended side effects and may pose security risks. Always make sure you trust the source before importing an extension. We are not responsible for any damage caused by third-party extensions.": "помните, что используя расширения от сторонних авторов, вы можете подвергать систему опасности. Устанавливайте расширения только от проверенных разработчиков. Мы не несём ответственности за любой ущерб, причинённый сторонними расширениями.", "Disclaimer:": "Внимание:", "context_derived": "Считывать из метаданных модели (по возможности)", "instruct_derived": "Считывать из метаданных модели (по возможности)", "Confirm token parsing with": "Чтобы убедиться в правильности выделения токенов, используйте", "Reasoning Effort": "Рассуждения", "Constrains effort on reasoning for reasoning models.": "Регулирует объём внутренних рассуждений модели (reasoning), для моделей, которые поддерживают эту возможность.\nПри менее подробном рассуждении ответ получается быстрее, а также экономятся токены, уходящие на рассуждения.", "openai_reasoning_effort_low": "Поверхностные", "openai_reasoning_effort_medium": "Обычные", "openai_reasoning_effort_high": "Подробные", "Persona Lore Alt+Click to open the lorebook": "Лорбук данной персоны\nAlt + ЛКМ чтобы открыть лорбук", "Persona Lorebook for": "Лорбук для персоны", "persona_world_template_txt": "Выбранный мир будет привязан к этой персоне. Будет работать вместе с глобальным лорбуком и лорбуками персонажа и чата.", "Global list": "Глобальный список", "Preset-specific list": "Список для данного пресета", "Banned tokens/strings are being sent in the request.": "Запрещённые токены и строки отсылаются в запросе.", "Banned tokens/strings are NOT being sent in the request.": "Запрещённые токены и строки НЕ отсылаются в запросе.", "Add a reasoning block": "Добавить блок рассуждений", "Create a copy of this message?": "Продублировать это сообщение?", "Max Recursion Steps": "Макс. глубина рекурсии", "0 = unlimited, 1 = scans once and doesn't recurse, 2 = scans once and recurses once, etc": "0 = неограничено, 1 = сканировать единожды, 2 = сканировать единожды и сделать один повторный проход, и т.д.\n(неактивно при указанном мин. числе активаций)", "(disabled when max recursion steps are used)": "(неактивно при указанной макс. глубине рекурсии)", "Enter a valid API URL": "Введите корректный адрес API", "No Ollama model selected.": "Не выбрана модель Ollama", "Background Fitting": "Способ подгонки фона под разрешение", "Chat Lore Alt+Click to open the lorebook": "Лорбук данного чата\nAlt + ЛКМ чтобы открыть лорбук", "Token Counter": "Подсчитать токены", "Type / paste in the box below to see the number of tokens in the text.": "Введите или вставьте текст в окошко ниже, чтобы подсчитать количество токенов в нём.", "Selected tokenizer:": "Выбранный токенайзер:", "Input:": "Входные данные:", "Tokenized text:": "Токенизированный текст:", "Token IDs:": "Идентификаторы токенов:", "Tokens:": "Токенов:", "Reset custom sampler selection": "Сбросить подборку семплеров", "Here you can toggle the display of individual samplers. (WIP)": "Здесь можно включить или выключить отображение каждого из сэмплеров отдельно. (WIP)", "Request Model Reasoning": "Запрашивать цепочку рассуждений", "Reasoning": "Рассуждения / Reasoning", "Auto-Parse": "Авто-парсинг", "reasoning_auto_parse": "Автоматически считывать блоки рассуждений, расположенные между префиксом и суффиксом рассуждений. Для работы должно быть указано и то, и другое.", "Auto-Expand": "Разворачивать", "reasoning_auto_expand": "Автоматически разворачивать блоки рассуждений.", "Show Hidden": "Показывать время", "reasoning_show_hidden": "Отображать затраченное на рассуждения время для моделей со скрытой цепочкой рассуждений", "Add to Prompts": "Добавлять в промпт", "reasoning_add_to_prompts": "Добавлять существующие блоки рассуждений в промпт. Для добавления новых используйте меню редактирования сообщений.", "reasoning_max_additions": "Макс. кол-во блоков рассуждений в промпте, считается от последнего сообщения", "Max": "Макс.", "Reasoning Formatting": "Форматирование рассуждений", "Prefix": "Префикс", "Suffix": "Постфикс", "Separator": "Разделитель", "reasoning_separator": "Вставляется между рассуждениями и содержанием самого сообщения.", "reasoning_prefix": "Вставляется перед рассуждениями.", "reasoning_suffix": "Вставляется после рассуждений.", "Seed_desc": "Фиксированное значение зерна позволяет получать предсказуемые, одинаковые результаты на одинаковых настройках. Поставьте -1 для рандомного зерна.", "# of Beams": "Кол-во лучей", "The number of sequences generated at each step with Beam Search.": "Кол-во вариа<PERSON><PERSON><PERSON>, генерируемых Beam Search на каждом шаге работы.", "Penalize sequences based on their length.": "Штрафует строки в зависимости от длины", "Controls the stopping condition for beam search. If checked, the generation stops as soon as there are '# of Beams' sequences. If not checked, a heuristic is applied and the generation is stopped when it's very unlikely to find better candidates.": "Определяет, когда останавливать работу Beam Search. Поставив галочку, вы укажете поиску остановиться тогда, когда будет достигнуто кол-во лучей из соответствующего поля. Если галочку не отмечать, то генерация остановится тогда, когда сочтёт, что дальше найти лучших кандидатов слишком маловероятно.", "A greedy, brute-force algorithm used in LLM sampling to find the most likely sequence of words or tokens. It expands multiple candidate sequences at once, maintaining a fixed number (beam width) of top sequences at each step.": "Жадный алгоритм LLM-сэмплинга, подбирающий наиболее вероятную последовательность слов или токенов путём исследования и расширения сразу нескольких вариантов. На каждом шаге он удерживает фиксированное кол-во самых подходящих вариантов (ширина луча).", "Temperature_Last_desc": "Применять сэмплер Температуры в последнюю очередь. Почти всегда оправдано.\nПри включении: сначала все токены семплируются, и затем температура регулирует распределение у оставшихся (технически, у оставшихся логитов).\nПри выключении: сначала температура настраивает распределение ВСЕХ токенов, и потом они семплируются уже с этим обновлённым распределением.\nПри отключении этой опции токены в хвосте получают больше шансов попасть в итоговую последовательность, что может привести к менее связным и логичным ответам.", "Swipe # for All Messages": "Номер свайпа на всех сообщениях", "Display swipe numbers for all messages, not just the last.": "Отображать номер свайпа для всех сообщений, а не только для последнего.", "Never": "Никогда", "Groups and Past Personas": "Для групп и прошлых персон", "Always": "Всегда", "Request model reasoning": "Запрашивать рассуждения", "Allows the model to return its thinking process.": "Позволяет модели высылать в ответе свою цепочку рассуждений.", "Rename Persona": "Переименовать персону", "Change Persona Image": "Изменить изображение персоны", "Duplicate Persona": "Клонировать персону", "Delete Persona": "Удалить персону", "Enter a new name for this persona:": "Введите новое имя персоны:", "Connections": "Связи", "Click to select this as default persona for the new chats. Click again to remove it.": "Нажмите, чтобы установить эту персону стандартной для всех новых чатов. Нажмите ещё раз, чтобы отключить.", "Character": "Перс<PERSON><PERSON><PERSON>", "Click to lock your selected persona to the current character. Click again to remove the lock.": "Нажмите, чтобы закрепить эту персону для текущего персонажа. Нажмите ещё раз, чтобы открепить.", "Chat": "Чат", "[No character connections. Click one of the buttons above to connect this persona.]": "[Связи отсутствуют. Нажмите на одну из кнопок выше, чтобы создать.]", "Global Settings": "Общие настройки", "Allow multiple persona connections per character": "Разрешить привязывать несколько персон к одному персонажу", "When multiple personas are connected to a character, a popup will appear to select which one to use": "При связывании нескольких персон с персонажем, будет появляться окошко с предложением выбрать нужную.", "Auto-lock a chosen persona to the chat": "Автоматически привязывать выбранную персону к чату", "Whenever a persona is selected, it will be locked to the current chat and automatically selected when the chat is opened.": "При выборе новой персоны она автоматически будет привязана к текущему чату, и будет выбираться при его открытии.", "Current Persona": "Текущая персона", "The chat has been successfully converted!": "Чат успешно преобразован!", "Manual": "Когда вы скажете", "Hide on character card": "Скрыть на карточке персонажа", "All connections to ${0} have been removed.": "Все связи с персонажем ${0} были удалены.", "Personas Unlocked": "Персоны отвязаны", "Remove All Connections": "Удалить все связи", "Persona ${0} selected and auto-locked to current chat": "Персона ${0} выбрана и автоматически закреплена за этим чатом", "This persona is only temporarily chosen. Click for more info.": "Данная персона выбрана лишь временно. Нажмите, чтобы узнать больше.", "Temporary Persona": "Временная персона", "A different persona is locked to this chat, or you have a different default persona set. The currently selected persona will only be temporary, and resets on reload. Consider locking this persona to the chat if you want to permanently use it.": "К этому чату уже привязана иная персона, либо у вас выбрана иная персона по-умолчанию. Выбранная в данный момент персона будет временной, и сбросится после перезагрузки. Если хотите всегда использовать её в этом чате, советуем её прикрепить.", "Current Persona: ${0}": "Выбранная персона: ${0}", "Chat persona: ${0}": "Персона для этого чата: ${0}", "Default persona: ${0}": "Персона по умолчанию (стандартная): ${0}", "Persona ${0} is now unlocked from this chat.": "Персона ${0} отвязана от этого чата.", "Persona Unlocked": "Персона отвязана", "Persona ${0} is now unlocked from character ${1}.": "Персона ${0} отвязана от персонажа ${1}.", "Persona Not Found": "Персона не найдена", "Persona Locked": "Персона закреплена", "User persona ${0} is locked to character ${1}${2}": "Персона ${0} прикреплена к персонажу ${1}${2}", "Persona Name Not Set": "У персоны отсутствует имя", "You must bind a name to this persona before you can set a lorebook.": "Перед привязкой лорбука персоне необходимо присвоить имя.", "Default Persona Removed": "Персона по умолчанию снята", "Persona is locked to the current character": "Персона закреплена за текущим персонажем", "Persona is locked to the current chat": "Персона закреплена за текущим чатом", "characters": "перс.", "character": "персо<PERSON>ж", "in this group": "в группе", "Chatting Since": "Первый чат", "Chat Time": "Общее время чата", "First Interaction": "Первое взаимодействие", "User Messages": "Сообщений отправлено пользователем", "Character Messages": "Сообщений отправлено персонажем", "User Words": "Слов отправлено пользователем", "Character Words": "Слов отправлено персонажем", "stats_header_User": "пользователю", "stats_header_Character": "персона<PERSON>у", "${0} Stats": "Статистика по ${0}", "Context": "Контекст", "Response": "Ответ", "Connected": "Подключено", "Enter new background name:": "Введите новое название для фона:", "AI Horde Website": "Сайт AI Horde", "Enable web search": "Включить поиск в Интернете", "Use search capabilities provided by the backend.": "Разрешить использование предоставляемых бэкендом функций поиска.", "Request inline images": "Запрашивать inline-изображения", "Allows the model to return image attachments.": "Разрешить модели отправлять вложения в виде картинок.", "Request inline images_desc_2": "Не совместимо со следующим функционалом: вызов функций, поиск в Интернете, системный промпт.", "Connected Personas": "Связанные персоны", "[Currently no personas connected]": "[Связанных персон нет]", "The following personas are connected to the current character.\n\nClick on a persona to select it for the current character.\nShift + Click to unlink the persona from the character.": "С этим персонажем связаны следующие персоны.\n\nНажмите на персону, чтобы выбрать её для данного персонажа.\nShift + ЛКМ, чтобы её отвязать.", "Persona Connections": "Связи с персонами", "Pooled order": "Если уже давно не отвечали", "Attach a file or image to a current chat.": "Приложить файл или изображение к текущему чату", "Delete the Chat File?": "Удалить чат?", "Forbidden": "Доступ запрещён", "To view your API keys here, set the value of allowKeysExposure to true in config.yaml file and restart the SillyTavern server.": "Чтобы видеть здесь ваши API-ключи, установите параметр allowKeysExposure в config.yaml в положение true, после чего перезапустите сервер SillyTavern.", "Invalid endpoint URL. Requests may fail.": "Некорректный адрес эндпоинта. Запросы могут не проходить.", "How to install extensions?": "Как устанавливать расширения?", "Click the flashing button to install extensions.": "Чтобы их установить, нажмите на мигающую кнопку.", "ext_regex_reasoning_desc": "Содержимое блоков рассуждений. При отмеченной галочке \"Только промпт\" будут также обработаны добавленные в промпт рассуждения.", "Macro in Find Regex": "Макросы в рег. выражении", "Don't substitute": "Не заменять", "Substitute (raw)": "Заменять в \"чистом\" виде", "Substitute (escaped)": "Заменять после экранирования", "ext_regex_other_options_desc": "По умолчанию, расширение вносит изменения в сам файл чата.\nПри включении одной из опций (или обеих), файл чата останется нетронутым, при этом сами изменения по-прежнему будут действовать.", "ext_regex_flags_help": "Нажмите, чтобы узнать больше о флагах в рег. выражениях.", "Applies to all matches": "Заменяет все вхождения", "Applies to the first match": "Заменяет первое вхождение", "Case insensitive": "Не чувствительно к регистру", "Case sensitive": "Чувствительно к регистру", "Find Regex is empty": "Рег. выражение не указано", "Click the button to save it as a file.": "Нажмите на кнопку справа, чтобы сохранить его в файл.", "Export as JSONL": "Экспорт в формате JSONL", "Thought for some time": "Какое-то время заняли размышления", "Thinking...": "В раздумьях...", "Thought for ${0}": "Размышления заняли ${0}", "Hidden reasoning - Add reasoning block": "Рассуждения скрыты - Добавить блок рассуждений", "Add reasoning block": "Добавить блок рассуждений", "Edit reasoning": "Редактировать рассуждения", "Copy reasoning": "Скопировать рассуждения", "Confirm Edit": "Подтвердить", "Remove reasoning": "Удалить рассуждения", "Cancel edit": "Отменить редактирование", "Remove Reasoning": "Удалить рассуждения", "Are you sure you want to clear the reasoning?<br />Visible message contents will stay intact.": "Вы точно хотите удалить блок рассуждений?<br />Основное сообщение останется на месте.", "Reasoning Parse": "Парсинг рассуждений", "Both prefix and suffix must be set in the Reasoning Formatting settings.": "В настройках форматирования рассуждений должны быть заданы префикс и суффикс.", "Invalid return type '${0}', defaulting to 'reasoning'.": "Некорректный возвращаемый тип, используем стандартный 'reasoning'.", "Reasoning already exists.": "Рассуждения уже присутствуют.", "Edit Message": "Редактирование", "Status check bypassed": "Проверка статуса отключена", "Valid": "Работает", "Tie this entry to specific characters or characters with specific tags": "Привязать запись к опред. персонажам или персонажам с заданными тегами", "Move/Copy Entry to Another Lorebook": "Переместить/Скопировать запись в другой лорбук", "There are no other lorebooks to move to.": "Некуда перемещать: не найдено других лорбуков.", "Select Target Lorebook": "Выберите куда переместить", "Move/Copy '${0}' to:": "Переместить/Скопировать '${0}' в:", "Please select a target lorebook.": "Выберите лорбук, в который будет перемещена запись.", "Scan depth cannot be negative": "Глу<PERSON>ина сканирования не может быть отрицательной", "Scan depth cannot exceed ${0}": "Глубина сканирования не может превышать ${0}", "Select your current Reasoning Template": "Выберите текущий Шаблон рассуждений", "Delete template": "Удалить шаблон", "Reasoning Template": "Шаблон рассуждений", "openai_reasoning_effort_auto": "Авто", "openai_reasoning_effort_minimum": "Минимальные", "openai_reasoning_effort_maximum": "Максимальные", "OpenAI-style options: low, medium, high. Minimum and maximum are aliased to low and high. Auto does not send an effort level.": "OpenAI принимает следующее: low (Поверхностные), medium (Обычные), high (Подробные). Minimum (Минимальные) - то же самое, что low. Maximum (Максимальные) - то же самое, что high. При выборе Auto (Авто) значение не отсылается вообще.", "Allocates a portion of the response length for thinking (low: 10%, medium: 25%, high: 50%). Other options are model-dependent.": "Резервирует часть ответа для рассуждений (Поверхностные: 10% ответа, Обычные: 25%, Подробные: 50%). Остальные значения зависят от конкретной модели.", "xAI Model": "Модель xAI", "xAI API Key": "Ключ от API xAI", "HuggingFace Token": "Т<PERSON><PERSON><PERSON>н <PERSON>", "Endpoint URL": "Адрес эндпоинта", "Example: https://****.endpoints.huggingface.cloud": "Пример: https://****.endpoints.huggingface.cloud", "Featherless Model Selection": "Выбор модели из Featherless", "category": "категория", "Top": "Топовые", "All Classes": "Все классы", "Date Asc": "Дата, возрастание", "Date Desc": "Дата, убывание", "Background Image": "Фоновое изображение", "Delete the background?": "Удалить фон?", "tag_entries": "раз исп.", "Multiple personas are connected to this character.\nSelect a persona to use for this chat.": "К этому персонажу привязано несколько персон.\nВыберите персону, которую хотите использовать в этом чате.", "Select Persona": "Выберите персону", "Completion Object": "Как часть Completion Object", "Move ${0} to:": "Переместить '${0}' в:", "Unique to this chat.": "Действует только в рамках текущего чата.", "All group members will use the following scenario text instead of what is specified in their character cards.": "Все участники группы будут использовать этот сценарий вместо того, который указан в карточке.", "Checkpoints inherit the scenario override from their parent, and can be changed individually after that.": "Чекпоинты наследуют сценарий родителя, после отделения его можно менять.", "Delete Tag": "Удалить тег", "Do you want to delete the tag": "Вы точно хотите удалить тег", "If you want to merge all references to this tag into another tag, select it below:": "Если хотите заменить ссылки на этот тег на какой-то другой, то выберите из списка:", "Open Folder (Show all characters even if not selected)": "Открытая папка (показывает всех персонажей, даже если не выбрана)", "Closed Folder (Hide all characters unless selected)": "Закрытая папка (показывает персонажей только если выбрана)", "No Folder": "Не папка", "Show only favorites": "Показать только избранных персонажей", "Show only groups": "Показать только группы", "Show only folders": "Показать только папки", "Manage tags": "Панель управления тегами", "Show Tag List": "Показать список тегов", "Clear all filters": "Сбросить все фильтры", "There are no items to display.": "Отображать абсолютно нечего.", "Characters and groups hidden by filters or closed folders": "Персонажи и группы скрыты настройками фильтров либо закрытыми папками", "Otterly empty": "Всё что можно, всё выдрано", "Here be dragons": "Список настолько очистился, что в него вернулись драконы", "Kiwibunga": "Настолько пусто, что киви прилетела посидеть", "Pump-a-Rum": "Пу-пу-пу", "Croak it": "Только кваканье лягушек и стрёкот сверчков", "${0} character hidden.": "Персонажей скрыто: ${0}.", "${0} characters hidden.": "Персонажей скрыто: ${0}.", "/ page": "/ стр.", "Context Length": "Размер контекста", "Added On": "Добавлена", "Class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Next page": "След. страница", "Previous page": "Пред. страница", "Group: ${0}": "Группа: ${0}", "You deleted a character/chat and arrived back here for safety reasons! Pick another character!": "Вы удалили персонажа или чат, и мы из соображений безопасности перенесли вас на эту страницу! Выберите другого персонажа!", "Group is empty.": "Группа пуста.", "No characters available": "Персонажей нет", "Choose what to export": "Выберите, что экспортировать", "Text Completion Preset": "Пресет для режима Text Completion", "Update enabled": "Обновить включенные", "Could not connect to API": "Не удалось подключиться к API", "Connected to API": "Соединение с API установлено", "Go back": "Назад", "Recent Chats": "Недавние чаты", "Docs": "Документация", "Temporary Chat": "Временный чат", "Load": "Загрузить", "Import from JSONL": "Импорт из формата JSONL", "**Hint:** Set any character as your welcome page assistant from their \"More...\" menu.": "**Подсказка**: задайте персонажа, который будет встречать вас на главной странице. Для этого зайдите в меню \"Ещё...\" в интерфейсе карточки.", "[The chat is empty]": "[Чат пуст]", "Show more recent chats": "Больше недавних чатов", "Show less recent chats": "Меньше недавних чатов", "Rename chat": "Переименовать чат", "Delete chat": "Удалить чат", "Hide recent chats": "Скрыть недавние чаты", "Show recent chats": "Показать недавние чаты", "Set / Unset as Welcome Page Assistant": "Установить / снять с роли помощника на экране приветствия", "${0} is a system assistant. Choose another character.": "Персонаж ${0} выбран в качестве системного помощника. Выберите другого.", "Set ${0} as your assistant.": "Персонаж ${0} установлен в качестве помощника.", "${0} is no longer your assistant.": "Персонаж ${0} снят с роли помощника.", "Manage API keys": "Управление ключами", "Key:": "Ключ:", "No secrets saved.": "Ключей нет.", "Add Secret": "Доба<PERSON>и<PERSON>ь ключ", "Enter the secret value:": "Ключ:", "Enter a label for the secret (optional):": "Название ключа (необязательно):", "Copy ID": "Копировать ID", "Rename Secret": "Переименовать ключ", "Enter new label for the secret:": "Новое название ключа:", "Delete Secret: ${0}": "Удалить ключ ${0}", "Are you sure you want to delete this secret? This action cannot be undone.": "Вы точно хотите удалить этот ключ? Отменить будет невозможно.", "Secret ID copied to clipboard.": "ID ключа скопирован в буфер обмена.", "The key exposure might be disabled by the server config.": "Вероятно, в настройках сервера отключена возможность раскрытия ключей.", "Failed to copy secret value": "Не удалось скопировать значение", "Select": "Выделить", "Allow compressing requests by removing messages from the middle of the prompt.": "Включает возможность сжатия запроса путём удаления сообщений из середины промпта.", "Allow": "Разрешить", "Forbid": "Запретить", "Auto": "Авто", "Unknown": "Неизвестно", "enable_functions_desc_4": "Не работает, если выбрана постобработка промпта с отключенными функциями!", "This setting affects visibility only.": "Влияет только на видимость.", "openrouter_web_search_fee": "Платная опция! Добавляет $0,02 к стоимости каждого промпта.", "prompt_post_processing_semi_tools": "Semi-strict (чередовать роли, функции включены)", "prompt_post_processing_strict_tools": "Strict (чередовать роли, сначала пользователь; функции включены)", "prompt_post_processing_single": "Только одно сообщение пользователя (функции отключены)", "prompt_post_processing_merge_tools": "Объединять идущие подряд сообщение с одной ролью (функции включены)", "completions note prefix": "Постфикс ", "suffix will be added automatically.": "будет добавлен автоматически.", "Tabby Model": "Мод<PERSON><PERSON>ь Tabby", "Experimental feature. Use at your own risk.": "Экспериментальная возможность. Используйте на собственный риск.", "Tabby model hint prefix": "Чтобы иметь возможность менять модели на лету, укажите параметр ", "must be set in Tabby's config.yml to switch models.": " в файле config.yml на сервере Tabby.", "Use an admin API key.": "Используйте admin-ключ.", "Use OpenRouter website setting": "Использовать указанную на сайте OpenRouter", "Allocates a portion of the response length for thinking (min: 1024 tokens, low: 10%, medium: 25%, high: 50%, max: 95%), but minimum 1024 tokens. Auto does not request thinking.": "Отводит часть ответа под рассуждения (Минимальные: 10%, Обычные: 25%, Подробные: 50%, Максимальные: 95%), но минимум 1024 токена. При выборе значение Авто рассуждения не запрашиваются.", "Use system prompt": "Включить системный промпт", "Send inline videos": "Отправлять inline-видео", "video_inlining_hint_1": "Отправляет модели видео, если она поддерживает такую возможность. Чтобы прикрепить видео к чату, используйте на любом сообщении кнопку", "video_inlining_hint_2": "либо меню", "video_inlining_hint_3": ".", "video_inlining_hint_4": "Видео должно весить не более 20 Мб и длиться не дольше 1 минуты.", "Allocates a portion of the response length for thinking (Flash 2.5/Pro 2.5) (min: 0/128 tokens, low: 10%, medium: 25%, high: 50%, max: 24576/32768 tokens). Auto lets the model decide.": "Отводит часть ответа под рассуждения (Flash 2.5/Pro 2.5) (Минимальные: 0/128 токенов, Поверхностные: 10%, Обычные: 25%, Подробные: 50%, Максимальные: 24576/32768 токенов). При выборе значения Авто модель определяет объём самостоятельно.", "Google Vertex AI Configuration": "Настройки Google Vertex AI", "Authentication Mode": "Режим аутентификации:", "Express Mode (API Key)": "Express Mode (ключ от API)", "Full Version (Service Account)": "Полновесный (аккаунт на сервисе)", "Google Vertex AI API Key": "Ключ от Google Vertex AI", "Service Account Configuration": "Настройки аккаунта", "Region": "Регион", "Service Account JSON Content": "Данные аккаунта в формате JSON", "Validate JSON": "Провалидировать JSON", "View available regions and models": "Посмотреть список доступных регионов и моделей", "Click to Edit": "Редактирование по клику", "Click the message text in the chat log to edit it.": "Нажмите на текст сообщения в чате, чтобы приступить к редактированию.", "Rounded": "Закруглённые", "Notifications:": "Уведомления:", "Top Left": "Слева сверху", "Top Center": "Сверху по центру", "Top Right": "Справа сверху", "Bottom Left": "Снизу слева", "Bottom Center": "Снизу по центру", "Bottom Right": "Снизу справа", "Additional Matching Sources": "Доп. места для поиска", "Character Personality": "Резюме по личности персонажа", "Cap the number of entry activation recursions": "Макс. глубина рекурсии при активации записи другой записью", "Animated background thumbnails": "Статичное превью анимированных фонов", "Make animated backgrounds static in the selection menu. This will reduce UI and network lag if you have a large amount. This is only a change for the selection menu.": "Отключить анимацию фонов при их предпросмотре в меню выбора. Может помочь снизить объём лагов интерфейса и/или интернет-соединения. Затрагивает только меню выбора.", "Clean-Up": "Уборка", "Find and delete backups, unused chats, files, images, etc.": "Найти и удалить бэкапы, неиспользуемые чаты, файлы, картинки и т.д.", "Once deleted, the files will be gone forever!": "Восстановить удалённые файлы будет невозможно!", "Make sure to back up your data in advance.": "Перед удалением не забывайте делать копии.", "No results yet. Tap 'Scan' to start scanning.": "Пока ничего не найдено. Нажмите \"Начать сканирование\", чтобы запустить процесс поиска.", "Scan": "Начать сканирование", "Chats": "Чаты", "Chat files associated with deleted characters.": "Чаты с удалёнными персонажами.", "Group Chats": "Групповые чаты", "Chat files associated with deleted groups.": "Чаты с удалёнными группами.", "Chat Backups": "Копии чатов", "Automatically generated chat backups.": "Автоматически создаваемые бэкапы чатов.", "Settings Backups": "Копии настроек", "Automatically generated settings backups.": "Автоматически создаваемые бэкапы настроек.", "Files": "Файлы", "Files that are not associated with chat messages or Data Bank. WILL DELETE MANUAL UPLOADS!": "Файлы, не связанные ни с одним сообщением или банком данных. ЗАГРУЖЕННЫЕ ВРУЧНУЮ ФАЙЛЫ ТАКЖЕ БУДУТ УДАЛЕНЫ!", "Images": "Изображения", "Images that are not associated with chat messages. WILL DELETE MANUAL UPLOADS!": "Картинки, не связанные ни с одним сообщением. ЗАГРУЖЕННЫЕ ВРУЧНУЮ ФАЙЛЫ ТАКЖЕ БУДУТ УДАЛЕНЫ!", "Avatar Thumbnails": "Превью для аватарок", "Thumbnails for avatars of missing or deleted characters.": "Превью для аватарок удалённых персонажей", "Background Thumbnails": "Превью для фонов", "Thumbnails for missing or deleted backgrounds.": "Превью для удалённых фоновых изображений", "Persona Thumbnails": "Превью для персон", "Thumbnails for missing or deleted personas.": "Превью для удалённых персон", "Delete all items in this category": "Удалить всё в этой категории", "View item content": "Посмотреть содержимое", "Download item": "Скачать содержимое", "Delete this item": "Удалить содержимое", "This will permanently delete the file. THIS CANNOT BE UNDONE!": "Файл будет удалён навсегда. ОТМЕНИТЬ БУДЕТ НЕВОЗМОЖНО!", "This will permanently delete all files in this category. THIS CANNOT BE UNDONE!": "Все файлы в этой категории будут удалены. ОТМЕНИТЬ БУДЕТ НЕВОЗМОЖНО!", "Close popup": "Закрыть окно", "Branch or tag name (optional)": "Ветка или тэг (необязательно)", "ext_regex_bulk_edit": "Массовое редактирование", "Provided free of charge by Pollinations.AI": "Предоставляется бесплатно сервисом Pollinations.AI", "Avoid sending sensitive information. Provider's outputs may include ads.": "Не отправляйте чувствительную информацию. Тексты от провайдера могут содержать рекламу.", "Pollinations Model": "Модель Pollinations", "Character details are hidden.": "Информация о персонаже скрыта.", "Allow / Forbid the use of global styles for this character.": "Разрешить/запретить применение глобальных стилей к этому персонажу.", "Choose how to apply CSS style tags if they are defined in Creator's Notes of this character:": "Выберите, к чему применять CSS-теги, объявленные в Заметках создателя у этого персонажа:", "CAUTION: Malformed styles may cause issues.": "ОСТОРОЖНО: некорректно размеченные стили могут приводить к неожиданному поведению.", "Just to Creator's Notes": "Только к Заметкам создателя", "Apply to the entire app": "Ко всему приложению", "Pin greeting message styles": "CSS из первого сообщения активен всегда", "Always render style tags from greetings, even if the message is unloaded due to lazy loading.": "Всегда учитывать CSS-стили из первого сообщения, даже если оно на данный момент скрыто механизмом ленивой загрузки.", "Secret": "<PERSON><PERSON><PERSON><PERSON>", "Save and connect": "Сохранить и подключиться", "No scripts found": "Скриптов нет", "Are you sure you want to delete this regex script?": "Вы точно хотите удалить этот скрипт?", "Are you sure you want to move this regex script to scoped?": "Вы точно хотите сделать этот скрипт локальным?", "Cannot edit scoped scripts in group chats.": "Локальные скрипты невозможно редактировать в групповом чате.", "Are you sure you want to move this regex script to global?": "Вы точно хотите сделать этот скрипт глобальным?", "Could not save regex script: The script name was undefined or empty!": "Не удалось сохранить regex-скрипт: Не указано либо не определено название скрипта!", "This regex script will not work, but was saved anyway: A find regex isn't present.": "Этот regex не будет работать, но всё равно сохранён: Не указано рег. выражение для поиска", "Ephemerality": "Иллюзорность", "This regex script will not work, but was saved anyway: One \"Affects\" checkbox must be selected!": "Этот regex не будет работать, но всё равно сохранён: Должна быть отмечена хотя бы одна галочка в блоке \"Затрагивает\"!", "This character has embedded regex script(s).": "У этого персонажа имеются встроенные regex скрипты.", "Would you like to allow using them?": "Должны ли они продолжать работу?", "If you want to do it later, select 'Regex' from the extensions menu.": "Если захотите включить их позже, выберите в меню расширений пункт \"Regex\".", "Are you sure you want to delete the selected regex scripts?": "Вы точно хотите удалить выбранные скрипты?", "Regex script \"${0}\" imported.": "Успешно импортирован скрипт ${0}", "Invalid regex object.": "Некорректный объект рег. выражения.", "Sequences as Stop Strings": "Строки из шаблона в качестве стоп-строк", "Story String Sequences": "Строки для общего шаблона", "Used in Default position only.": "Только для позиции \"По умолчанию\"", "Story String Prefix": "Префикс для общего шаблона", "Inserted before a Story String.": "Вставляется перед общим шаблоном", "Story String Suffix": "Постфикс для общего шаблона", "Inserted after a Story String.": "Вставляется после общего шаблона", "Default (top of context)": "По умолчанию (наверху контекста)", "Bind Model to Templates": "Связать модель с шаблоном", "Persona Auto Selected": "Персона автоматически изменена", "Auto-selected persona based on ${0} connection.<br />Your messages will now be sent as ${1}.": "Персона выбрана автоматически на основе следующего критерия: ${0}<br />Ваши сообщения будут отправляться от лица ${1}", "Filter to specific generation types.": "Применять промпт только при определённых типах генерации.", "Quiet": "Тихая", "Swipe": "Свайп", "Triggers": "Триггеры", "All types (default)": "Всегда (по умолчанию)", "Disabling is not recommended.": "Рекомендуем не отключать.", "Toggle search bar": "Показать/скрыть поисковую строку", "Fireworks AI API Key": "Ключ от API Fireworks AI", "Fireworks AI Model": "Модель Fireworks AI", "Moonshot AI API Key": "Ключ от API Moonshot AI", "Moonshot AI Model": "Модель Moonshot AI", "AI/ML API Key": "Ключ от API AI/ML", "AI/ML Model": "Модель AI/ML", "Replace Character": "Заменить персонажа", "Choose a new character card to replace this character with.": "Выберите, каким персонажем хотите заменить текущего.", "All chats, assets and group memberships will be preserved, but local changes to the character data will be lost.": "Чаты с персонажем, его ассеты и членство в группах сохранятся. Однако сделанные вами изменения в карточке будут утеряны.", "Proceed?": "Продолжить?", "No Creator's Notes provided.": "Создатель не оставил примечаний.", "No auxiliary Lorebooks set. Click here to select.": "Вспомогательных лорбуков нет. ЛКМ, чтобы выбрать.", "Persona Title (optional, display only)": "Сноска (не влияет на чат, только в интерфейсе)", "This entry will not be recursively activated by other entries.": "Не будет активироваться другими записями.", "This entry will not activate other entries recursively.": "Не будет активировать другие записи.", "This entry can only be activated on recursive checking.": "Активируется только другими записями.", "This entry will be included ignoring budget constraints, assuming all other checks pass.": "Будет включена в промпт даже при превышении бюджета токенов (при условии, что все остальные условия соблюдены)", "Ignore budget": "Игнорировать бюджет", "Filter to Generation Triggers": "Только для опред. типов генераций", "Apply your current sorting to the \"Order\" field. The Order values will go down from the chosen number.": "Заполнить поле \"Приоритет\" в соответствии с текущей сортировкой для всех записей. Поля будут заполнены значениями, начиная с этого числа:", "More than 100 entries in this world. If you don't choose a number higher than that, the lower entries will default to 0.<br />(Usual default: 100)<br />Minimum: ${0}": "В этом лорбуке больше 100 записей! Если не выставить число больше 100, то последние записи будут иметь приоритет 0.<br/>(Стандартное значение: 100)<br/>(Минимальное число: ${0})", "Apply": "Применить", "Apply Current Sorting": "Выставить приоритеты по сортировке", "Delete the entry with UID: ${0}?": "Удалить запись с UID: ${0}?", "This action is irreversible!": "Отменить будет невозможно!", "Recursion Level": "Уровень рекурсии", "delay_until_recursion_level": "Насколько глубоко должна зайти рекурсия, чтобы иметь возможность активировать эту запись.\n\nИзначально рекурсия делает проход по 1 уровню. Если там не находится подходящих записей, то она идёт на 2 уровень, и там повторяет процесс.\nИ так, пока не дойдёт до самого глубокого уровня.\n\nАктуально только для рекурсивных записей (с отмеченной галочкой \"Рекурсивная\").", "A number lower than the entry count has been chosen. All entries below that will default to 0.": "Введённое число меньше, чем общее кол-во записей. Всем записям внизу списка присвоен приоритет 0.", "Invalid number: ${0}": "Некорректное число: ${0}", "Allow animations for WEBP backgrounds. This is only a change for the selection menu.": "Разрешить анимации в WEBP фонах. Актуально только для меню выбора.", "sd_refine_mode_txt": "Редактировать промпты перед генерацией", "sd_refine_mode": "Отправлять промпты на проверку вам, прежде чем пересылать в API", "sd_function_tool_txt": "Использовать вызов функций", "sd_function_tool": "Использовать функцию, чтобы понимать, когда пора генерировать изображение", "sd_interactive_mode_txt": "Интерактивный режим", "sd_interactive_mode": "Автоматически генерировать изображения, когда появляется сообщение вида \"send me a picture of a cat\"", "sd_multimodal_captioning_txt": "Мультимодальный промптинг", "sd_multimodal_captioning": "Использовать мультимодальный промптинг для создания портретов пользователя и персонажа, основываясь на их аватарках", "sd_free_extend_txt": "Дописывать промпты в свободном режиме", "sd_free_extend_small": "(для интерактивного режима или команд)", "sd_free_extend": "Автоматически дописывать промпты в свободном режиме (для всего, что не касается фонов и портретов), используя текущую выбранную LLM", "sd_snap_txt": "Корректировать автоматически выбранное разрешение", "sd_snap": "Подгонять картинки с фиксированным соотношением сторон (фоны, портреты) к ближайшему известному разрешению (рекомендуется для SDXL)", "Source": "API", "Hint: Save an API key in AI Horde API settings to use it here.": "Подсказка: сохраните ключ от API в настройках API на сайте AI Horde, чтобы использовать его автоматически.", "Sanitize prompts (recommended)": "Прогонять промпты через санитайзер (рекомендуется)", "Sampling steps": "Кол-во шагов", "Width": "Ши<PERSON><PERSON><PERSON>", "Height": "Высота", "Not all samplers supported.": "Поддерживает не все виды сэмплинга.", "(-1 for random)": "(-1 для случайного)", "Negative common prompt prefix": "Фиксированный префикс для негативных промптов", "Chat Message Visibility (by source)": "Видимость сообщений в чате (по источникам)", "Uncheck to hide the extension's messages in chat prompts.": "Снимите галочку, чтобы скрыть из промптов сообщения, приходящие из этого источника.", "Extensions Menu": "Меню расширений", "Slash Command": "Слэш-команды", "Interactive Mode": "Интерактивный режим", "Function Tool": "Функция", "Are you sure you want to delete the style \"${0}\"?": "Вы точно хотите удалить стиль \"${0}\"?", "API Key": "Ключ от API", "These settings only apply to DALL-E 3": "Данные настройки применяются только для DALL-E 3", "Image Style": "Стиль изображения", "Image Quality": "Качество изображения", "Standard": "Стандарт", "sd_res_512x512": "512x512 (1:1, ико<PERSON><PERSON><PERSON>, аватарки)", "sd_res_600x600": "600x600(1:1, иконки, аватарки)", "sd_res_512x768": "512x768 (2:3, вертикальная аватарка для карточки)", "sd_res_768x512": "768x512 (3:2, 35мм плёнка для кинофильмов, альбомн. ориентация)", "sd_res_960x540": "960x540 (16:9, обо<PERSON>, альбомн. ориентация)", "sd_res_540x960": "540x960 (9:16, обо<PERSON>, книжн. ориентация)", "sd_res_1920x1088": "1920x1088 (16:9, обо<PERSON>, альбомн. ориентация)", "sd_res_1088x1920": "1088x1920 (9:16, обо<PERSON>, книжн. ориентация)", "sd_res_1280x720": "1280x720 (16:9, обо<PERSON>, альбомн. ориентация)", "sd_res_720x1280": "720x1280 (9:16, обо<PERSON>, книжн. ориентация)", "Click to set": "Нажмите чтобы задать", "Prompt Upsampling": "Применять Prompt Upsampling", "Whether to perform upsampling on the prompt. If active, automatically modifies the prompt for more creative generation.": "При активации автоматически модифицирует промпт, чтобы сделать процесс генерации более креативным.", "Delete the workflow? This action is irreversible.": "Удалить этот воркфлоу? Отменить будет невозможно.", "Swap width and height": "Поменять местами ширину и высоту"}