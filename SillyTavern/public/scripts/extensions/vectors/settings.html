<div class="vectors_settings">
    <div class="inline-drawer">
        <div class="inline-drawer-toggle inline-drawer-header">
            <b data-i18n="Vector Storage">Vector Storage</b>
            <div class="inline-drawer-icon fa-solid fa-circle-chevron-down down"></div>
        </div>
        <div class="inline-drawer-content">
            <div class="flex-container flexFlowColumn">
                <label for="vectors_source" data-i18n="Vectorization Source">
                    Vectorization Source
                </label>
                <select id="vectors_source" class="text_pole">
                    <option value="cohere">Cohere</option>
                    <option value="extras">Extras (deprecated)</option>
                    <option value="palm">Google AI Studio</option>
                    <option value="vertexai">Google Vertex AI</option>
                    <option value="koboldcpp">KoboldCpp</option>
                    <option value="llamacpp">llama.cpp</option>
                    <option value="transformers" data-i18n="Local (Transformers)">Local (Transformers)</option>
                    <option value="mistral">MistralAI</option>
                    <option value="nomicai">NomicAI</option>
                    <option value="ollama">Ollama</option>
                    <option value="openai">OpenAI</option>
                    <option value="togetherai">TogetherAI</option>
                    <option value="vllm">vLLM</option>
                    <option value="webllm" data-i18n="WebLLM Extension">WebLLM Extension</option>
                </select>
            </div>
            <div class="flex-container flexFlowColumn" id="vector_altEndpointUrl">
                <label class="checkbox_label" for="vector_altEndpointUrl_enabled" title="Enable secondary endpoint URL usage, instead of the main one.">
                    <input id="vector_altEndpointUrl_enabled" type="checkbox" class="checkbox">
                    <span data-i18n="Use secondary URL">Use secondary URL</span>
                </label>
                <label for="vector_altEndpoint_address" data-i18n="Secondary Embedding endpoint URL">
                    Secondary Embedding endpoint URL
                </label>
                <input id="vector_altEndpoint_address" class="text_pole" type="text" placeholder="e.g. http://localhost:5001" />
            </div>
            <div class="flex-container flexFlowColumn" id="webllm_vectorsModel">
                <label for="vectors_webllm_model" data-i18n="Vectorization Model">
                    Vectorization Model
                </label>
                <div class="flex-container">
                    <select id="vectors_webllm_model" class="text_pole flex1">
                    </select>
                    <div id="vectors_webllm_load" class="menu_button menu_button_icon" title="Verify and load the selected model.">
                        <i class="fa-solid fa-check-to-slot"></i>
                    </div>
                </div>
                <div>
                    Requires the WebLLM extension to be installed. Click <a href="#" id="vectors_webllm_install">here</a> to install.
                </div>
            </div>
            <div class="flex-container flexFlowColumn" id="ollama_vectorsModel">
                <label for="vectors_ollama_model" data-i18n="Vectorization Model">
                    Vectorization Model
                </label>
                <input id="vectors_ollama_model" class="text_pole" type="text" placeholder="Model tag, e.g. llama3" />
                <label for="vectors_ollama_keep" class="checkbox_label" title="When checked, the model will not be unloaded after use.">
                    <input id="vectors_ollama_keep" type="checkbox" />
                    <span data-i18n="Keep model in memory">Keep model in memory</span>
                </label>
                <div>
                    The model must be downloaded first! Do it with the <code>ollama pull</code> command or <a href="#" id="vectors_ollama_pull">click here</a>.
                </div>
                <i data-i18n="Hint: Set the URL in the API connection settings.">
                    Hint: Set the URL in the API connection settings.
                </i>
            </div>
            <div class="flex-container flexFlowColumn" id="koboldcpp_vectorsModel">
                <span>
                    Set the KoboldCpp URL in the Text Completion API connection settings.
                </span>
                <span>
                    Must use version 1.87 or higher and have an embedding model loaded.
                </span>
            </div>
            <div class="flex-container flexFlowColumn" id="llamacpp_vectorsModel">
                <span data-i18n="The server MUST be started with the --embedding flag to use this feature!">
                    The server MUST be started with the <code>--embedding</code> flag to use this feature!
                </span>
                <i data-i18n="Hint: Set the URL in the API connection settings.">
                    Hint: Set the URL in the API connection settings.
                </i>
            </div>
            <div class="flex-container flexFlowColumn" id="openai_vectorsModel">
                <label for="vectors_openai_model" data-i18n="Vectorization Model">
                    Vectorization Model
                </label>
                <select id="vectors_openai_model" class="text_pole">
                    <option value="text-embedding-ada-002">text-embedding-ada-002</option>
                    <option value="text-embedding-3-small">text-embedding-3-small</option>
                    <option value="text-embedding-3-large">text-embedding-3-large</option>
                </select>
            </div>
            <div class="flex-container flexFlowColumn" id="cohere_vectorsModel">
                <label for="vectors_cohere_model" data-i18n="Vectorization Model">
                    Vectorization Model
                </label>
                <select id="vectors_cohere_model" class="text_pole">
                    <option value="embed-v4.0">embed-v4.0</option>
                    <option value="embed-english-v3.0">embed-english-v3.0</option>
                    <option value="embed-multilingual-v3.0">embed-multilingual-v3.0</option>
                    <option value="embed-english-light-v3.0">embed-english-light-v3.0</option>
                    <option value="embed-multilingual-light-v3.0">embed-multilingual-light-v3.0</option>
                    <option value="embed-english-v2.0">embed-english-v2.0</option>
                    <option value="embed-english-light-v2.0">embed-english-light-v2.0</option>
                    <option value="embed-multilingual-v2.0">embed-multilingual-v2.0</option>
                </select>
            </div>
            <div class="flex-container flexFlowColumn" id="together_vectorsModel">
                <label for="vectors_togetherai_model" data-i18n="Vectorization Model">
                    Vectorization Model
                </label>
                <select id="vectors_togetherai_model" class="text_pole">
                    <option value="togethercomputer/m2-bert-80M-32k-retrieval">M2-BERT-Retrieval-32k</option>
                    <option value="togethercomputer/m2-bert-80M-8k-retrieval">M2-BERT-Retrieval-8k</option>
                    <option value="togethercomputer/m2-bert-80M-2k-retrieval">M2-BERT-Retrieval-2K</option>
                    <option value="WhereIsAI/UAE-Large-V1">UAE-Large-V1</option>
                    <option value="BAAI/bge-large-en-v1.5">BAAI-Bge-Large-1p5</option>
                    <option value="BAAI/bge-base-en-v1.5">BAAI-Bge-Base-1p5</option>
                    <option value="sentence-transformers/msmarco-bert-base-dot-v5">Sentence-BERT</option>
                    <option value="bert-base-uncased">Bert Base Uncased</option>
                </select>
            </div>
            <div class="flex-container flexFlowColumn" id="vllm_vectorsModel">
                <label for="vectors_vllm_model" data-i18n="Vectorization Model">
                    Vectorization Model
                </label>
                <input id="vectors_vllm_model" class="text_pole" type="text" placeholder="Model name, e.g. intfloat/e5-mistral-7b-instruct" />
                <i data-i18n="Hint: Set the URL in the API connection settings.">
                    Hint: Set the URL in the API connection settings.
                </i>
            </div>
            <div class="flex-container flexFlowColumn" id="google_vectorsModel">
                <label for="vectors_google_model" data-i18n="Vectorization Model">
                    Vectorization Model
                </label>
                <select id="vectors_google_model" class="text_pole">
                    <option value="gemini-embedding-001">gemini-embedding-001</option>
                    <option value="gemini-embedding-exp-03-07">gemini-embedding-exp-03-07</option>
                    <option value="text-embedding-004">text-embedding-004</option>
                    <option value="embedding-001">embedding-001</option>
                </select>
            </div>
            <div class="flex-container alignItemsCenter" id="nomicai_apiKey">
                <label for="api_key_nomicai" class="flex1">
                    <span data-i18n="NomicAI API Key">NomicAI API Key</span>
                </label>
                <div id="api_key_nomicai" class="menu_button menu_button_icon manage-api-keys" data-key="api_key_nomicai">
                    <i class="fa-solid fa-key"></i>
                    <span data-i18n="Click to set">Click to set</span>
                </div>
            </div>

            <div class="flex-container marginTopBot5">
                <div class="flex-container flex1 flexFlowColumn" title="How many last messages will be matched for relevance.">
                    <label for="vectors_query">
                        <small data-i18n="Query messages">Query messages</small>
                    </label>
                    <input type="number" id="vectors_query" class="text_pole" min="1" max="99" />
                </div>
                <div class="flex-container flex1 flexFlowColumn" title="Cut-off score for relevance. Helps to filter out irrelevant data.">
                    <label for="vectors_query">
                        <small data-i18n="Score threshold">Score threshold</small>
                    </label>
                    <input type="number" id="vectors_score_threshold" class="text_pole" min="0" max="1" step="0.05" />
                </div>
                <div class="flex-container flex1 flexFlowColumn" title="Prioritize chunking on the preferred delimiter.">
                    <label for="vectors_force_chunk_delimiter">
                        <small data-i18n="Chunk boundary">Chunk boundary</small>
                    </label>
                    <textarea id="vectors_force_chunk_delimiter" class="text_pole" rows="1" placeholder="(None)"></textarea>
                </div>
            </div>

            <div class="flex-container">
                <label class="checkbox_label expander" for="vectors_include_wi" title="Query results can activate World Info entries.">
                    <input id="vectors_include_wi" type="checkbox" class="checkbox">
                    <span data-i18n="Include in World Info Scanning">Include in World Info Scanning</span>
                </label>
            </div>

            <hr>

            <h4 data-i18n="World Info settings">
                World Info settings
            </h4>

            <label class="checkbox_label" for="vectors_enabled_world_info" title="Enable activation of World Info entries based on vector similarity.">
                <input id="vectors_enabled_world_info" type="checkbox" class="checkbox">
                <span data-i18n="Enable for World Info">Enable for World Info</span>
            </label>

            <div id="vectors_world_info_settings" class="marginTopBot5">
                <div class="flex-container">
                    <label for="vectors_enabled_for_all" class="checkbox_label">
                        <input id="vectors_enabled_for_all" type="checkbox" />
                        <span data-i18n="Enabled for all entries">Enabled for all entries</span>
                    </label>
                    <ul class="margin0">
                        <li>
                            <small data-i18n="Checked: all entries except ❌ status can be activated.">Checked: all entries except ❌ status can be activated.</small>
                        </li>
                        <li>
                            <small data-i18n="Unchecked: only entries with ❌ status can be activated.">Unchecked: only entries with 🔗 status can be activated.</small>
                        </li>
                    </ul>
                </div>
                <div class="flex-container">
                    <div class="flex1">
                        <!--  Vacant for future use -->
                    </div>
                    <div class="flex1" title="Maximum number of entries to be activated">
                        <label for="vectors_max_entries" >
                            <small data-i18n="Max Entries">Max Entries</small>
                        </label>
                        <input id="vectors_max_entries" type="number" class="text_pole widthUnset" min="1" max="9999" />
                    </div>
                    <div class="flex1">
                        <!--  Vacant for future use -->
                    </div>
                </div>
            </div>

            <h4 data-i18n="File vectorization settings">
                File vectorization settings
            </h4>

            <label class="checkbox_label" for="vectors_enabled_files">
                <input id="vectors_enabled_files" type="checkbox" class="checkbox">
                <span data-i18n="Enable for files">Enable for files</span>
            </label>

            <div id="vectors_files_settings" class="marginTopBot5">
                <label class="checkbox_label" for="vectors_only_custom_boundary" title="Create a chunk for every custom Chunk boundary detected in the file text. Don't chunk on default boundaries.">
                    <input id="vectors_only_custom_boundary" type="checkbox" class="checkbox">
                    <span data-i18n="Only chunk on custom boundary">
                        Only chunk on custom boundary
                    </span>
                </label>
                <label class="checkbox_label" for="vectors_translate_files" title="This can help with retrieval accuracy if using embedding models that are trained on English data. Uses the selected API from Chat Translation extension settings.">
                    <input id="vectors_translate_files" type="checkbox" class="checkbox">
                    <span data-i18n="Translate files into English before processing">
                        Translate files into English before processing
                    </span>
                    <i class="fa-solid fa-flask" title="Experimental feature"></i>
                </label>
                <div class="flex justifyCenter" title="These settings apply to files attached directly to messages.">
                    <span data-i18n="Message attachments">Message attachments</span>
                </div>
                <div class="flex-container">
                    <div class="flex1" title="Only files past this size will be vectorized.">
                        <label for="vectors_size_threshold">
                            <small data-i18n="Size threshold (KB)">Size threshold (KB)</small>
                        </label>
                        <input id="vectors_size_threshold" type="number" class="text_pole" min="1" max="99999" />
                    </div>
                    <div class="flex1" title="Chunk size for file splitting.">
                        <label for="vectors_chunk_size">
                            <small data-i18n="Chunk size (chars)">Chunk size (chars)</small>
                        </label>
                        <input id="vectors_chunk_size" type="number" class="text_pole" min="1" max="99999" />
                    </div>
                    <div class="flex1" title="The overlap between adjacent chunks in % from chunk size. The overlap text is trimmed to sentence boundaries. 0 = disabled.">
                        <label for="vectors_overlap_percent">
                            <small data-i18n="Chunk overlap (%)">Chunk overlap (%)</small>
                        </label>
                        <input id="vectors_overlap_percent" type="number" class="text_pole" min="0" max="99" step="1" />
                    </div>
                    <div class="flex1" title="How many chunks to retrieve when querying.">
                        <label for="vectors_chunk_count">
                            <small data-i18n="Retrieve chunks">Retrieve chunks</small>
                        </label>
                        <input id="vectors_chunk_count" type="number" class="text_pole" min="1" max="99999" />
                    </div>
                </div>
                <div class="flex justifyCenter" title="These settings apply to files stored in the Data Bank.">
                    <span data-i18n="Data Bank files">Data Bank files</span>
                </div>
                <div class="flex-container">
                    <div class="flex1" title="Only files past this size will be vectorized.">
                        <label for="vectors_size_threshold_db">
                            <small data-i18n="Size threshold (KB)">Size threshold (KB)</small>
                        </label>
                        <input id="vectors_size_threshold_db" type="number" class="text_pole" min="1" max="99999" />
                    </div>
                    <div class="flex1" title="Chunk size for file splitting.">
                        <label for="vectors_chunk_size_db">
                            <small data-i18n="Chunk size (chars)">Chunk size (chars)</small>
                        </label>
                        <input id="vectors_chunk_size_db" type="number" class="text_pole" min="1" max="99999" />
                    </div>
                    <div class="flex1" title="The overlap between adjacent chunks in % from chunk size. The overlap text is trimmed to sentence boundaries. 0 = disabled.">
                        <label for="vectors_overlap_percent_db">
                            <small data-i18n="Chunk overlap (%)">Chunk overlap (%)</small>
                        </label>
                        <input id="vectors_overlap_percent_db" type="number" class="text_pole" min="0" max="99" step="1" />
                    </div>
                    <div class="flex1" title="How many chunks to retrieve when querying.">
                        <label for="vectors_chunk_count_db">
                            <small data-i18n="Retrieve chunks">Retrieve chunks</small>
                        </label>
                        <input id="vectors_chunk_count_db" type="number" class="text_pole" min="1" max="99999" />
                    </div>
                </div>
                <div class="flex-container flexFlowColumn">
                    <label for="vectors_file_template_db">
                        <span data-i18n="Injection Template">Injection Template</span>
                    </label>
                    <textarea id="vectors_file_template_db" class="margin0 text_pole textarea_compact" rows="3" placeholder="Use &lcub;&lcub;text&rcub;&rcub; macro to specify the position of retrieved text."></textarea>
                    <label for="vectors_file_position_db" data-i18n="Injection Position">Injection Position</label>
                    <div class="radio_group">
                        <label>
                            <input type="radio" name="vectors_file_position_db" value="2" />
                            <span data-i18n="Before Main Prompt / Story String">Before Main Prompt / Story String</span>
                        </label>
                        <!--Keep these as 0 and 1 to interface with the setExtensionPrompt function-->
                        <label>
                            <input type="radio" name="vectors_file_position_db" value="0" />
                            <span data-i18n="After Main Prompt / Story String">After Main Prompt / Story String</span>
                        </label>
                        <label for="vectors_file_depth_db" title="How many messages before the current end of the chat." data-i18n="[title]How many messages before the current end of the chat.">
                            <input type="radio" name="vectors_file_position_db" value="1" />
                            <span data-i18n="In-chat @ Depth">In-chat @ Depth</span>
                            <input id="vectors_file_depth_db" class="text_pole widthUnset" type="number" min="0" max="9999" />
                            <span>as</span>
                            <select id="vectors_file_depth_role_db"  class="text_pole widthNatural">
                                <option value="0" data-i18n="System">System</option>
                                <option value="1" data-i18n="User">User</option>
                                <option value="2" data-i18n="Assistant">Assistant</option>
                            </select>
                        </label>
                    </div>
                </div>
                <div class="flex-container">
                    <div id="vectors_files_vectorize_all" class="menu_button menu_button_icon" title="Vectorize all files in the Data Bank and current chat.">
                        <span data-i18n="Vectorize All">Vectorize All</span>
                    </div>
                    <div id="vectors_files_purge" class="menu_button menu_button_icon" title="Purge all file vectors in the Data Bank and current chat.">
                        <span data-i18n="Purge Vectors">Purge Vectors</span>
                    </div>
                </div>
            </div>

            <hr>

            <h4 data-i18n="Chat vectorization settings">
                Chat vectorization settings
            </h4>
            <label class="checkbox_label" for="vectors_enabled_chats">
                <input id="vectors_enabled_chats" type="checkbox" class="checkbox">
                <span data-i18n="Enabled for chat messages">Enabled for chat messages</span>
            </label>

            <hr>

            <div id="vectors_chats_settings">
                <div id="vectors_advanced_settings">
                    <label for="vectors_template" data-i18n="Injection Template">
                        Injection Template
                    </label>
                    <textarea id="vectors_template" class="text_pole textarea_compact" rows="3" placeholder="Use &lcub;&lcub;text&rcub;&rcub; macro to specify the position of retrieved text."></textarea>
                    <label for="vectors_position" data-i18n="Injection Position">Injection Position</label>
                    <div class="radio_group">
                        <label>
                            <input type="radio" name="vectors_position" value="2" />
                            <span data-i18n="Before Main Prompt / Story String">Before Main Prompt / Story String</span>
                        </label>
                        <!--Keep these as 0 and 1 to interface with the setExtensionPrompt function-->
                        <label>
                            <input type="radio" name="vectors_position" value="0" />
                            <span data-i18n="After Main Prompt / Story String">After Main Prompt / Story String</span>
                        </label>
                        <label for="vectors_depth" title="How many messages before the current end of the chat." data-i18n="[title]How many messages before the current end of the chat.">
                            <input type="radio" name="vectors_position" value="1" />
                            <span data-i18n="In-chat @ Depth">In-chat @ Depth </span>
                            <input id="vectors_depth" class="text_pole widthUnset" type="number" min="0" max="9999" />
                        </label>
                    </div>
                    <div class="flex-container">
                        <div class="flex1" title="Can increase the retrieval quality for the cost of processing. 0 = disabled.">
                            <label for="vectors_message_chunk_size">
                                <small data-i18n="Chunk size (chars)">Chunk size (chars)</small>
                            </label>
                            <input id="vectors_message_chunk_size" type="number" class="text_pole widthUnset" min="0" max="9999" />
                        </div>
                        <div class="flex1" title="Prevents last N messages from being placed out of order.">
                            <label for="vectors_protect">
                                <small data-i18n="Retain#">Retain#</small>
                            </label>
                            <input type="number" id="vectors_protect" class="text_pole widthUnset" min="1" max="9999" />
                        </div>
                        <div class="flex1" title="How many past messages to insert as memories.">
                            <label for="vectors_insert">
                                <small data-i18n="Insert#">Insert#</small>
                            </label>
                            <input type="number" id="vectors_insert" class="text_pole widthUnset" min="1" max="9999" />
                        </div>
                    </div>
                    <hr class="m-b-1">
                    <div class="flex-container flexFlowColumn">
                        <div class="flex-container alignitemscenter justifyCenter">
                            <i class="fa-solid fa-flask" title="Summarization for vectors is an experimental feature that may improve vectors or may worsen them. Use at your own discretion."></i>
                            <span data-i18n="Vector Summarization">Vector Summarization</span>
                        </div>
                        <label class="checkbox_label expander" for="vectors_summarize" title="Summarize chat messages before generating embeddings.">
                            <input id="vectors_summarize" type="checkbox" class="checkbox">
                            <span data-i18n="Summarize chat messages for vector generation">Summarize chat messages for vector generation</span>
                        </label>
                        <i class="failure" data-i18n="Warning: This will slow down vector generation drastically, as all messages have to be summarized first.">Warning: This will slow down vector generation drastically, as all messages have to be summarized first.</i>

                        <label class="checkbox_label expander" for="vectors_summarize_user" title="Summarize sent chat messages before generating embeddings.">
                            <input id="vectors_summarize_user" type="checkbox" class="checkbox">
                            <span data-i18n="Summarize chat messages when sending">Summarize chat messages when sending</span>
                        </label>
                        <i class="failure" data-i18n="Warning: This might cause your sent messages to take a bit to process and slow down response time.">Warning: This might cause your sent messages to take a bit to process and slow down response time.</i>

                        <label for="vectors_summary_source" title="Summarize with:">Summarize with:</label>
                        <select id="vectors_summary_source" class="text_pole">
                            <option value="main" data-i18n="Main API">Main API</option>
                            <option value="extras" data-i18n="Extras API">Extras API</option>
                            <option value="webllm" data-i18n="WebLLM Extension">WebLLM Extension</option>
                        </select>

                        <label for="vectors_summary_prompt" title="Summary Prompt:">Summary Prompt:</label>
                        <small data-i18n="Only used when Main API or WebLLM Extension is selected.">Only used when Main API or WebLLM Extension is selected.</small>
                        <textarea id="vectors_summary_prompt" class="text_pole textarea_compact" rows="6" placeholder="This prompt will be sent to AI to request the summary generation."></textarea>
                    </div>
                </div>
                <small data-i18n="Old messages are vectorized gradually as you chat. To process all previous messages, click the button below.">
                    Old messages are vectorized gradually as you chat.
                    To process all previous messages, click the button below.
                </small>
                <div class="flex-container">
                    <div id="vectors_vectorize_all" class="menu_button menu_button_icon" data-i18n="Vectorize All">
                        Vectorize All
                    </div>
                    <div id="vectors_purge" class="menu_button menu_button_icon" data-i18n="Purge Vectors">
                        Purge Vectors
                    </div>
                    <div id="vectors_view_stats" class="menu_button menu_button_icon" data-i18n="View Stats">
                        View Stats
                    </div>
                </div>
                <div id="vectorize_progress" style="display: none;">
                    <small>
                        Processed <span id="vectorize_progress_percent">0</span>% of messages.
                        ETA: <span id="vectorize_progress_eta">...</span> seconds.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
