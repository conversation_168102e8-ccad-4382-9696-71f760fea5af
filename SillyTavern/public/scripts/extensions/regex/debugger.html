<div class="regex-debugger-container">
    <!-- Rules List Column -->
    <div class="regex-debugger-rules-list">
        <h3>
            <i class="fa-solid fa-list-ol"></i>
            <span data-i18n="ext_regex_debugger_active_rules"
                >Active Rules</span
            >
        </h3>
        <div class="flex-container">
            <button
                id="regex_debugger_save_order"
                class="menu_button menu_button_icon interactable"
                title="Save current rule order"
                tabindex="0"
            >
                <i class="fa-solid fa-floppy-disk"></i>
                <span data-i18n="ext_regex_debugger_save_order"
                    >Save Order</span
                >
            </button>
        </div>
        <ul id="regex_debugger_rules" class="sortable-list">
            <!-- Rules will be populated here by JavaScript -->
        </ul>
    </div>

    <!-- Testing Area Column -->
    <div class="regex-debugger-tester">
        <h3>
            <i class="fa-solid fa-vial"></i>
            <span data-i18n="ext_regex_debugger_testing_area"
                >Testing Area</span
            >
        </h3>
        <div class="regex-debugger-io">
            <div class="regex-debugger-input">
                <label
                    for="regex_debugger_raw_input"
                    data-i18n="ext_regex_debugger_raw_input"
                    >Raw Input</label
                >
                <textarea
                    id="regex_debugger_raw_input"
                    class="text_pole autoSetHeight"
                    rows="4"
                ></textarea>
            </div>
            <div
                id="regex_debugger_run_test_header"
                class="flex-container"
            >
                <button
                    id="regex_debugger_run_test"
                    class="menu_button menu_button_icon interactable"
                    title="Run the test pipeline"
                    tabindex="0"
                >
                    <i class="fa-solid fa-play"></i>
                    <span data-i18n="ext_regex_debugger_run_test"
                        >Run Test</span
                    >
                </button>
                <div class="flex-container gap10px">
                    <div class="radio_group">
                        <label
                            ><input
                                type="radio"
                                name="display_mode"
                                value="replace"
                                checked
                            />
                            <span data-i18n="ext_regex_debugger_display_replace"
                                >Replace</span
                            ></label
                        >
                        <label
                            ><input
                                type="radio"
                                name="display_mode"
                                value="highlight"
                            />
                            <span
                                data-i18n="ext_regex_debugger_display_highlight"
                                >Highlight</span
                            ></label
                        >
                    </div>
                    <select
                        id="regex_debugger_render_mode"
                    >
                        <option
                            value="text"
                            data-i18n="ext_regex_debugger_render_text"
                        >
                            Render as Text
                        </option>
                        <option
                            value="message"
                            data-i18n="ext_regex_debugger_render_message"
                        >
                            Render as Message
                        </option>
                    </select>
                </div>
            </div>
            <div class="regex-debugger-results">
                <div class="results-header">
                    <h4>
                        <i class="fa-solid fa-shoe-prints"></i>
                        <span data-i18n="ext_regex_debugger_step_by_step"
                            >Step-by-step Transformation</span
                        >
                    </h4>
                    <div
                        id="regex_debugger_expand_steps"
                        class="menu_button menu_button_icon"
                        title="Expand view"
                    >
                        <i class="fa-solid fa-expand"></i>
                    </div>
                </div>
                <div id="regex_debugger_steps_output" class="results-box"></div>

                <div class="results-header">
                    <h4>
                        <i class="fa-solid fa-flag-checkered"></i>
                        <span data-i18n="ext_regex_debugger_final_output"
                            >Final Output</span
                        >
                    </h4>
                    <div
                        id="regex_debugger_expand_final"
                        class="menu_button menu_button_icon"
                        title="Expand view"
                    >
                        <i class="fa-solid fa-expand"></i>
                    </div>
                </div>
                <div
                    id="regex_debugger_final_output"
                    class="results-box final-output"
                ></div>
            </div>
        </div>
    </div>
</div>

<!-- Template for a single rule item -->
<template id="regex_debugger_rule_template">
    <li class="regex-debugger-rule" draggable="true">
        <i class="fa-solid fa-grip-vertical handle"></i>
        <label class="checkbox">
            <input type="checkbox" class="rule-enabled" checked />
        </label>
        <div class="rule-details">
            <span class="rule-name"></span>
            <code class="rule-regex"></code>
            <small class="rule-scope"></small>
        </div>
        <div class="menu_button menu_button_icon edit_rule" title="Edit Rule">
            <i class="fa-solid fa-pencil"></i>
        </div>
    </li>
</template>

<!-- Template for a single transformation step -->
<template id="regex_debugger_step_template">
    <div class="step-result">
        <div class="step-header">
            <strong></strong>
        </div>
        <pre class="step-output"></pre>
    </div>
</template>
