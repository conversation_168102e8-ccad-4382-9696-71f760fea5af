<h3 class="flex-container justifyCenter alignitemscenter">
    <span data-i18n="Prompt Itemization">Prompt Itemization</span>
    <div id="showRawPrompt" class="fa-solid fa-square-poll-horizontal menu_button" title="Show Raw Prompt" data-i18n="[title]Show Raw Prompt"></div>
    <div id="copyPromptToClipboard" class="fa-solid fa-copy menu_button" title="Copy Prompt" data-i18n="[title]Copy Prompt"></div>
    <div id="diffPrevPrompt" class="fa-solid fa-code-compare menu_button" title="Show Prompt Differences" data-i18n="[title]Show Prompt Differences"></div>
</h3>
<div>
    <div>
        <span data-i18n="API/Model:">API/Model:</span> {{mainApiFriendlyName}} {{#if apiUsed}}({{apiUsed}}){{/if}} {{#if modelUsed}}&ndash; {{modelUsed}}{{/if}}
    </div>
    <div>
        <small><span data-i18n="Preset:">Preset:</span> {{presetName}}</small>
        <span>|</span>
        <small><span data-i18n="Tokenizer:">Tokenizer:</span> {{selectedTokenizer}}</small>
    </div>
</div>

<span class="tokenItemizingSubclass" data-i18n="Only the white numbers really matter. All numbers are estimates. Grey color items may not have been included in the context due to certain prompt format settings.">
    Only the white numbers really matter. All numbers are estimates.
    Grey color items may not have been included in the context due to certain prompt format settings.
</span>
<hr>
<div class="justifyLeft">
    <div class="flex-container">
        <div class="flex-container flex1 flexFlowColumns flexNoGap wide50p tokenGraph">
            <div class="wide100p" style="background-color: grey; height: {{oaiSystemTokensPercentage}}%;"></div>
            <div class="wide100p" style="background-color: salmon; height: {{oaiStartTokensPercentage}}%;"></div>
            <div class="wide100p" style="background-color: indianred; height: {{storyStringTokensPercentage}}%;"></div>
            <div class="wide100p" style="background-color: gold; height: {{worldInfoStringTokensPercentage}}%;"></div>
            <div class="wide100p" style="background-color: palegreen; height: {{ActualChatHistoryTokensPercentage}}%;">
            </div>
            <div class="wide100p" style="background-color: cornflowerblue; height: {{allAnchorsTokensPercentage}}%;">
            </div>
            <div class="wide100p" style="background-color: mediumpurple; height: {{promptBiasTokensPercentage}}%;">
            </div>
        </div>
        <div class="flex-container wide50p">
            <div class="wide100p flex-container flexNoGap flexFlowColumn">
                <div class="flex-container wide100p">
                    <div class="flex1" style="color: grey;"><span data-i18n="System Info:">System Info:</span></div>
                    <div class="">{{oaiSystemTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Chat Start: </div>
                    <div class="tokenItemizingSubclass">{{oaiStartTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Main: </div>
                    <div class="tokenItemizingSubclass">{{oaiMainTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Post-History: </div>
                    <div class="tokenItemizingSubclass">{{oaiJailbreakTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Auxiliary: </div>
                    <div class="tokenItemizingSubclass">{{oaiNsfwTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Nudge: </div>
                    <div class="tokenItemizingSubclass">{{oaiNudgeTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Impersonate: </div>
                    <div class="tokenItemizingSubclass">{{oaiImpersonateTokens}}</div>
                </div>
            </div>
            <div class="wide100p flex-container flexNoGap flexFlowColumn">
                <div class="flex-container wide100p">
                    <div class="flex1" style="color: indianred;"><span data-i18n="Prompt Tokens:">Prompt Tokens:</span></div>
                    <div class="">{{oaiPromptTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Description: </div>
                    <div class="tokenItemizingSubclass">{{charDescriptionTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Personality:</div>
                    <div class="tokenItemizingSubclass">{{charPersonalityTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Scenario: </div>
                    <div class="tokenItemizingSubclass">{{scenarioTextTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">
                        <span>-- Examples:</span>
                        {{#if examplesCount}}<small>({{examplesCount}})</small>{{/if}}
                    </div>
                    <div class="tokenItemizingSubclass">{{examplesStringTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- User Persona:</div>
                    <div class="tokenItemizingSubclass">{{userPersonaStringTokens}}</div>
                </div>
            </div>
            <div class="wide100p flex-container">
                <div class="flex1" style="color: gold;"><span data-i18n="World Info:">World Info:</span></div>
                <div class="">{{worldInfoStringTokens}}</div>
            </div>
            <div class="wide100p flex-container">
                <div class="flex1" style="color: palegreen;">
                    <span data-i18n="Chat History:">Chat History:</span>
                    {{#if messagesCount}}<small>({{messagesCount}})</small>{{/if}}
                </div>
                <div class="">{{ActualChatHistoryTokens}}</div>
            </div>
            <div class="wide100p flex-container flexNoGap flexFlowColumn">
                <div class="wide100p flex-container">
                    <div class="flex1" style="color: cornflowerblue;"><span data-i18n="Extensions:">Extensions:</span></div>
                    <div class="">{{allAnchorsTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Summarize: </div>
                    <div class="tokenItemizingSubclass">{{summarizeStringTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Author's Note:</div>
                    <div class="tokenItemizingSubclass">{{authorsNoteStringTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Smart Context:</div>
                    <div class="tokenItemizingSubclass">{{smartContextStringTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Vector Storage (Chats):</div>
                    <div class="tokenItemizingSubclass">{{chatVectorsStringTokens}}</div>
                </div>
                <div class="flex-container ">
                    <div class=" flex1 tokenItemizingSubclass">-- Vector Storage (Data Bank):</div>
                    <div class="tokenItemizingSubclass">{{dataBankVectorsStringTokens}}</div>
                </div>
            </div>
            <div class="wide100p flex-container">
                <div class="flex1" style="color: mediumpurple;"><span>&lcub;&lcub;&rcub;&rcub;</span> <span data-i18n="Bias:">Bias:</span></div>
                <div class="">{{oaiBiasTokens}}</div>
            </div>
        </div>

    </div>
    <hr>
    <div class="wide100p flex-container flexFlowColumns">
        <div class="flex-container wide100p">
            <div class="flex1"><span data-i18n="Total Tokens in Prompt:">Total Tokens in Prompt:</span></div>
            <div class="">{{finalPromptTokens}}</div>
        </div>
        <div class="flex-container wide100p">
            <div class="flex1"><span data-i18n="Max Context">Max Context</span>&nbsp;<small data-i18n="(Context Size - Response Length)">(Context Size - Response Length)</small>:</div>
            <div class="">{{thisPrompt_max_context}}</div>
        </div>
    </div>
</div>
<hr>
<div id="rawPromptPopup" class="list-group">
    <div id="rawPromptWrapper" class="tokenItemizingMaintext"></div>
</div>
