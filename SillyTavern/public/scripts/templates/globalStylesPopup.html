<div class="flex-container flexFlowColumn">
    <h3 data-i18n="Creator's Notes contain CSS style tags. Do you want to apply them just to Creator's Notes or to the entire application?" class="margin0">
        Creator's Notes contain CSS style tags. Do you want to apply them just to C<PERSON>'s Notes or to the entire application?
    </h3>
    <h4 data-i18n="CAUTION: Malformed styles may cause issues." class="neutral_warning">
        CAUTION: Malformed styles may cause issues.
    </h4>
    <hr>
    <small>
        <span data-i18n="To change the preference later, use the">
            To change the preference later, use the
        </span>
        <code class="fa-solid fa-palette"></code>
        <span data-i18n="button in the Creator's Notes block.">
            button in the Creator's Notes block.
        </span>
    </small>
    <textarea class="text_pole textarea_compact monospace" rows="8" readonly></textarea>
    <small class="justifyLeft">
        <b data-i18n="Note:">
            Note:
        </b>
        <span data-i18n="Class names will be automatically prefixed with 'custom-'.">
            Class names will be automatically prefixed with 'custom-'.
        </span>
    </small>
</div>
