<!DOCTYPE html>
<html>

<head>
    <base href="/">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, viewport-fit=cover, initial-scale=1, maximum-scale=1.0, user-scalable=no, interactive-widget=resizes-content">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="darkreader-lock">
    <meta name="robots" content="noindex, nofollow" />
    <style>
        /* Put critical CSS here. The rest should go in stylesheets. */
        body {
            background-color: rgb(36, 36, 37);
        }
    </style>
    <link rel="preload" as="style" href="style.css">
    <link rel="apple-touch-icon" sizes="57x57" href="img/apple-icon-57x57.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="img/apple-icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="img/apple-icon-114x114.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="img/apple-icon-144x144.png" />
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="style.css">
    <link rel="stylesheet" type="text/css" href="css/st-tailwind.css">
    <link rel="stylesheet" type="text/css" href="css/login.css">
    <link rel="manifest" crossorigin="use-credentials" href="manifest.json">
    <link href="webfonts/NotoSans/stylesheet.css" rel="stylesheet">
    <link href="webfonts/NotoSansMono/stylesheet.css" rel="stylesheet">
    <!-- fontawesome webfonts-->
    <link href="css/fontawesome.min.css" rel="stylesheet">
    <link href="css/solid.min.css" rel="stylesheet">
    <link href="css/user.css" rel="stylesheet">
    <title>SillyTavern</title>
</head>

<body class="login">
    <div id="shadow_popup" style="opacity: 0;">
        <div id="dialogue_popup">
            <div id="dialogue_popup_holder">
                <div id="dialogue_popup_text">
                    <div id="userSelectBlock" class="flex-container flexFlowColumn alignItemsCenter">
                        <h2 id="logoBlock" class="flex-container">
                            <img src="img/logo.png" alt="SillyTavern" class="logo">
                            <span>Welcome to SillyTavern</span>
                        </h2>
                        <h3 id="normalLoginPrompt">
                            Select an Account
                        </h3>
                        <h3 id="discreetLoginPrompt">
                            Enter Login Details
                        </h3>
                        <div id="userListBlock" class="wide100p">
                            <div id="userList" class="flex-container justifySpaceEvenly"></div>
                            <div id="handleEntryBlock" style="display:none;" class="flex-container flexFlowColumn alignItemsCenter">
                                <input id="userHandle" class="text_pole" type="text" placeholder="User handle" autocomplete="username">
                            </div>
                            <div id="passwordEntryBlock" style="display:none;"
                                class="flex-container flexFlowColumn alignItemsCenter">
                                <input id="userPassword" class="text_pole" type="password" placeholder="Password" autocomplete="current-password">
                                <a id="recoverPassword" href="#" onclick="return false;">Forgot password?</a>
                                <div class="flex-container">
                                    <div id="loginButton" class="menu_button">Login</div>
                                </div>
                            </div>
                            <div id="passwordRecoveryBlock" style="display:none;" class="flex-container flexFlowColumn alignItemsCenter">
                                <div id="recoverMessage">
                                    Recovery code has been posted to the server console.
                                </div>
                                <input id="recoveryCode" class="text_pole" type="text" placeholder="Recovery code">
                                <input id="newPassword" class="text_pole" type="password" placeholder="New password" autocomplete="new-password">
                                <div class="flex-container flexGap10">
                                    <div id="sendRecovery" class="menu_button">Send</div>
                                    <div id="cancelRecovery" class="menu_button">Cancel</div>
                                </div>
                            </div>
                        </div>
                        <div class="neutral_warning" id="errorMessage">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="lib/jquery-3.5.1.min.js"></script>
    <script src="scripts/login.js" type="module"></script>
</body>

</html>
