import type { <PERSON><PERSON>odel, <PERSON><PERSON><PERSON>ider } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";

export type ChatModelWithProvider = LLMModel & { provider: LLMProvider };

const MODEL_INCLUDE = { provider: true } as const;

type FindChatModelOptions = {
  requestedModelId?: string | null;
  fallbackModelId?: string | null;
};

export const findChatModel = async (
  options: FindChatModelOptions,
): Promise<ChatModelWithProvider | null> => {
  const requested = options.requestedModelId?.trim();
  if (requested) {
    const model = await prisma.lLMModel.findUnique({
      where: { id: requested },
      include: MODEL_INCLUDE,
    });

    if (model) {
      return model;
    }
  }

  const fallback = options.fallbackModelId?.trim();
  if (fallback) {
    const model = await prisma.lLMModel.findUnique({
      where: { id: fallback },
      include: MODEL_INCLUDE,
    });

    if (model) {
      return model;
    }
  }

  const defaultModel = await prisma.lLMModel.findFirst({
    where: { isDefault: true },
    orderBy: { updatedAt: "desc" },
    include: MODEL_INCLUDE,
  });

  if (defaultModel) {
    return defaultModel;
  }

  return prisma.lLMModel.findFirst({
    orderBy: { updatedAt: "desc" },
    include: MODEL_INCLUDE,
  });
};
