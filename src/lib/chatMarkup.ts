const CONTROL_TAGS = ["StoryLine", "StatusBlock"] as const;
const CONTROL_TAG_GROUP = CONTROL_TAGS.join("|");
const CONTROL_TAG_BLOCK_REGEX = new RegExp(
  `<(${CONTROL_TAG_GROUP})>([\\s\\S]*?)<\\/\\1>`,
  "gi",
);
const CONTROL_TAG_SINGLE_REGEX = new RegExp(
  `<\\/?(${CONTROL_TAG_GROUP})\\s*\\/?>`,
  "gi",
);
const EXCESS_NEWLINES_REGEX = /\n{3,}/g;

const replaceControlBlock = (_: string, __: string, inner: string) => {
  const trimmed = inner.trim();
  return trimmed.length ? `${trimmed}\n` : "";
};

export const normalizeChatContent = (content: string | null | undefined): string => {
  if (typeof content !== "string" || content.length === 0) {
    return "";
  }

  let result = content.replace(CONTROL_TAG_BLOCK_REGEX, replaceControlBlock);
  result = result.replace(CONTROL_TAG_SINGLE_REGEX, "");
  result = result.replace(EXCESS_NEWLINES_REGEX, "\n\n");

  return result.trim();
};

export const chatMessagePreview = (
  content: string | null | undefined,
): string | null => {
  const normalized = normalizeChatContent(content);
  return normalized.length > 0 ? normalized : null;
};
