import { Prisma } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";
import { DEFAULT_MAIN_PROMPT } from "./systemPrompt";
import { replacePersonaPlaceholders, resolvePersonaStrings, type PersonaStrings } from "./userPersona";

export type UserSettingsRecord = {
  id: string;
  userId: string;
  mainPrompt: string | null;
  createdAt: Date;
  updatedAt: Date;
};

const emptySettings = (userId: string): Prisma.UserSettingsCreateInput => ({
  user: {
    connect: { id: userId },
  },
  mainPrompt: null,
});

export const ensureUserSettings = async (userId: string): Promise<UserSettingsRecord> => {
  let settings = await prisma.userSettings.findUnique({
    where: { userId },
  });

  if (!settings) {
    settings = await prisma.userSettings.create({
      data: emptySettings(userId),
    });
  }

  return settings;
};

export const updateUserSettings = async (
  userId: string,
  updates: { mainPrompt?: string | null },
) => {
  const data: Prisma.UserSettingsUpdateInput = {};

  if ("mainPrompt" in updates) {
    data.mainPrompt = updates.mainPrompt ?? null;
  }

  return prisma.userSettings.update({
    where: { userId },
    data,
  });
};

export const resolveMainPrompt = (
  settings: { mainPrompt: string | null },
  personaStrings: PersonaStrings,
) => {
  const prompt = settings.mainPrompt?.trim().length ? settings.mainPrompt : DEFAULT_MAIN_PROMPT;
  return replacePersonaPlaceholders(prompt, personaStrings) ?? prompt;
};

export const getResolvedMainPromptForUser = async (
  userId: string,
  persona: { name: string; content: string },
) => {
  const settings = await ensureUserSettings(userId);
  const personaStrings = resolvePersonaStrings(persona);
  return {
    prompt: resolveMainPrompt(settings, personaStrings),
    settings,
    personaStrings,
  };
};
