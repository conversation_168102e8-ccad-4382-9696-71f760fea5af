import { Buffer } from "node:buffer";

type CharacterCard = Record<string, unknown>;

type TextChunk = {
  keyword: string;
  text: string;
};

const PNG_SIGNATURE = Buffer.from([137, 80, 78, 71, 13, 10, 26, 10]);

const isJsonFile = (file: File) => {
  const lowerName = file.name.toLowerCase();
  return (
    file.type === "application/json" ||
    lowerName.endsWith(".json") ||
    lowerName === "" // fallback when type missing
  );
};

const isPngFile = (file: File) => {
  const lowerName = file.name.toLowerCase();
  return file.type === "image/png" || lowerName.endsWith(".png");
};

const decodeTextChunk = (data: Buffer): TextChunk | null => {
  const separatorIndex = data.indexOf(0);

  if (separatorIndex === -1) {
    return null;
  }

  const keyword = data.slice(0, separatorIndex).toString("latin1");
  const text = data.slice(separatorIndex + 1).toString("latin1");

  return { keyword, text };
};

const extractTextChunksFromPng = (buffer: Buffer): TextChunk[] => {
  if (buffer.length < PNG_SIGNATURE.length) {
    throw new Error("PNG file is too short.");
  }

  if (!buffer.subarray(0, PNG_SIGNATURE.length).equals(PNG_SIGNATURE)) {
    throw new Error("File is not a valid PNG.");
  }

  const chunks: TextChunk[] = [];
  let offset = PNG_SIGNATURE.length;

  while (offset + 8 <= buffer.length) {
    const length = buffer.readUInt32BE(offset);
    offset += 4;

    const type = buffer.toString("ascii", offset, offset + 4);
    offset += 4;

    if (offset + length > buffer.length) {
      throw new Error("PNG chunk length exceeds file size.");
    }

    const chunkData = buffer.slice(offset, offset + length);
    offset += length;

    // Skip CRC (4 bytes)
    offset += 4;

    if (type !== "tEXt") {
      continue;
    }

    const decoded = decodeTextChunk(chunkData);
    if (decoded) {
      chunks.push(decoded);
    }
  }

  return chunks;
};

const parsePngCard = async (file: File): Promise<CharacterCard> => {
  const arrayBuffer = await file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  const textChunks = extractTextChunksFromPng(buffer);

  if (!textChunks.length) {
    throw new Error("PNG metadata does not contain character data.");
  }

  const findChunk = (keyword: string) =>
    textChunks.find((chunk) => chunk.keyword.toLowerCase() === keyword);

  const preferred = findChunk("ccv3") ?? findChunk("chara");

  if (!preferred) {
    throw new Error("PNG character metadata not found.");
  }

  let decoded: string;

  try {
    decoded = Buffer.from(preferred.text, "base64").toString("utf8");
  } catch {
    throw new Error("Character metadata is not valid base64.");
  }

  try {
    const card = JSON.parse(decoded) as CharacterCard;
    return card;
  } catch {
    throw new Error("Character metadata is not valid JSON.");
  }
};

const parseJsonCard = async (file: File): Promise<CharacterCard> => {
  const text = await file.text();

  try {
    const card = JSON.parse(text) as CharacterCard;
    return card;
  } catch {
    throw new Error("Uploaded JSON is invalid.");
  }
};

export const parseCharacterCardFile = async (
  file: File,
): Promise<CharacterCard> => {
  if (isJsonFile(file)) {
    return parseJsonCard(file);
  }

  if (isPngFile(file)) {
    return parsePngCard(file);
  }

  throw new Error("Unsupported file type. Upload a JSON or PNG character card.");
};
