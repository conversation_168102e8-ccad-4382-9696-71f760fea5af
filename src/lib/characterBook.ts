import type { Prisma, CharacterBookEntry } from "@/generated/prisma";

type CharacterContext = {
  description: string | null;
  personality: string | null;
  scenario: string | null;
  creatorNotes: string | null;
  postHistoryInstructions: string | null;
  characterBook: Prisma.JsonValue | null;
};

export type CharacterBookChatMessage = {
  role: "assistant" | "user";
  content: string;
};

type ActivatedDepthGroup = {
  depth: number | null;
  role: "system" | "user" | "assistant";
  content: string[];
};

export type EvaluatedBook = {
  before: string[];
  after: string[];
  depth: ActivatedDepthGroup[];
  hasStatusFormat: boolean;
};

const WORLD_INFO_POSITION = {
  before: 0,
  after: 1,
  ANTop: 2,
  ANBottom: 3,
  atDepth: 4,
  EMTop: 5,
  EMBottom: 6,
} as const;

type WorldInfoPosition = (typeof WORLD_INFO_POSITION)[keyof typeof WORLD_INFO_POSITION];

const WORLD_INFO_LOGIC = {
  AND_ANY: 0,
  NOT_ALL: 1,
  NOT_ANY: 2,
  AND_ALL: 3,
} as const;

type WorldInfoLogic = (typeof WORLD_INFO_LOGIC)[keyof typeof WORLD_INFO_LOGIC];

const ROLE_MAP: Record<number, ActivatedDepthGroup["role"]> = {
  0: "system",
  1: "user",
  2: "assistant",
};

const DEFAULT_DEPTH = 4;

const isRecord = (value: unknown): value is Record<string, unknown> =>
  typeof value === "object" && value !== null;

const escapeRegex = (value: string) => value.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

const normaliseSearchTargets = (targets: Array<string | null | undefined>) =>
  targets
    .filter((value): value is string => typeof value === "string" && value.length > 0)
    .map((value) => value);

const collectRawEntryMap = (characterBook: Prisma.JsonValue | null) => {
  const map = new Map<number, Record<string, unknown>>();

  if (!isRecord(characterBook)) {
    return map;
  }

  const rawEntries = characterBook.entries;
  if (!Array.isArray(rawEntries)) {
    return map;
  }

  for (const entry of rawEntries) {
    if (isRecord(entry)) {
      const idValue = entry.id;
      if (typeof idValue === "number") {
        map.set(idValue, entry);
      }
    }
  }

  return map;
};

const resolvePosition = (
  entry: CharacterBookEntry,
  rawEntry: Record<string, unknown> | undefined,
): WorldInfoPosition => {
  const extension = isRecord(entry.extensions) ? entry.extensions : {};
  const positionOverride = extension.position;

  if (typeof positionOverride === "number") {
    return positionOverride as WorldInfoPosition;
  }

  const rawPosition = rawEntry?.position;
  if (typeof rawPosition === "number") {
    return rawPosition as WorldInfoPosition;
  }

  if (typeof entry.position === "string") {
    switch (entry.position) {
      case "before_char":
        return WORLD_INFO_POSITION.before;
      case "after_char":
        return WORLD_INFO_POSITION.after;
      default:
        break;
    }
  }

  return WORLD_INFO_POSITION.after;
};

const resolveRole = (entry: CharacterBookEntry) => {
  const extension = isRecord(entry.extensions) ? entry.extensions : {};
  const roleValueRaw = extension.role;
  const roleNumber = typeof roleValueRaw === "number" ? roleValueRaw : 0;
  return ROLE_MAP[roleNumber] ?? "system";
};

const resolveDepth = (entry: CharacterBookEntry) => {
  const extension = isRecord(entry.extensions) ? entry.extensions : {};
  const depthValue = extension.depth;
  return typeof depthValue === "number" ? depthValue : null;
};

const getBooleanExtension = (
  entry: CharacterBookEntry,
  key: string,
  defaultValue = false,
) => {
  const extension = isRecord(entry.extensions) ? entry.extensions : {};
  const value = extension[key];
  return typeof value === "boolean" ? value : defaultValue;
};

const toLogicValue = (entry: CharacterBookEntry) => {
  const extension = isRecord(entry.extensions) ? entry.extensions : {};
  const value = extension.selectiveLogic;
  return typeof value === "number" ? (value as WorldInfoLogic) : WORLD_INFO_LOGIC.AND_ANY;
};

const toProbabilitySettings = (
  entry: CharacterBookEntry,
): { useProbability: boolean; probability: number } => {
  const extension = isRecord(entry.extensions) ? entry.extensions : {};
  const useProbabilityValue = extension.useProbability;
  const probabilityValue = extension.probability;

  return {
    useProbability: typeof useProbabilityValue === "boolean" ? useProbabilityValue : true,
    probability: typeof probabilityValue === "number" ? probabilityValue : 100,
  };
};

const toStringArray = (value: unknown) =>
  Array.isArray(value)
    ? (value
        .map((item) => (typeof item === "string" ? item : null))
        .filter((item): item is string => Boolean(item?.length)))
    : [];

const buildHaystacks = (
  entry: CharacterBookEntry,
  character: CharacterContext,
  chatText: string,
) => {
  const haystacks = new Set<string>();
  if (chatText.length) {
    haystacks.add(chatText);
  }

  if (getBooleanExtension(entry, "match_persona_description")) {
    // Persona description is not available in this context; leaving placeholder for future support
  }

  if (getBooleanExtension(entry, "match_character_description") && character.description) {
    haystacks.add(character.description);
  }

  if (getBooleanExtension(entry, "match_character_personality") && character.personality) {
    haystacks.add(character.personality);
  }

  if (getBooleanExtension(entry, "match_character_depth_prompt") && character.postHistoryInstructions) {
    haystacks.add(character.postHistoryInstructions);
  }

  if (getBooleanExtension(entry, "match_scenario") && character.scenario) {
    haystacks.add(character.scenario);
  }

  if (getBooleanExtension(entry, "match_creator_notes") && character.creatorNotes) {
    haystacks.add(character.creatorNotes);
  }

  return normaliseSearchTargets(Array.from(haystacks));
};

const compileMatcher = (
  rawEntry: Record<string, unknown> | undefined,
  options: {
    caseSensitive: boolean;
    matchWholeWords: boolean;
  },
  key: string,
) => {
  const useRegexRaw = rawEntry?.use_regex;
  const useRegex = typeof useRegexRaw === "boolean" ? useRegexRaw : false;

  if (useRegex) {
    const flags = options.caseSensitive ? "g" : "gi";
    try {
      return new RegExp(key, flags);
    } catch (error) {
      console.warn("Failed to compile regex from character book key", error);
    }
  }

  const escaped = escapeRegex(key);
  const wrapped = options.matchWholeWords ? `\\b${escaped}\\b` : escaped;
  const flags = options.caseSensitive ? "g" : "gi";
  return new RegExp(wrapped, flags);
};

const matchKeyAgainstHaystacks = (
  pattern: RegExp,
  haystacks: string[],
) => haystacks.some((text) => pattern.test(text));

const shouldActivateEntry = (
  entry: CharacterBookEntry,
  rawEntry: Record<string, unknown> | undefined,
  character: CharacterContext,
  chatText: string,
) => {
  const enabled = typeof entry.enabled === "boolean" ? entry.enabled : true;
  if (!enabled) {
    return false;
  }

  const { useProbability, probability } = toProbabilitySettings(entry);
  if (useProbability && probability < 100) {
    const roll = Math.random() * 100;
    if (roll > probability) {
      return false;
    }
  }

  const constant = typeof entry.constant === "boolean" ? entry.constant : false;
  if (constant) {
    return true;
  }

  const keys = toStringArray(entry.keys);
  if (keys.length === 0) {
    return false;
  }

  const haystacks = buildHaystacks(entry, character, chatText);
  if (!haystacks.length) {
    return false;
  }

  const extension = isRecord(entry.extensions) ? entry.extensions : {};
  const matchWholeWords = Boolean(extension.match_whole_words);
  const caseSensitive = Boolean(extension.case_sensitive);

  const primaryMatched = keys.some((key) => {
    const matcher = compileMatcher(rawEntry, { caseSensitive, matchWholeWords }, key);
    if (!matcher) {
      return false;
    }
    return matchKeyAgainstHaystacks(matcher, haystacks);
  });

  if (!primaryMatched) {
    return false;
  }

  const secondaryKeys = toStringArray(entry.secondaryKeys);
  if (!secondaryKeys.length) {
    return true;
  }

  const selectiveLogic = toLogicValue(entry);
  const matches = secondaryKeys.map((key) => {
    const matcher = compileMatcher(rawEntry, { caseSensitive, matchWholeWords }, key);
    if (!matcher) {
      return false;
    }
    return matchKeyAgainstHaystacks(matcher, haystacks);
  });

  const hasAnyMatch = matches.some(Boolean);
  const hasAllMatch = matches.every(Boolean);

  switch (selectiveLogic) {
    case WORLD_INFO_LOGIC.AND_ANY:
      return hasAnyMatch;
    case WORLD_INFO_LOGIC.NOT_ALL:
      return !hasAllMatch;
    case WORLD_INFO_LOGIC.NOT_ANY:
      return !hasAnyMatch;
    case WORLD_INFO_LOGIC.AND_ALL:
      return hasAllMatch;
    default:
      return hasAnyMatch;
  }
};

export const evaluateCharacterBook = (
  entries: CharacterBookEntry[],
  character: CharacterContext,
  messages: CharacterBookChatMessage[],
): EvaluatedBook => {
  if (!entries.length) {
    return {
      before: [],
      after: [],
      depth: [],
      hasStatusFormat: false,
    };
  }

  const rawEntryMap = collectRawEntryMap(character.characterBook);
  const chatText = messages.map((message) => message.content).join("\n");
  const activated = entries.filter((entry) =>
    shouldActivateEntry(entry, rawEntryMap.get(entry.entryId), character, chatText),
  );

  if (!activated.length) {
    return {
      before: [],
      after: [],
      depth: [],
      hasStatusFormat: false,
    };
  }

  activated.sort((a, b) => {
    const orderA = a.insertionOrder ?? a.entryId;
    const orderB = b.insertionOrder ?? b.entryId;
    return (orderA ?? 0) - (orderB ?? 0);
  });

  const before: string[] = [];
  const after: string[] = [];
  const depthGroups = new Map<string, ActivatedDepthGroup>();
  let hasStatusFormat = false;

  for (const entry of activated) {
    if (entry.content.includes("<status_format")) {
      hasStatusFormat = true;
    }

    const rawEntry = rawEntryMap.get(entry.entryId);
    const position = resolvePosition(entry, rawEntry);
    const depth = resolveDepth(entry);
    const role = resolveRole(entry);
    const content = entry.content;

    switch (position) {
      case WORLD_INFO_POSITION.before:
        before.push(content);
        break;
      case WORLD_INFO_POSITION.after:
      case WORLD_INFO_POSITION.ANTop:
      case WORLD_INFO_POSITION.ANBottom:
      case WORLD_INFO_POSITION.EMTop:
      case WORLD_INFO_POSITION.EMBottom:
        after.push(content);
        break;
      case WORLD_INFO_POSITION.atDepth: {
        const depthKey = `${role}:${depth ?? DEFAULT_DEPTH}`;
        const existing = depthGroups.get(depthKey);
        if (existing) {
          existing.content.push(content);
        } else {
          depthGroups.set(depthKey, {
            depth: depth ?? DEFAULT_DEPTH,
            role,
            content: [content],
          });
        }
        break;
      }
      default:
        after.push(content);
        break;
    }
  }

  const depth = Array.from(depthGroups.values()).sort((a, b) => {
    const depthDiff = (a.depth ?? DEFAULT_DEPTH) - (b.depth ?? DEFAULT_DEPTH);
    if (depthDiff !== 0) {
      return depthDiff;
    }
    const roleOrder = ["system", "user", "assistant"];
    return roleOrder.indexOf(a.role) - roleOrder.indexOf(b.role);
  });

  return {
    before,
    after,
    depth,
    hasStatusFormat,
  };
};
