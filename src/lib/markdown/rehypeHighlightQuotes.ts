import { visit } from "unist-util-visit";
import type { Element, Root, Text } from "hast";

const HIGHLIGHT_CLASS = "text-sky-600";
const SKIP_TAGS = new Set(["code", "pre", "kbd", "samp"]);

export function rehypeHighlightQuotes() {
  return (tree: Root) => {
    visit(tree, "text", (node, index, parent) => {
      if (!parent || typeof index !== "number") {
        return;
      }

      if ("tagName" in parent && typeof parent.tagName === "string") {
        if (SKIP_TAGS.has(parent.tagName)) {
          return;
        }

        const className = parent.properties?.className;
        const hasHighlightClass = Array.isArray(className)
          ? className.includes(HIGHLIGHT_CLASS)
          : typeof className === "string" && className.includes(HIGHLIGHT_CLASS);

        if (hasHighlightClass) {
          return;
        }
      }

      const value = node.value;
      if (
        typeof value !== "string" ||
        (!value.includes("\"") && !value.includes("“") && !value.includes("”"))
      ) {
        return;
      }

      const newNodes: Array<Text | Element> = [];
      const pattern = /("[^"]+"|“[^”]+”)/g;
      let lastIndex = 0;
      let match: RegExpExecArray | null;

      while ((match = pattern.exec(value))) {
        const matchIndex = match.index ?? 0;
        const matchText = match[0];

        if (matchIndex > lastIndex) {
          const preceding = value.slice(lastIndex, matchIndex);
          if (preceding.length > 0) {
            newNodes.push({ type: "text", value: preceding });
          }
        }

        const highlightNode: Element = {
          type: "element",
          tagName: "span",
          properties: { className: [HIGHLIGHT_CLASS] },
          children: [{ type: "text", value: matchText }],
        };

        newNodes.push(highlightNode);
        lastIndex = matchIndex + matchText.length;
      }

      if (lastIndex < value.length) {
        const trailing = value.slice(lastIndex);
        if (trailing.length > 0) {
          newNodes.push({ type: "text", value: trailing });
        }
      }

      if (newNodes.length) {
        parent.children.splice(index, 1, ...newNodes);
      }
    });
  };
}
