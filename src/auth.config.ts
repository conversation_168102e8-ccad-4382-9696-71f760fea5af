import { PrismaAdapter } from "@auth/prisma-adapter";
import { compare } from "bcryptjs";
import type { AdapterUser } from "@auth/core/adapters";
import type { NextAuthConfig, Session, User } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import type { JWT } from "next-auth/jwt";

import { UserRole } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";

const mapUserRole = (role: unknown): UserRole => {
  if (role === UserRole.ADMIN || role === "ADMIN") {
    return UserRole.ADMIN;
  }
  return UserRole.USER;
};

export const authConfig: NextAuthConfig = {
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/login",
  },
  providers: [
    Credentials({
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      authorize: async (credentials) => {
        const email = credentials?.email as string;
        const password = credentials?.password as string;

        if (!email || !password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: { email: email.toLowerCase() },
        });

        if (!user?.passwordHash) {
          return null;
        }

        const isValid = await compare(password, user.passwordHash);
        if (!isValid) {
          return null;
        }

        const userWithoutHash = { ...user };
        delete userWithoutHash.passwordHash;

        return userWithoutHash;
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }: { token: JWT; user?: User | AdapterUser | null }) {
      if (user) {
        const role = mapUserRole((user as Partial<User> & { role?: UserRole }).role);
        token.role = role;
      } else if (!token.role && token.sub) {
        const existingUser = await prisma.user.findUnique({
          where: { id: token.sub },
          select: { role: true },
        });
        if (existingUser) {
          token.role = mapUserRole(existingUser.role);
        }
      }
      return token;
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      if (session.user) {
        if (token.sub) {
          session.user.id = token.sub;
        }
        session.user.role = mapUserRole(token.role);
      }
      return session;
    },
  },
};

export default authConfig;
