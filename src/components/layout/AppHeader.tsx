"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { signOut, useSession } from "next-auth/react";

const baseNavigation = [
  { href: "/", label: "Dashboard" },
  { href: "/characters", label: "Characters" },
];

const adminNavigation = [
  { href: "/llm-models", label: "LLM Providers" },
];

const toDisplayName = (value?: string | null) => {
  if (value && value.trim().length > 0) {
    return value.trim();
  }
  return "User";
};

export default function AppHeader() {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const router = useRouter();

  const isAdmin = session?.user.role === "ADMIN";

  const navigation = isAdmin
    ? [...baseNavigation, ...adminNavigation]
    : baseNavigation;

  const handleSignOut = async () => {
    await signOut({ redirect: false });
    router.push("/login");
  };

  return (
    <header className="border-b border-neutral-200 bg-white">
      <div className="mx-auto flex w-full max-w-6xl items-center justify-between gap-6 px-6 py-4">
        <div className="flex flex-1 items-center gap-6">
          <Link href="/" className="text-base font-semibold text-neutral-900">
            OpenAgents
          </Link>
          <nav className="hidden items-center gap-4 text-sm font-medium text-neutral-600 sm:flex">
            {navigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={
                    isActive
                      ? "text-neutral-900"
                      : "transition hover:text-neutral-900"
                  }
                >
                  {item.label}
                </Link>
              );
            })}
          </nav>
        </div>
        <div className="flex items-center gap-3 text-sm text-neutral-600">
          {status === "authenticated" && session?.user ? (
            <details className="relative">
              <summary className="flex cursor-pointer list-none items-center gap-2 rounded border border-transparent px-3 py-1 text-xs font-semibold text-neutral-700 transition hover:border-neutral-300 hover:bg-neutral-50">
                <span>{toDisplayName(session.user.name ?? session.user.email)}</span>
                <span className="text-[11px] uppercase tracking-wide text-neutral-400">
                  {session.user.role?.toLowerCase()}
                </span>
              </summary>
              <div className="absolute right-0 z-10 mt-2 w-48 rounded-lg border border-neutral-200 bg-white p-2 text-sm shadow-lg">
                <Link
                  href="/settings"
                  className="block rounded px-3 py-2 text-neutral-700 transition hover:bg-neutral-100"
                >
                  User Settings
                </Link>
                <Link
                  href="/personas"
                  className="block rounded px-3 py-2 text-neutral-700 transition hover:bg-neutral-100"
                >
                  Manage Personas
                </Link>
                <button
                  type="button"
                  onClick={handleSignOut}
                  className="mt-1 block w-full rounded px-3 py-2 text-left text-neutral-700 transition hover:bg-neutral-100"
                >
                  Sign out
                </button>
              </div>
            </details>
          ) : (
            <div className="flex items-center gap-2">
              <Link
                href="/login"
                className="rounded border border-neutral-300 px-3 py-1 text-xs font-semibold text-neutral-700 transition hover:border-neutral-400 hover:text-neutral-900"
              >
                Log in
              </Link>
              <Link
                href="/register"
                className="rounded bg-neutral-900 px-3 py-1 text-xs font-semibold text-white transition hover:bg-neutral-800"
              >
                Register
              </Link>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
