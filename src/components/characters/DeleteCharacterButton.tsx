"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";

export default function DeleteCharacterButton({ slug }: { slug: string }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = async () => {
    if (!confirm("Delete this character? This action cannot be undone.")) {
      return;
    }

    setLoading(true);
    setError(null);

    const response = await fetch(`/api/characters/${slug}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      const payload = (await response.json().catch(() => null)) as { error?: string } | null;
      setError(payload?.error ?? "Failed to delete character.");
      setLoading(false);
      return;
    }

    router.push("/characters");
    router.refresh();
  };

  return (
    <div className="flex flex-col gap-2">
      <button
        type="button"
        onClick={handleDelete}
        disabled={loading}
        className="rounded border border-red-200 bg-red-50 px-4 py-2 text-sm font-semibold text-red-700 transition hover:bg-red-100 disabled:opacity-60"
      >
        {loading ? "Deleting…" : "Delete Character"}
      </button>
      {error ? <p className="text-xs text-red-600">{error}</p> : null}
    </div>
  );
}
