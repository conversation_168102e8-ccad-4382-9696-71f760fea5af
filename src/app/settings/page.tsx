import { redirect } from "next/navigation";

import UserSettingsForm from "./UserSettingsForm";
import { ensureUserSettings } from "@/lib/userSettings";
import { auth } from "@/auth";

export default async function SettingsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  const settings = await ensureUserSettings(session.user.id!);

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-5xl flex-col gap-8 px-6 py-12">
      <header className="space-y-2">
        <h1 className="text-3xl font-semibold text-neutral-900">User Settings</h1>
        <p className="text-sm text-neutral-600">
          Configure the default system prompt used when chatting with characters.
        </p>
      </header>

      <section className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
        <h2 className="text-lg font-semibold text-neutral-900">Main Prompt</h2>
        <p className="mt-2 text-sm text-neutral-600 space-x-1">
          <span>
            This prompt establishes how characters should respond at the start of each conversation. It supports macros such as
          </span>
          <code className="rounded bg-neutral-100 px-1 py-0.5 text-xs">{"{{char}}"}</code>
          <code className="rounded bg-neutral-100 px-1 py-0.5 text-xs">{"{{user}}"}</code>
          <span>and</span>
          <code className="rounded bg-neutral-100 px-1 py-0.5 text-xs">{"{{persona}}"}</code>.
        </p>
        <div className="mt-4">
          <UserSettingsForm initialPrompt={settings.mainPrompt} />
        </div>
      </section>
    </main>
  );
}
