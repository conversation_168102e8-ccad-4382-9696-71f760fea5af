"use client";

import { FormEvent, useState, useTransition } from "react";

import { DEFAULT_MAIN_PROMPT } from "@/lib/systemPrompt";

type UserSettingsFormProps = {
  initialPrompt: string | null;
};

export default function UserSettingsForm({ initialPrompt }: UserSettingsFormProps) {
  const [mainPrompt, setMainPrompt] = useState(initialPrompt ?? "");
  const [pending, startTransition] = useTransition();
  const [feedback, setFeedback] = useState<{ type: "success" | "error"; message: string } | null>(null);

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setFeedback(null);

    startTransition(async () => {
      try {
        const response = await fetch("/api/user-settings", {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ mainPrompt }),
        });

        const payload = (await response.json()) as { error?: string };

        if (!response.ok) {
          setFeedback({ type: "error", message: payload.error ?? "Unable to update settings." });
          return;
        }

        setFeedback({ type: "success", message: "Main prompt saved." });
      } catch (error) {
        console.error("Update user settings failed", error);
        setFeedback({ type: "error", message: "Unable to update settings." });
      }
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="main-prompt" className="text-sm font-medium text-neutral-700">
          Custom Main Prompt
        </label>
        <p className="mt-1 text-xs text-neutral-500">
          Leave this blank to use the default prompt. You can include macros like {"{{char}}"}, {"{{user}}"}, and {"{{persona}}"}.
        </p>
        <textarea
          id="main-prompt"
          value={mainPrompt}
          onChange={(event) => setMainPrompt(event.currentTarget.value)}
          disabled={pending}
          rows={10}
          className="mt-2 w-full resize-y rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none disabled:cursor-not-allowed disabled:bg-neutral-100"
          placeholder={DEFAULT_MAIN_PROMPT}
        />
      </div>
      <div className="flex items-center gap-3">
        <button
          type="submit"
          disabled={pending}
          className="inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white transition disabled:cursor-not-allowed disabled:opacity-60"
        >
          {pending ? "Saving…" : "Save"}
        </button>
        <button
          type="button"
          disabled={pending}
          onClick={() => {
            setMainPrompt(DEFAULT_MAIN_PROMPT);
            setFeedback(null);
          }}
          className="inline-flex items-center justify-center rounded border border-neutral-300 px-3 py-2 text-sm font-medium text-neutral-700 transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-60"
        >
          Use Default
        </button>
        <button
          type="button"
          disabled={pending}
          onClick={() => {
            setMainPrompt("");
            setFeedback(null);
          }}
          className="inline-flex items-center justify-center rounded border border-neutral-300 px-3 py-2 text-sm font-medium text-neutral-700 transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-60"
        >
          Clear
        </button>
      </div>
      {feedback ? (
        <div
          className={`rounded border px-4 py-2 text-sm ${
            feedback.type === "success"
              ? "border-green-200 bg-green-50 text-green-700"
              : "border-red-200 bg-red-50 text-red-700"
          }`}
        >
          {feedback.message}
        </div>
      ) : null}
    </form>
  );
}
