"use client";

import { FormEvent, useMemo, useState, useTransition } from "react";
import { useRouter } from "next/navigation";

import type { Prisma } from "@/generated/prisma";

type ProviderModel = {
  id: string;
  identifier: string;
  displayName: string;
  description: string | null;
  promptPricePer1MTokens: number | null;
  completionPricePer1MTokens: number | null;
  currency: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
};

type ProviderWithModels = {
  id: string;
  name: string;
  baseUrl: string | null;
  apiKey: string | null;
  metadata: Prisma.JsonValue | null;
  createdAt: string;
  updatedAt: string;
  models: ProviderModel[];
};

type LLMSettingsProps = {
  providers: ProviderWithModels[];
};

const safeJsonStringify = (value: Prisma.JsonValue | null) => {
  if (!value) {
    return "";
  }

  try {
    return JSON.stringify(value, null, 2);
  } catch (error) {
    console.error("Failed to serialize metadata", error);
    return "";
  }
};

const formatCurrency = (currency: string) => currency.toUpperCase();

function useRefresh() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const refresh = () => {
    startTransition(() => {
      router.refresh();
    });
  };

  return { refresh, isPending };
}

type ProviderCardProps = {
  provider: ProviderWithModels;
  onChange: () => void;
};

function ProviderCard({ provider, onChange }: ProviderCardProps) {
  const [name, setName] = useState(provider.name);
  const [baseUrl, setBaseUrl] = useState(provider.baseUrl ?? "");
  const [apiKey, setApiKey] = useState(provider.apiKey ?? "");
  const [metadata, setMetadata] = useState(safeJsonStringify(provider.metadata));
  const [status, setStatus] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const resetStatus = () => {
    setStatus(null);
    setError(null);
  };

  const handleUpdate = async (event: FormEvent) => {
    event.preventDefault();
    resetStatus();
    setSubmitting(true);

    try {
      const response = await fetch(`/api/llm/providers/${provider.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          baseUrl,
          apiKey,
          metadata: metadata.trim().length ? metadata : null,
        }),
      });

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as { error?: string } | null;
        setError(payload?.error ?? "Failed to update provider.");
        return;
      }

      setStatus("Provider updated.");
      onChange();
    } catch (requestError) {
      console.error("Failed to update provider", requestError);
      setError("Request failed.");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm(`Delete provider "${provider.name}"? This removes all associated models.`)) {
      return;
    }

    resetStatus();
    setSubmitting(true);

    try {
      const response = await fetch(`/api/llm/providers/${provider.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as { error?: string } | null;
        setError(payload?.error ?? "Failed to delete provider.");
        return;
      }

      setStatus("Provider deleted.");
      onChange();
    } catch (requestError) {
      console.error("Failed to delete provider", requestError);
      setError("Request failed.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="space-y-4 rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
      <form className="space-y-4" onSubmit={handleUpdate}>
        <div className="grid gap-4 md:grid-cols-2">
          <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
            Name
            <input
              type="text"
              value={name}
              onChange={(event) => setName(event.currentTarget.value)}
              className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
              required
            />
          </label>
          <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
            Base URL
            <input
              type="text"
              value={baseUrl}
              onChange={(event) => setBaseUrl(event.currentTarget.value)}
              placeholder="https://api.openrouter.ai/api/v1"
              className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
            />
          </label>
          <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
            API Key
            <input
              type="text"
              value={apiKey}
              onChange={(event) => setApiKey(event.currentTarget.value)}
              placeholder="Optional — stored encrypted in DB"
              className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
            />
          </label>
          <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
            Metadata (JSON)
            <textarea
              value={metadata}
              onChange={(event) => setMetadata(event.currentTarget.value)}
              rows={4}
              placeholder='{"defaultHeaders": {"X-Title": "My App"}}'
              className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
            />
          </label>
        </div>
        <div className="flex flex-wrap items-center gap-3 text-xs">
          <button
            type="submit"
            disabled={submitting}
            className="inline-flex items-center justify-center rounded bg-black px-3 py-1.5 text-xs font-semibold text-white transition disabled:cursor-not-allowed disabled:opacity-60"
          >
            {submitting ? "Saving…" : "Save Provider"}
          </button>
          <button
            type="button"
            onClick={handleDelete}
            disabled={submitting}
            className="inline-flex items-center justify-center rounded border border-red-300 px-3 py-1.5 text-xs font-semibold text-red-600 transition hover:border-red-400 disabled:cursor-not-allowed disabled:opacity-60"
          >
            Delete Provider
          </button>
          <span className="text-neutral-500">
            Last updated {new Date(provider.updatedAt).toLocaleString()}
          </span>
          {status ? <span className="text-green-600">{status}</span> : null}
          {error ? <span className="text-red-600">{error}</span> : null}
        </div>
      </form>
      <ProviderModels provider={provider} onChange={onChange} disabled={submitting} />
    </div>
  );
}

type ProviderModelsProps = {
  provider: ProviderWithModels;
  onChange: () => void;
  disabled: boolean;
};

function ProviderModels({ provider, onChange, disabled }: ProviderModelsProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-neutral-800">Models</h3>
        <span className="text-xs text-neutral-500">{provider.models.length} configured</span>
      </div>
      <div className="space-y-3">
        {provider.models.map((model) => (
          <ModelRow key={model.id} model={model} onChange={onChange} disabled={disabled} />
        ))}
      </div>
      <AddModelForm providerId={provider.id} onChange={onChange} disabled={disabled} />
    </div>
  );
}

type ModelRowProps = {
  model: ProviderModel;
  onChange: () => void;
  disabled: boolean;
};

function ModelRow({ model, onChange, disabled }: ModelRowProps) {
  const [identifier, setIdentifier] = useState(model.identifier);
  const [displayName, setDisplayName] = useState(model.displayName);
  const [description, setDescription] = useState(model.description ?? "");
  const [promptPrice, setPromptPrice] = useState(
    model.promptPricePer1MTokens?.toString() ?? "",
  );
  const [completionPrice, setCompletionPrice] = useState(
    model.completionPricePer1MTokens?.toString() ?? "",
  );
  const [currency, setCurrency] = useState(model.currency);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const resetFeedback = () => {
    setMessage(null);
    setError(null);
  };

  const handleSave = async () => {
    resetFeedback();
    setSaving(true);

    try {
      const response = await fetch(`/api/llm/models/${model.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          identifier,
          displayName,
          description,
          promptPricePer1MTokens: promptPrice,
          completionPricePer1MTokens: completionPrice,
          currency,
        }),
      });

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as { error?: string } | null;
        setError(payload?.error ?? "Failed to update model.");
        return;
      }

      setMessage("Model saved.");
      onChange();
    } catch (requestError) {
      console.error("Failed to update model", requestError);
      setError("Request failed.");
    } finally {
      setSaving(false);
    }
  };

  const handleMakeDefault = async () => {
    resetFeedback();
    setSaving(true);

    try {
      const response = await fetch(`/api/llm/models/${model.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isDefault: true }),
      });

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as { error?: string } | null;
        setError(payload?.error ?? "Failed to set default model.");
        return;
      }

      setMessage("Default model updated.");
      onChange();
    } catch (requestError) {
      console.error("Failed to set default model", requestError);
      setError("Request failed.");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm(`Delete model "${model.displayName}"?`)) {
      return;
    }

    resetFeedback();
    setSaving(true);

    try {
      const response = await fetch(`/api/llm/models/${model.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as { error?: string } | null;
        setError(payload?.error ?? "Failed to delete model.");
        return;
      }

      setMessage("Model deleted.");
      onChange();
    } catch (requestError) {
      console.error("Failed to delete model", requestError);
      setError("Request failed.");
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="rounded border border-neutral-200 p-4">
      <div className="grid gap-3 md:grid-cols-2">
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Identifier
          <input
            type="text"
            value={identifier}
            onChange={(event) => setIdentifier(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Display Name
          <input
            type="text"
            value={displayName}
            onChange={(event) => setDisplayName(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="md:col-span-2 flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Description
          <input
            type="text"
            value={description}
            onChange={(event) => setDescription(event.currentTarget.value)}
            placeholder="Optional description"
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Prompt Price / 1M Tokens
          <input
            type="number"
            min="0"
            step="0.0001"
            value={promptPrice}
            onChange={(event) => setPromptPrice(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Completion Price / 1M Tokens
          <input
            type="number"
            min="0"
            step="0.0001"
            value={completionPrice}
            onChange={(event) => setCompletionPrice(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Currency
          <input
            type="text"
            value={currency}
            onChange={(event) => setCurrency(formatCurrency(event.currentTarget.value))}
            maxLength={6}
            className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-800 focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <div className="flex flex-col gap-2 text-xs text-neutral-500">
          <span>Created {new Date(model.createdAt).toLocaleString()}</span>
          <span>Updated {new Date(model.updatedAt).toLocaleString()}</span>
          {model.isDefault ? <span className="text-green-600">Default model</span> : null}
        </div>
      </div>
      <div className="mt-3 flex flex-wrap items-center gap-3 text-xs">
        <button
          type="button"
          onClick={handleSave}
          disabled={saving || disabled}
          className="inline-flex items-center justify-center rounded bg-neutral-900 px-3 py-1.5 text-xs font-semibold text-white transition disabled:cursor-not-allowed disabled:opacity-60"
        >
          {saving ? "Saving…" : "Save"}
        </button>
        <button
          type="button"
          onClick={handleMakeDefault}
          disabled={saving || disabled || model.isDefault}
          className="inline-flex items-center justify-center rounded border border-neutral-300 px-3 py-1.5 text-xs font-semibold text-neutral-700 transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-60"
        >
          Set as Default
        </button>
        <button
          type="button"
          onClick={handleDelete}
          disabled={saving || disabled}
          className="inline-flex items-center justify-center rounded border border-red-300 px-3 py-1.5 text-xs font-semibold text-red-600 transition hover:border-red-400 disabled:cursor-not-allowed disabled:opacity-60"
        >
          Delete
        </button>
        {message ? <span className="text-green-600">{message}</span> : null}
        {error ? <span className="text-red-600">{error}</span> : null}
      </div>
    </div>
  );
}

type AddModelFormProps = {
  providerId: string;
  onChange: () => void;
  disabled: boolean;
};

function AddModelForm({ providerId, onChange, disabled }: AddModelFormProps) {
  const [identifier, setIdentifier] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [description, setDescription] = useState("");
  const [promptPrice, setPromptPrice] = useState("");
  const [completionPrice, setCompletionPrice] = useState("");
  const [currency, setCurrency] = useState("USD");
  const [isDefault, setIsDefault] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);

  const resetForm = () => {
    setIdentifier("");
    setDisplayName("");
    setDescription("");
    setPromptPrice("");
    setCompletionPrice("");
    setCurrency("USD");
    setIsDefault(false);
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setStatus(null);
    setSubmitting(true);

    try {
      const response = await fetch("/api/llm/models", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          providerId,
          identifier,
          displayName,
          description,
          promptPricePer1MTokens: promptPrice,
          completionPricePer1MTokens: completionPrice,
          currency,
          isDefault,
        }),
      });

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as { error?: string } | null;
        setError(payload?.error ?? "Failed to create model.");
        return;
      }

      setStatus("Model created.");
      resetForm();
      onChange();
    } catch (requestError) {
      console.error("Failed to create model", requestError);
      setError("Request failed.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form
      className="space-y-3 rounded border border-dashed border-neutral-300 p-4"
      onSubmit={handleSubmit}
    >
      <h4 className="text-sm font-semibold text-neutral-800">Add Model</h4>
      <div className="grid gap-3 md:grid-cols-2">
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Identifier
          <input
            type="text"
            value={identifier}
            onChange={(event) => setIdentifier(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
            required
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Display Name
          <input
            type="text"
            value={displayName}
            onChange={(event) => setDisplayName(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
            required
          />
        </label>
        <label className="md:col-span-2 flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Description
          <input
            type="text"
            value={description}
            onChange={(event) => setDescription(event.currentTarget.value)}
            placeholder="Optional description"
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Prompt Price / 1M Tokens
          <input
            type="number"
            min="0"
            step="0.0001"
            value={promptPrice}
            onChange={(event) => setPromptPrice(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Completion Price / 1M Tokens
          <input
            type="number"
            min="0"
            step="0.0001"
            value={completionPrice}
            onChange={(event) => setCompletionPrice(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Currency
          <input
            type="text"
            value={currency}
            onChange={(event) => setCurrency(formatCurrency(event.currentTarget.value))}
            maxLength={6}
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex items-center gap-2 text-xs font-medium text-neutral-600">
          <input
            type="checkbox"
            checked={isDefault}
            onChange={(event) => setIsDefault(event.currentTarget.checked)}
            className="h-4 w-4"
          />
          Set as default model for this provider
        </label>
      </div>
      <div className="flex flex-wrap items-center gap-3 text-xs">
        <button
          type="submit"
          disabled={submitting || disabled}
          className="inline-flex items-center justify-center rounded bg-neutral-900 px-3 py-1.5 text-xs font-semibold text-white transition disabled:cursor-not-allowed disabled:opacity-60"
        >
          {submitting ? "Saving…" : "Add Model"}
        </button>
        {status ? <span className="text-green-600">{status}</span> : null}
        {error ? <span className="text-red-600">{error}</span> : null}
      </div>
    </form>
  );
}

type CreateProviderFormProps = {
  onChange: () => void;
};

function CreateProviderForm({ onChange }: CreateProviderFormProps) {
  const [name, setName] = useState("");
  const [baseUrl, setBaseUrl] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [metadata, setMetadata] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);

  const resetForm = () => {
    setName("");
    setBaseUrl("");
    setApiKey("");
    setMetadata("");
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setSubmitting(true);
    setError(null);
    setStatus(null);

    try {
      const response = await fetch("/api/llm/providers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          baseUrl,
          apiKey,
          metadata: metadata.trim().length ? metadata : null,
        }),
      });

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as { error?: string } | null;
        setError(payload?.error ?? "Failed to create provider.");
        return;
      }

      setStatus("Provider created.");
      resetForm();
      onChange();
    } catch (requestError) {
      console.error("Failed to create provider", requestError);
      setError("Request failed.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form
      className="space-y-3 rounded border border-dashed border-neutral-300 bg-neutral-50 p-6"
      onSubmit={handleSubmit}
    >
      <h2 className="text-lg font-semibold text-neutral-800">Add Provider</h2>
      <div className="grid gap-3 md:grid-cols-2">
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Name
          <input
            type="text"
            value={name}
            onChange={(event) => setName(event.currentTarget.value)}
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
            required
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Base URL
          <input
            type="text"
            value={baseUrl}
            onChange={(event) => setBaseUrl(event.currentTarget.value)}
            placeholder="https://api.openai.com/v1"
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="flex flex-col gap-1 text-xs font-medium text-neutral-600">
          API Key
          <input
            type="text"
            value={apiKey}
            onChange={(event) => setApiKey(event.currentTarget.value)}
            placeholder="Optional"
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
          />
        </label>
        <label className="md:col-span-2 flex flex-col gap-1 text-xs font-medium text-neutral-600">
          Metadata (JSON)
          <textarea
            value={metadata}
            onChange={(event) => setMetadata(event.currentTarget.value)}
            rows={4}
            placeholder='{"defaultHeaders": {"HTTP-Referer": "https://example.com"}}'
            className="rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
          />
        </label>
      </div>
      <div className="flex flex-wrap items-center gap-3 text-xs">
        <button
          type="submit"
          disabled={submitting}
          className="inline-flex items-center justify-center rounded bg-neutral-900 px-3 py-1.5 text-xs font-semibold text-white transition disabled:cursor-not-allowed disabled:opacity-60"
        >
          {submitting ? "Saving…" : "Add Provider"}
        </button>
        {status ? <span className="text-green-600">{status}</span> : null}
        {error ? <span className="text-red-600">{error}</span> : null}
      </div>
    </form>
  );
}

export default function LLMSettings({ providers }: LLMSettingsProps) {
  const { refresh, isPending } = useRefresh();

  const orderedProviders = useMemo(
    () =>
      providers.map((provider) => ({
        ...provider,
        models: provider.models.slice().sort((a, b) => {
          if (a.isDefault && !b.isDefault) return -1;
          if (!a.isDefault && b.isDefault) return 1;
          return a.displayName.localeCompare(b.displayName);
        }),
      })),
    [providers],
  );

  const handleChange = () => {
    refresh();
  };

  return (
    <div className="space-y-6">
      <CreateProviderForm onChange={handleChange} />

      {orderedProviders.length === 0 ? (
        <p className="rounded border border-neutral-200 bg-white p-6 text-sm text-neutral-600">
          No providers configured yet. Add one to unlock chat model selection.
        </p>
      ) : (
        <div className="space-y-6">
          {orderedProviders.map((provider) => (
            <ProviderCard key={provider.id} provider={provider} onChange={handleChange} />
          ))}
        </div>
      )}

      {isPending ? (
        <p className="text-xs text-neutral-500">Refreshing…</p>
      ) : null}
    </div>
  );
}
