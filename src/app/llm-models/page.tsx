import Link from "next/link";
import { notFound, redirect } from "next/navigation";

import type { Prisma } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";
import { auth } from "@/auth";

import LLMSettings from "./LLMSettings";

const decimalToNumber = (value: Prisma.Decimal | null) =>
  value ? value.toNumber() : null;

export default async function LLMModelsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  if (session.user.role !== "ADMIN") {
    notFound();
  }

  const providers = await prisma.lLMProvider.findMany({
    orderBy: { name: "asc" },
    include: {
      models: {
        orderBy: [{ isDefault: "desc" }, { displayName: "asc" }],
      },
    },
  });

  const data = providers.map((provider) => ({
    id: provider.id,
    name: provider.name,
    baseUrl: provider.baseUrl,
    apiKey: provider.apiKey,
    metadata: provider.metadata,
    createdAt: provider.createdAt.toISOString(),
    updatedAt: provider.updatedAt.toISOString(),
    models: provider.models.map((model) => ({
      id: model.id,
      identifier: model.identifier,
      displayName: model.displayName,
      description: model.description,
      promptPricePer1MTokens: decimalToNumber(model.promptPricePer1MTokens),
      completionPricePer1MTokens: decimalToNumber(model.completionPricePer1MTokens),
      currency: model.currency,
      isDefault: model.isDefault,
      createdAt: model.createdAt.toISOString(),
      updatedAt: model.updatedAt.toISOString(),
    })),
  }));

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-5xl flex-col gap-6 px-6 py-12">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-semibold">LLM Providers & Models</h1>
          <p className="text-sm text-neutral-600">
            Manage chat providers, service endpoints, API keys, and available models.
          </p>
        </div>
        <Link
          href="/"
          className="text-sm font-medium text-neutral-600 transition hover:text-neutral-900"
        >
          ← Back to Dashboard
        </Link>
      </div>

      <LLMSettings providers={data} />
    </main>
  );
}
