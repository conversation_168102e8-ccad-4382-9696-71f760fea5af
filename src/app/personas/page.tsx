import { redirect } from "next/navigation";

import <PERSON>aManager from "./PersonaManager";
import { ensureUserPersonas } from "@/lib/userPersona";
import { auth } from "@/auth";

export default async function PersonasPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  const { personas, defaultPersona } = await ensureUserPersonas({
    userId: session.user.id!,
    userName: session.user.name,
  });

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-5xl flex-col gap-8 px-6 py-12">
      <div className="space-y-2">
        <h1 className="text-3xl font-semibold text-neutral-900">Your Personas</h1>
        <p className="text-sm text-neutral-600">
          Define how characters perceive you. At least one persona must match your account name.
        </p>
      </div>

      <PersonaManager
        initialPersonas={personas.map((persona) => ({
          id: persona.id,
          name: persona.name,
          content: persona.content,
          isDefault: persona.isDefault,
          createdAt: persona.createdAt.toISOString(),
          updatedAt: persona.updatedAt.toISOString(),
        }))}
        defaultPersonaId={defaultPersona.id}
        canonicalName={session.user.name?.trim() ?? "User"}
      />
    </main>
  );
}
