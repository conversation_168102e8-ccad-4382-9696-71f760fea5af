"use client";

import { FormEvent, useEffect, useMemo, useState, useTransition } from "react";

type PersonaSummary = {
  id: string;
  name: string;
  content: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
};

type PersonaManagerProps = {
  initialPersonas: PersonaSummary[];
  defaultPersonaId: string;
  canonicalName: string;
};

type PersonaResponse = {
  personas: PersonaSummary[];
  defaultPersona: PersonaSummary;
};

const serializeBody = (input: Record<string, unknown>) =>
  JSON.stringify(input);

const PersonaCard = ({
  persona,
  canonicalName,
  onSave,
  onSetDefault,
  onDelete,
  pending,
}: {
  persona: PersonaSummary;
  canonicalName: string;
  onSave: (payload: { id: string; name: string; content: string }) => Promise<void>;
  onSetDefault: (id: string) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  pending: boolean;
}) => {
  const [name, setName] = useState(persona.name);
  const [content, setContent] = useState(persona.content);

  useEffect(() => {
    setName(persona.name);
    setContent(persona.content);
  }, [persona.id, persona.name, persona.content]);

  const isCanonical = persona.name === canonicalName;

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault();
    await onSave({ id: persona.id, name, content });
  };

  const disabled = pending;

  return (
    <form
      onSubmit={handleSubmit}
      className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm"
    >
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="flex flex-col">
          <label className="text-sm font-medium text-neutral-700" htmlFor={`persona-name-${persona.id}`}>
            Persona Name
          </label>
          <input
            id={`persona-name-${persona.id}`}
            type="text"
            value={name}
            onChange={(event) => setName(event.currentTarget.value)}
            disabled={disabled || isCanonical}
            className="mt-2 w-full rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none disabled:cursor-not-allowed disabled:bg-neutral-100"
          />
        </div>
        <div className="flex items-center gap-2 text-sm text-neutral-600">
          {persona.isDefault ? (
            <span className="rounded-full bg-sky-100 px-2.5 py-1 text-xs font-semibold text-sky-700">
              Default Persona
            </span>
          ) : null}
          {isCanonical ? (
            <span className="rounded-full bg-neutral-100 px-2.5 py-1 text-xs font-semibold text-neutral-600">
              Matches Account Name
            </span>
          ) : null}
        </div>
      </div>

      <label
        className="mt-4 block text-sm font-medium text-neutral-700"
        htmlFor={`persona-content-${persona.id}`}
      >
        Persona Description
      </label>
      <textarea
        id={`persona-content-${persona.id}`}
        value={content}
        onChange={(event) => setContent(event.currentTarget.value)}
        disabled={disabled}
        rows={5}
        className="mt-2 w-full resize-y rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none disabled:cursor-not-allowed disabled:bg-neutral-100"
      />

      <div className="mt-4 flex flex-wrap items-center gap-3">
        <button
          type="submit"
          disabled={disabled}
          className="inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white transition disabled:cursor-not-allowed disabled:opacity-60"
        >
          Save Changes
        </button>
        <button
          type="button"
          onClick={() => onSetDefault(persona.id)}
          disabled={disabled || persona.isDefault}
          className="inline-flex items-center justify-center rounded border border-neutral-300 px-4 py-2 text-sm font-medium text-neutral-700 transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-60"
        >
          Set as Default
        </button>
        <button
          type="button"
          onClick={() => onDelete(persona.id)}
          disabled={disabled || isCanonical}
          className="ml-auto inline-flex items-center justify-center rounded border border-red-300 px-4 py-2 text-sm font-medium text-red-600 transition hover:border-red-400 hover:text-red-700 disabled:cursor-not-allowed disabled:opacity-60"
        >
          Delete
        </button>
      </div>
    </form>
  );
};

export default function PersonaManager({
  initialPersonas,
  defaultPersonaId,
  canonicalName,
}: PersonaManagerProps) {
  const [personas, setPersonas] = useState<PersonaSummary[]>(initialPersonas);
  const [defaultId, setDefaultId] = useState(defaultPersonaId);
  const [pendingId, setPendingId] = useState<string | null>(null);
  const [createPending, startCreateTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [newName, setNewName] = useState("");
  const [newContent, setNewContent] = useState("");
  const [newDefault, setNewDefault] = useState(false);

  const sortedPersonas = useMemo(
    () =>
      [...personas].sort((a, b) => {
        if (a.id === defaultId) return -1;
        if (b.id === defaultId) return 1;
        return a.createdAt.localeCompare(b.createdAt);
      }),
    [personas, defaultId],
  );

  const updateFromResponse = (payload: PersonaResponse) => {
    setPersonas(payload.personas);
    setDefaultId(payload.defaultPersona.id);
    setSuccess("Persona changes saved.");
    setError(null);
  };

  const handleCreate = (event: FormEvent) => {
    event.preventDefault();
    setError(null);
    setSuccess(null);

    const trimmedName = newName.trim();
    const trimmedContent = newContent.trim();

    if (!trimmedName || !trimmedContent) {
      setError("Provide both a name and persona description.");
      return;
    }

    startCreateTransition(async () => {
      try {
        const response = await fetch("/api/personas", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: serializeBody({
            name: trimmedName,
            content: trimmedContent,
            isDefault: newDefault,
          }),
        });

        const payload = (await response.json()) as PersonaResponse & { error?: string };

        if (!response.ok) {
          setError(payload.error ?? "Unable to create persona.");
          return;
        }

        updateFromResponse(payload);
        setNewName("");
        setNewContent("");
        setNewDefault(false);
      } catch (requestError) {
        console.error("Create persona request failed", requestError);
        setError("Unable to create persona at this time.");
      }
    });
  };

  const handleSave = async ({ id, name, content }: { id: string; name: string; content: string }) => {
    setPendingId(id);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`/api/personas/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: serializeBody({ name, content }),
      });

      const payload = (await response.json()) as PersonaResponse & { error?: string };

      if (!response.ok) {
        setError(payload.error ?? "Unable to update persona.");
        return;
      }

      updateFromResponse(payload);
    } catch (requestError) {
      console.error("Update persona request failed", requestError);
      setError("Unable to update persona.");
    } finally {
      setPendingId(null);
    }
  };

  const handleSetDefault = async (id: string) => {
    setPendingId(id);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`/api/personas/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: serializeBody({ isDefault: true }),
      });

      const payload = (await response.json()) as PersonaResponse & { error?: string };

      if (!response.ok) {
        setError(payload.error ?? "Unable to set default persona.");
        return;
      }

      updateFromResponse(payload);
    } catch (requestError) {
      console.error("Set default persona request failed", requestError);
      setError("Unable to set default persona.");
    } finally {
      setPendingId(null);
    }
  };

  const handleDelete = async (id: string) => {
    setPendingId(id);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`/api/personas/${id}`, {
        method: "DELETE",
      });

      const payload = (await response.json()) as PersonaResponse & { error?: string };

      if (!response.ok) {
        setError(payload.error ?? "Unable to delete persona.");
        return;
      }

      updateFromResponse(payload);
    } catch (requestError) {
      console.error("Delete persona request failed", requestError);
      setError("Unable to delete persona.");
    } finally {
      setPendingId(null);
    }
  };

  return (
    <div className="space-y-8">
      <section className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
        <h2 className="text-lg font-semibold text-neutral-900">Create New Persona</h2>
        <p className="mt-2 text-sm text-neutral-600">
          Craft additional personas to swap into conversations. You can always set a new default later.
        </p>
        <form onSubmit={handleCreate} className="mt-4 space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700" htmlFor="new-persona-name">
              Persona Name
            </label>
            <input
              id="new-persona-name"
              type="text"
              value={newName}
              onChange={(event) => setNewName(event.currentTarget.value)}
              disabled={createPending}
              className="mt-2 w-full rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none disabled:cursor-not-allowed disabled:bg-neutral-100"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700" htmlFor="new-persona-content">
              Persona Description
            </label>
            <textarea
              id="new-persona-content"
              value={newContent}
              onChange={(event) => setNewContent(event.currentTarget.value)}
              disabled={createPending}
              rows={4}
              className="mt-2 w-full resize-y rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none disabled:cursor-not-allowed disabled:bg-neutral-100"
            />
          </div>
          <label className="inline-flex items-center gap-2 text-sm text-neutral-700">
            <input
              type="checkbox"
              checked={newDefault}
              onChange={(event) => setNewDefault(event.currentTarget.checked)}
              disabled={createPending}
              className="h-4 w-4"
            />
            Set as default persona
          </label>
          <div className="flex items-center gap-3">
            <button
              type="submit"
              disabled={createPending}
              className="inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white transition disabled:cursor-not-allowed disabled:opacity-60"
            >
              {createPending ? "Creating…" : "Create Persona"}
            </button>
            <button
              type="button"
              onClick={() => {
                setNewName("");
                setNewContent("");
                setNewDefault(false);
              }}
              disabled={createPending}
              className="inline-flex items-center justify-center rounded border border-neutral-300 px-4 py-2 text-sm font-medium text-neutral-700 transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-60"
            >
              Reset
            </button>
          </div>
        </form>
      </section>

      {error ? (
        <div className="rounded border border-red-200 bg-red-50 px-4 py-3 text-sm text-red-700">
          {error}
        </div>
      ) : null}
      {success ? (
        <div className="rounded border border-green-200 bg-green-50 px-4 py-3 text-sm text-green-700">
          {success}
        </div>
      ) : null}

      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-neutral-900">Your Personas</h2>
        <p className="text-sm text-neutral-600">
          Update descriptions to change how characters perceive you. The persona matching your account name is required and always available.
        </p>
        <div className="mt-4 space-y-4">
          {sortedPersonas.map((persona) => (
            <PersonaCard
              key={persona.id}
              persona={persona}
              canonicalName={canonicalName}
              onSave={handleSave}
              onSetDefault={handleSetDefault}
              onDelete={handleDelete}
              pending={pendingId === persona.id}
            />
          ))}
        </div>
      </section>
    </div>
  );
}
