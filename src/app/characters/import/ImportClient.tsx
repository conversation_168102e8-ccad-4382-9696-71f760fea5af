"use client";

import { FormEvent, useState } from "react";

type ImportResult = {
  id: string;
  slug: string;
  name: string;
  characterVersion: string;
};

export default function CharacterImportClient() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<ImportResult | null>(null);

  const onSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!selectedFile) {
      setError("Choose a character JSON file to import.");
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setResult(null);

    const formData = new FormData();
    formData.append("file", selectedFile);

    try {
      const response = await fetch("/api/characters/import", {
        method: "POST",
        body: formData,
      });

      const payload = await response.json();

      if (!response.ok) {
        setError(payload.error ?? "Import failed.");
        return;
      }

      setResult(payload.character as ImportResult);
    } catch {
      setError("Unexpected error while importing.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="mx-auto flex w-full max-w-2xl flex-col gap-8 py-16">
      <section className="space-y-4">
        <h1 className="text-3xl font-semibold">Import Character</h1>
        <p className="text-sm text-neutral-600">
          Upload a SillyTavern character card (`*.json`). The API stores the
          canonical details and the raw payload in Postgres.
        </p>
      </section>

      <form
        onSubmit={onSubmit}
        className="rounded-lg border border-dashed border-neutral-300 bg-white p-6 shadow-sm"
      >
        <label className="block text-sm font-medium text-neutral-700">
          Character Card File (JSON or PNG)
        </label>
        <input
          type="file"
          name="file"
          accept="application/json,image/png,.json,.png"
          onChange={(event) => {
            const file = event.currentTarget.files?.[0] ?? null;
            setSelectedFile(file);
            setResult(null);
            setError(null);
          }}
          className="mt-2 w-full cursor-pointer rounded border border-neutral-300 bg-neutral-50 px-3 py-2 text-sm"
        />
        <p className="mt-2 text-xs text-neutral-500">
          Examples: `mainKiera_spec_v2.json`, `*.png` character cards
        </p>

        <button
          type="submit"
          disabled={isSubmitting}
          className="mt-6 inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-medium text-white disabled:opacity-60"
        >
          {isSubmitting ? "Importing…" : "Import Character"}
        </button>
      </form>

      {error ? (
        <div className="rounded border border-red-200 bg-red-50 p-4 text-sm text-red-800">
          {error}
        </div>
      ) : null}

      {result ? (
        <div className="rounded border border-green-200 bg-green-50 p-4 text-sm text-green-800">
          <p className="font-medium">Character imported successfully.</p>
          <p>
            <span className="font-semibold">Name:</span> {result.name}
          </p>
          <p>
            <span className="font-semibold">Version:</span> {result.characterVersion}
          </p>
          <p>
            <span className="font-semibold">Slug:</span> {result.slug}
          </p>
        </div>
      ) : null}
    </div>
  );
}
