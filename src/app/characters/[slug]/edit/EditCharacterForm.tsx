"use client";

import { useState, FormEvent } from "react";
import { useRouter } from "next/navigation";

type EditableCharacter = {
  slug: string;
  name: string;
  description: string | null;
  personality: string | null;
  scenario: string | null;
  creatorNotes: string | null;
  firstMessage: string | null;
  tags: string[];
  alternateGreetings: string[];
  groupOnlyGreetings: string[];
  avatarUrl: string | null;
};

const listToText = (list: string[]) => list.join("\n");

const textToList = (value: string) =>
  value
    .split("\n")
    .map((entry) => entry.trim())
    .filter((entry) => entry.length > 0);

export default function EditCharacterForm({ character }: { character: EditableCharacter }) {
  const router = useRouter();
  const [name, setName] = useState(character.name);
  const [avatarUrl, setAvatarUrl] = useState(character.avatarUrl ?? "");
  const [description, setDescription] = useState(character.description ?? "");
  const [personality, setPersonality] = useState(character.personality ?? "");
  const [scenario, setScenario] = useState(character.scenario ?? "");
  const [creatorNotes, setCreatorNotes] = useState(character.creatorNotes ?? "");
  const [firstMessage, setFirstMessage] = useState(character.firstMessage ?? "");
  const [tags, setTags] = useState(character.tags.join(", "));
  const [alternateGreetings, setAlternateGreetings] = useState(listToText(character.alternateGreetings));
  const [groupOnlyGreetings, setGroupOnlyGreetings] = useState(listToText(character.groupOnlyGreetings));
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const onSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setSuccess(null);
    setLoading(true);

    const payload = {
      name,
      avatarUrl: avatarUrl.trim().length ? avatarUrl.trim() : null,
      description,
      personality,
      scenario,
      creatorNotes,
      firstMessage,
      tags: tags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag.length > 0),
      alternateGreetings: textToList(alternateGreetings),
      groupOnlyGreetings: textToList(groupOnlyGreetings),
    };

    const response = await fetch(`/api/characters/${character.slug}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const body = (await response.json().catch(() => null)) as { error?: string } | null;
      setError(body?.error ?? "Failed to update character.");
      setLoading(false);
      return;
    }

    setSuccess("Character updated.");
    setLoading(false);
    router.push(`/characters/${character.slug}`);
    router.refresh();
  };

  return (
    <form
      onSubmit={onSubmit}
      className="grid gap-6 lg:grid-cols-[minmax(0,2fr)_minmax(0,1fr)]"
    >
      <div className="space-y-6">
        <section className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
          <div className="flex flex-wrap items-center justify-between gap-2">
            <h2 className="text-lg font-semibold text-neutral-900">Basic Details</h2>
            <span className="text-xs text-neutral-500">Visible to everyone</span>
          </div>
          <p className="mt-2 text-sm text-neutral-600">
            Set the core information for the character card.
          </p>
          <div className="mt-4 grid gap-4 sm:grid-cols-2">
            <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
              Name
              <input
                type="text"
                value={name}
                onChange={(event) => setName(event.currentTarget.value)}
                required
                className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
              />
            </label>
            <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
              Avatar URL
              <input
                type="url"
                value={avatarUrl}
                onChange={(event) => setAvatarUrl(event.currentTarget.value)}
                placeholder="https://example.com/avatar.png"
                className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
              />
            </label>
          </div>
          <div className="mt-4">
            <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
              Tags (comma separated)
              <input
                type="text"
                value={tags}
                onChange={(event) => setTags(event.currentTarget.value)}
                className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
              />
            </label>
          </div>
        </section>

        <section className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
          <div className="flex flex-wrap items-center justify-between gap-2">
            <h2 className="text-lg font-semibold text-neutral-900">Persona & Notes</h2>
            <span className="text-xs text-neutral-500">Feed the model context</span>
          </div>
          <p className="mt-2 text-sm text-neutral-600">
            Describe how the character should behave and any guidance you want to
            preserve.
          </p>
          <div className="mt-4 space-y-4">
            <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
              Creator Notes
              <textarea
                value={creatorNotes}
                onChange={(event) => setCreatorNotes(event.currentTarget.value)}
                rows={5}
                className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
              />
            </label>
            <div className="grid gap-4 sm:grid-cols-2">
              <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
                Description
                <textarea
                  value={description}
                  onChange={(event) => setDescription(event.currentTarget.value)}
                  rows={4}
                  className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
                />
              </label>
              <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
                Personality
                <textarea
                  value={personality}
                  onChange={(event) => setPersonality(event.currentTarget.value)}
                  rows={4}
                  className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
                />
              </label>
            </div>
            <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
              Scenario
              <textarea
                value={scenario}
                onChange={(event) => setScenario(event.currentTarget.value)}
                rows={4}
                className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
              />
            </label>
          </div>
        </section>

        <section className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
          <div className="flex flex-wrap items-center justify-between gap-2">
            <h2 className="text-lg font-semibold text-neutral-900">Greetings & Messaging</h2>
            <span className="text-xs text-neutral-500">First impressions matter</span>
          </div>
          <p className="mt-2 text-sm text-neutral-600">
            Curate the opening messages players will encounter in chats.
          </p>
          <div className="mt-4 space-y-4">
            <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
              First Message
              <textarea
                value={firstMessage}
                onChange={(event) => setFirstMessage(event.currentTarget.value)}
                rows={3}
                className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
              />
            </label>
            <div className="grid gap-4 sm:grid-cols-2">
              <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
                Alternate Greetings (one per line)
                <textarea
                  value={alternateGreetings}
                  onChange={(event) => setAlternateGreetings(event.currentTarget.value)}
                  rows={5}
                  className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
                />
              </label>
              <label className="flex flex-col gap-2 text-sm font-medium text-neutral-700">
                Group-only Greetings (one per line)
                <textarea
                  value={groupOnlyGreetings}
                  onChange={(event) => setGroupOnlyGreetings(event.currentTarget.value)}
                  rows={5}
                  className="rounded border border-neutral-300 px-3 py-2 text-sm text-neutral-900 focus:border-neutral-500 focus:outline-none"
                />
              </label>
            </div>
          </div>
        </section>
      </div>

      <aside className="space-y-6">
        <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
          <h2 className="text-lg font-semibold text-neutral-900">Review & Save</h2>
          <p className="mt-2 text-sm text-neutral-600">
            Double-check key details before publishing your updates.
          </p>
          <ul className="mt-4 space-y-2 text-xs text-neutral-600">
            <li>- Ensure greetings introduce the scenario clearly.</li>
            <li>- Use creator notes for meta guidance that should not reach players.</li>
            <li>- Tags help with search and discovery.</li>
          </ul>
          {error ? (
            <p className="mt-4 rounded border border-red-200 bg-red-50 px-3 py-2 text-sm text-red-600">
              {error}
            </p>
          ) : null}
          {success ? (
            <p className="mt-4 rounded border border-green-200 bg-green-50 px-3 py-2 text-sm text-green-600">
              {success}
            </p>
          ) : null}
          <button
            type="submit"
            disabled={loading}
            className="mt-6 inline-flex w-full items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white transition disabled:opacity-60"
          >
            {loading ? "Saving…" : "Save Changes"}
          </button>
        </div>
      </aside>
    </form>
  );
}
