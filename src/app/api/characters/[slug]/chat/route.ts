import { NextRequest, NextResponse } from "next/server";

import <PERSON><PERSON><PERSON> from "openai";

import type { LLMProvider, Character, CharacterBookEntry } from "@/generated/prisma";
import { MessageRole } from "@/generated/prisma";
import { findChatModel } from "@/lib/chatModel";
import { buildCharacterSystemPrompt } from "@/lib/characterPrompt";
import {
  CharacterBookChatMessage,
  evaluateCharacterBook,
} from "@/lib/characterBook";
import { getCharacterForRead } from "@/lib/permissions";
import { prisma } from "@/lib/prisma";
import {
  applyPersonaToCharacter,
  getCurrentUserPersona,
  replacePersonaAndCharacterPlaceholders,
  resolvePersonaStrings,
} from "@/lib/userPersona";
import { ensureUserSettings, resolveMainPrompt } from "@/lib/userSettings";
import { auth } from "@/auth";

const DEFAULT_TEMPERATURE = 0.7;

type ClientMessage = {
  role: "assistant" | "user";
  content: string;
};

type RouteParams = {
  params: Promise<{ slug: string }>;
};

type CharacterWithBookEntries = Character & {
  bookEntries: CharacterBookEntry[];
};

const getNumberFromEnv = (value: string | undefined, fallback: number) => {
  if (typeof value === "undefined") return fallback;
  const parsed = Number(value);
  return Number.isFinite(parsed) ? parsed : fallback;
};

const DEFAULT_OPENAI_BASE_URL = "https://api.openai.com/v1";
const DEFAULT_OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1";

type ProviderMetadata = {
  defaultHeaders?: Record<string, string>;
};

const normaliseProviderName = (provider: LLMProvider) => provider.name.trim().toLowerCase();

const resolveProviderBaseUrl = (provider: LLMProvider) => {
  if (provider.baseUrl && provider.baseUrl.trim().length > 0) {
    return provider.baseUrl.trim();
  }

  const name = normaliseProviderName(provider);
  if (name === "openrouter") {
    return DEFAULT_OPENROUTER_BASE_URL;
  }

  return DEFAULT_OPENAI_BASE_URL;
};

const resolveProviderMetadata = (provider: LLMProvider) => {
  if (!provider.metadata) {
    return {};
  }

  if (typeof provider.metadata !== "object") {
    return {};
  }

  return provider.metadata as ProviderMetadata;
};

const resolveProviderHeaders = (provider: LLMProvider) => {
  const name = normaliseProviderName(provider);
  const headers: Record<string, string> = {
    ...(resolveProviderMetadata(provider).defaultHeaders ?? {}),
  };

  if (name === "openrouter") {
    if (!headers["HTTP-Referer"] && process.env.CHARACTER_CHAT_OPENROUTER_SITE) {
      headers["HTTP-Referer"] = process.env.CHARACTER_CHAT_OPENROUTER_SITE;
    }
    if (!headers["X-Title"] && process.env.CHARACTER_CHAT_OPENROUTER_TITLE) {
      headers["X-Title"] = process.env.CHARACTER_CHAT_OPENROUTER_TITLE;
    }
  }

  return headers;
};

const resolveProviderApiKey = (provider: LLMProvider) => {
  if (provider.apiKey && provider.apiKey.trim().length > 0) {
    return provider.apiKey.trim();
  }

  const name = normaliseProviderName(provider);

  if (name === "openrouter") {
    return process.env.CHARACTER_CHAT_OPENROUTER_API_KEY ?? null;
  }

  if (name === "openai") {
    return process.env.OPENAI_API_KEY ?? null;
  }

  return process.env.CHARACTER_CHAT_API_KEY ?? null;
};

const createOpenAIClient = (provider: LLMProvider) => {
  const apiKey = resolveProviderApiKey(provider);
  const headers = resolveProviderHeaders(provider);

  return new OpenAI({
    apiKey: apiKey ?? "",
    baseURL: resolveProviderBaseUrl(provider),
    defaultHeaders: Object.keys(headers).length ? headers : undefined,
  });
};

const createOpenAIStream = (
  iterator: AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>,
) => {
  const encoder = new TextEncoder();
  let aggregated = "";

  let resolveFinal: (value: string) => void = () => undefined;
  let rejectFinal: (reason?: unknown) => void = () => undefined;

  const finalText = new Promise<string>((resolve, reject) => {
    resolveFinal = resolve;
    rejectFinal = reject;
  });

  const stream = new ReadableStream<Uint8Array>({
    async start(controller) {
      try {
        for await (const chunk of iterator) {
          const choice = chunk.choices?.[0];
          if (!choice) {
            continue;
          }

          const delta = choice.delta?.content;
          if (delta) {
            aggregated += delta;
            controller.enqueue(
              encoder.encode(
                `${JSON.stringify({ type: "delta", content: delta })}\n`,
              ),
            );
          }
        }

        controller.enqueue(
          encoder.encode(`${JSON.stringify({ type: "done" })}\n`),
        );
        resolveFinal(aggregated);
      } catch (error) {
        controller.enqueue(
          encoder.encode(
            `${JSON.stringify({ type: "error", message: "Chat completion stream interrupted." })}\n`,
          ),
        );
        rejectFinal(error);
      } finally {
        controller.close();
      }
    },
    cancel(_reason) {
      // When stream is cancelled, resolve with partial content instead of rejecting
      // This ensures partial responses get saved to the database
      resolveFinal(aggregated);
    },
  });

  return { stream, finalText };
};


export async function POST(req: NextRequest, { params }: RouteParams) {
  const { slug } = await params;

  if (!slug) {
    return NextResponse.json(
      { error: "Character slug is required." },
      { status: 400 },
    );
  }

  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const character = await getCharacterForRead({
    slug,
    include: {
      bookEntries: {
        orderBy: {
          insertionOrder: "asc",
        },
      },
    },
  }) as CharacterWithBookEntries | null;

  if (!character) {
    return NextResponse.json({ error: "Character not found." }, { status: 404 });
  }

  const userPersona = await getCurrentUserPersona({
    userId: session.user.id!,
    userName: session.user.name,
  });

  const personaStrings = resolvePersonaStrings(userPersona);
  const personaAwareCharacter = applyPersonaToCharacter(character, personaStrings);
  const userSettings = await ensureUserSettings(session.user.id!);

  let payload: unknown;

  try {
    payload = await req.json();
  } catch {
    return NextResponse.json(
      { error: "Request body must be valid JSON." },
      { status: 400 },
    );
  }

  const body =
    payload && typeof payload === "object"
      ? (payload as {
        messages?: ClientMessage[];
        chatSessionId?: string;
        includeCharacterBook?: boolean;
        modelId?: string;
        reuseUserMessageId?: string;
        replaceAssistantMessageId?: string;
        initialGreeting?: string;
      })
      : undefined;

  const messages = body?.messages;
  const chatSessionId = body?.chatSessionId;
  const includeCharacterBook =
    typeof body?.includeCharacterBook === "boolean" ? body.includeCharacterBook : true;
  const incomingModelId =
    typeof body?.modelId === "string" && body.modelId.trim().length > 0
      ? body.modelId.trim()
      : null;
  const reuseUserMessageId =
    typeof body?.reuseUserMessageId === "string" && body.reuseUserMessageId.trim().length > 0
      ? body.reuseUserMessageId.trim()
      : null;
  const replaceAssistantMessageId =
    typeof body?.replaceAssistantMessageId === "string" &&
      body.replaceAssistantMessageId.trim().length > 0
      ? body.replaceAssistantMessageId.trim()
      : null;
  const initialGreeting =
    typeof body?.initialGreeting === "string" && body.initialGreeting.trim().length > 0
      ? body.initialGreeting.trim()
      : null;

  if (!Array.isArray(messages) || messages.length === 0) {
    return NextResponse.json(
      { error: "At least one chat message is required." },
      { status: 400 },
    );
  }

  if (!chatSessionId) {
    return NextResponse.json(
      { error: "A chat session id is required." },
      { status: 400 },
    );
  }

  const malformedMessage = messages.find(
    (message) =>
      !message ||
      (message.role !== "assistant" && message.role !== "user") ||
      typeof message.content !== "string" ||
      message.content.trim().length === 0,
  );

  if (malformedMessage) {
    return NextResponse.json(
      { error: "Messages must include a role and non-empty content." },
      { status: 400 },
    );
  }

  const chatSession = await prisma.chatSession.findUnique({
    where: { id: chatSessionId },
    select: {
      id: true,
      characterId: true,
      modelId: true,
    },
  });

  if (!chatSession || chatSession.characterId !== character.id) {
    return NextResponse.json({ error: "Chat session not found." }, { status: 404 });
  }

  const model = await findChatModel({
    requestedModelId: incomingModelId,
    fallbackModelId: chatSession.modelId,
  });

  if (incomingModelId && model && model.id !== incomingModelId) {
    return NextResponse.json(
      { error: "Requested chat model was not found." },
      { status: 400 },
    );
  }

  if (model && chatSession.modelId !== model.id) {
    await prisma.chatSession.update({
      where: { id: chatSession.id },
      data: { modelId: model.id },
    });
  }

  if (!model) {
    return NextResponse.json(
      incomingModelId
        ? { error: "Requested chat model was not found." }
        : { error: "No chat models are configured. Add at least one model before chatting." },
      { status: incomingModelId ? 400 : 503 },
    );
  }

  const latestMessage = messages[messages.length - 1];

  if (!latestMessage || latestMessage.role !== "user") {
    return NextResponse.json(
      { error: "Last message must be a user entry." },
      { status: 400 },
    );
  }



  let existingUserMessage: { id: string; content: string } | null = null;
  if (reuseUserMessageId) {
    existingUserMessage = await prisma.chatMessage.findFirst({
      where: {
        id: reuseUserMessageId,
        chatSessionId,
        role: MessageRole.USER,
      },
      select: {
        id: true,
        content: true,
      },
    });

    if (!existingUserMessage) {
      return NextResponse.json(
        { error: "Referenced user message was not found for regeneration." },
        { status: 400 },
      );
    }
  }

  let existingAssistantMessage: { id: string } | null = null;
  if (replaceAssistantMessageId) {
    existingAssistantMessage = await prisma.chatMessage.findFirst({
      where: {
        id: replaceAssistantMessageId,
        chatSessionId,
        role: MessageRole.ASSISTANT,
      },
      select: {
        id: true,
      },
    });

    if (!existingAssistantMessage) {
      return NextResponse.json(
        { error: "Referenced assistant message was not found for regeneration." },
        { status: 400 },
      );
    }
  }

  const personaAdjustedMessages = messages.map((message) =>
    message.role === "assistant"
      ? {
        ...message,
        content:
          replacePersonaAndCharacterPlaceholders(
            message.content,
            personaStrings,
            personaAwareCharacter.name,
          ) ?? message.content,
      }
      : message,
  );



  let greetingMessage: ClientMessage | null = null;
  let persistedUserMessage = existingUserMessage;
  let createdNewUserMessage = false;

  // Use a transaction to ensure greeting and user message are created atomically
  await prisma.$transaction(async (tx) => {
    // Check if this is the first USER message in the session
    const existingUserMessageCount = await tx.chatMessage.count({
      where: {
        chatSessionId,
        role: MessageRole.USER,
      },
    });

    // If this is the first user message and we have an initial greeting, create it first
    if (existingUserMessageCount === 0 && initialGreeting) {
      const processedGreeting = replacePersonaAndCharacterPlaceholders(
        initialGreeting,
        personaStrings,
        personaAwareCharacter.name,
      ) ?? initialGreeting;

      await tx.chatMessage.create({
        data: {
          chatSessionId,
          role: MessageRole.ASSISTANT,
          content: processedGreeting,
        },
      });

      // Store the greeting message to include in the conversation history
      greetingMessage = {
        role: "assistant",
        content: processedGreeting,
      };
    }

    // Create or update the user message
    if (persistedUserMessage) {
      if (persistedUserMessage.content !== latestMessage.content) {
        persistedUserMessage = await tx.chatMessage.update({
          where: { id: persistedUserMessage.id },
          data: { content: latestMessage.content },
        });
      }
    } else {
      persistedUserMessage = await tx.chatMessage.create({
        data: {
          chatSessionId,
          role: MessageRole.USER,
          content: latestMessage.content,
        },
      });
      createdNewUserMessage = true;
    }
  });

  await prisma.chatSession.update({
    where: { id: chatSessionId },
    data: { updatedAt: new Date() },
  });

  // Build the complete message history including greeting for book context
  const allMessagesForBook: ClientMessage[] = [];

  if (greetingMessage) {
    allMessagesForBook.push(greetingMessage);
  }

  allMessagesForBook.push(...personaAdjustedMessages);

  const updatedBookContextMessages: CharacterBookChatMessage[] = allMessagesForBook.map((message) => ({
    role: message.role,
    content: message.content,
  }));

  const bookPrompt = includeCharacterBook
    ? evaluateCharacterBook(
      personaAwareCharacter.bookEntries || [],
      {
        description: personaAwareCharacter.description,
        personality: personaAwareCharacter.personality,
        scenario: personaAwareCharacter.scenario,
        creatorNotes: personaAwareCharacter.creatorNotes,
        postHistoryInstructions: personaAwareCharacter.postHistoryInstructions,
        characterBook: personaAwareCharacter.characterBook,
      },
      updatedBookContextMessages,
    )
    : null;

  const systemPrompt = buildCharacterSystemPrompt(personaAwareCharacter, {
    book: bookPrompt,
    persona: personaStrings,
    mainPrompt: resolveMainPrompt(userSettings, personaStrings),
  });

  const temperature = getNumberFromEnv(
    process.env.CHARACTER_CHAT_TEMPERATURE,
    DEFAULT_TEMPERATURE,
  );

  // Build the conversation history, including the greeting if this is the first message
  const conversationMessages: ClientMessage[] = [];

  if (greetingMessage) {
    conversationMessages.push(greetingMessage);
  }

  conversationMessages.push(...personaAdjustedMessages);

  const openAiMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
    {
      role: "system",
      content: systemPrompt,
    },
    ...conversationMessages.map((message) => ({
      role: message.role,
      content: message.content,
    })),
  ];

  const configuredTimeout = Number(process.env.CHARACTER_CHAT_TIMEOUT_MS);
  const timeoutMs =
    Number.isFinite(configuredTimeout) && configuredTimeout > 0
      ? configuredTimeout
      : 120_000;

  const openai = createOpenAIClient(model.provider);

  try {
    const completion = await openai.chat.completions.create(
      {
        model: model.identifier,
        temperature,
        messages: openAiMessages,
        stream: true,
      },
      {
        signal: AbortSignal.timeout(timeoutMs),
      },
    );

    const { stream, finalText } = createOpenAIStream(completion);

    finalText
      .then(async (content) => {
        const trimmed = content.trim();

        if (!trimmed) {
          if (createdNewUserMessage && persistedUserMessage) {
            await prisma.chatMessage
              .delete({ where: { id: persistedUserMessage.id } })
              .catch(() => undefined);
          }
          return;
        }

        if (existingAssistantMessage) {
          await prisma.chatMessage.update({
            where: { id: existingAssistantMessage.id },
            data: {
              content: trimmed,
            },
          });
        } else {
          await prisma.chatMessage.create({
            data: {
              chatSessionId,
              role: MessageRole.ASSISTANT,
              content: trimmed,
            },
          });
        }

        await prisma.chatSession.update({
          where: { id: chatSessionId },
          data: { updatedAt: new Date() },
        });
      })
      .catch(async (error) => {
        console.error("Failed to persist chat response", error);
        if (createdNewUserMessage && persistedUserMessage) {
          await prisma.chatMessage
            .delete({ where: { id: persistedUserMessage.id } })
            .catch(() => undefined);
        }
      });

    return new Response(stream, {
      headers: {
        "Content-Type": "application/x-ndjson; charset=utf-8",
        "Cache-Control": "no-store",
      },
    });
  } catch (error) {
    console.error("Chat completion request failed", error);

    if (createdNewUserMessage && persistedUserMessage) {
      await prisma.chatMessage
        .delete({ where: { id: persistedUserMessage.id } })
        .catch(() => undefined);
    }

    const message =
      error instanceof Error && error.message
        ? error.message
        : "Failed to contact chat completion service.";

    return NextResponse.json({ error: message }, { status: 502 });
  }
}
