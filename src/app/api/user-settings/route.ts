import { NextResponse } from "next/server";

import { ensureUserSettings, updateUserSettings } from "@/lib/userSettings";
import { auth } from "@/auth";

export async function GET() {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const settings = await ensureUserSettings(session.user.id!);

  return NextResponse.json({
    settings: {
      mainPrompt: settings.mainPrompt,
      updatedAt: settings.updatedAt.toISOString(),
    },
  });
}

export async function PATCH(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  let payload: unknown;

  try {
    payload = await request.json();
  } catch {
    return NextResponse.json({ error: "Invalid JSON body." }, { status: 400 });
  }

  if (!payload || typeof payload !== "object") {
    return NextResponse.json({ error: "Invalid request payload." }, { status: 400 });
  }

  const { mainPrompt } = payload as { mainPrompt?: unknown };

  if (typeof mainPrompt !== "string" && typeof mainPrompt !== "undefined" && mainPrompt !== null) {
    return NextResponse.json({ error: "mainPrompt must be a string." }, { status: 400 });
  }

  const trimmed = typeof mainPrompt === "string" ? mainPrompt.trim() : null;

  const updated = await updateUserSettings(session.user.id!, {
    mainPrompt: trimmed && trimmed.length > 0 ? trimmed : null,
  });

  return NextResponse.json({
    settings: {
      mainPrompt: updated.mainPrompt,
      updatedAt: updated.updatedAt.toISOString(),
    },
  });
}
