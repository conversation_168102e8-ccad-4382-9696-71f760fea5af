import { NextResponse } from "next/server";
import { hash } from "bcryptjs";

import { UserRole } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";

const MIN_PASSWORD_LENGTH = 8;

export async function POST(request: Request) {
  let payload: unknown;
  try {
    payload = await request.json();
  } catch {
    return NextResponse.json({ error: "Body must be valid JSON." }, { status: 400 });
  }

  if (!payload || typeof payload !== "object") {
    return NextResponse.json({ error: "Body must be an object." }, { status: 400 });
  }

  const { name, email, password } = payload as {
    name?: unknown;
    email?: unknown;
    password?: unknown;
  };

  if (typeof email !== "string" || !email.trim()) {
    return NextResponse.json({ error: "Email is required." }, { status: 400 });
  }

  if (typeof password !== "string" || password.length < MIN_PASSWORD_LENGTH) {
    return NextResponse.json(
      { error: `Password must be at least ${MIN_PASSWORD_LENGTH} characters.` },
      { status: 400 },
    );
  }

  const existing = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
    select: { id: true },
  });

  if (existing) {
    return NextResponse.json({ error: "Email is already registered." }, { status: 409 });
  }

  const userCount = await prisma.user.count();
  const role = userCount === 0 ? UserRole.ADMIN : UserRole.USER;

  try {
    await prisma.user.create({
      data: {
        name: typeof name === "string" && name.trim() ? name.trim() : null,
        email: email.toLowerCase(),
        passwordHash: await hash(password, 12),
        role,
      },
    });
  } catch (error) {
    console.error("Failed to register user", error);
    return NextResponse.json({ error: "Failed to create user." }, { status: 500 });
  }

  return NextResponse.json({ success: true });
}
