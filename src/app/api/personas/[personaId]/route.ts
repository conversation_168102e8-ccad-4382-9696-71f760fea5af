import { NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { ensureUserPersonas } from "@/lib/userPersona";
import { auth } from "@/auth";

type RouteParams = {
  params: Promise<{
    personaId: string;
  }>;
};

const serializePersona = (persona: {
  id: string;
  name: string;
  content: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}) => ({
  id: persona.id,
  name: persona.name,
  content: persona.content,
  isDefault: persona.isDefault,
  createdAt: persona.createdAt.toISOString(),
  updatedAt: persona.updatedAt.toISOString(),
});

export async function PATCH(request: Request, { params }: RouteParams) {
  const { personaId } = await params;

  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const persona = await prisma.userPersona.findFirst({
    where: {
      id: personaId,
      userId: session.user.id!,
    },
  });

  if (!persona) {
    return NextResponse.json({ error: "Persona not found." }, { status: 404 });
  }

  let payload: unknown;

  try {
    payload = await request.json();
  } catch {
    return NextResponse.json({ error: "Invalid JSON body." }, { status: 400 });
  }

  if (!payload || typeof payload !== "object") {
    return NextResponse.json({ error: "Invalid request payload." }, { status: 400 });
  }

  const { name, content, isDefault } = payload as {
    name?: unknown;
    content?: unknown;
    isDefault?: unknown;
  };

  const updates: Record<string, unknown> = {};
  const canonicalName = session.user.name?.trim() ?? "User";

  if (typeof name === "string") {
    const trimmed = name.trim();
    if (trimmed.length === 0) {
      return NextResponse.json({ error: "Persona name cannot be empty." }, { status: 400 });
    }
    if (persona.name !== trimmed) {
      if (persona.name === canonicalName && trimmed !== canonicalName) {
        return NextResponse.json(
          { error: "The primary persona name must match your account name." },
          { status: 400 },
        );
      }
      updates.name = trimmed;
    }
  }

  if (typeof content === "string") {
    const trimmed = content.trim();
    if (trimmed.length === 0) {
      return NextResponse.json({ error: "Persona content cannot be empty." }, { status: 400 });
    }
    if (trimmed !== persona.content) {
      updates.content = trimmed;
    }
  }

  if (typeof isDefault === "boolean") {
    updates.isDefault = isDefault;
  }

  if (Object.keys(updates).length === 0) {
    return NextResponse.json({ error: "No changes supplied." }, { status: 400 });
  }

  try {
    await prisma.userPersona.update({
      where: { id: persona.id },
      data: updates,
    });

    if (updates.isDefault === true) {
      await prisma.userPersona.updateMany({
        where: {
          userId: session.user.id!,
          id: { not: persona.id },
          isDefault: true,
        },
        data: { isDefault: false },
      });
    }
  } catch (error) {
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return NextResponse.json(
        { error: "A persona with that name already exists." },
        { status: 409 },
      );
    }
    console.error("Update persona failed", error);
    return NextResponse.json({ error: "Unable to update persona." }, { status: 500 });
  }

  const { personas, defaultPersona } = await ensureUserPersonas({
    userId: session.user.id!,
    userName: session.user.name,
  });

  return NextResponse.json({
    personas: personas.map(serializePersona),
    defaultPersona: serializePersona(defaultPersona),
  });
}

export async function DELETE(_: Request, { params }: RouteParams) {
  const { personaId } = await params;

  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const persona = await prisma.userPersona.findFirst({
    where: {
      id: personaId,
      userId: session.user.id!,
    },
  });

  if (!persona) {
    return NextResponse.json({ error: "Persona not found." }, { status: 404 });
  }

  const canonicalName = session.user.name?.trim() ?? "User";
  if (persona.name === canonicalName) {
    return NextResponse.json(
      { error: "You must keep a persona that matches your account name." },
      { status: 400 },
    );
  }

  await prisma.userPersona.delete({ where: { id: persona.id } });

  const { personas, defaultPersona } = await ensureUserPersonas({
    userId: session.user.id!,
    userName: session.user.name,
  });

  return NextResponse.json({
    personas: personas.map(serializePersona),
    defaultPersona: serializePersona(defaultPersona),
  });
}
