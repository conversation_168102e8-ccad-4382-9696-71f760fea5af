import { NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { ensureUserPersonas } from "@/lib/userPersona";
import { auth } from "@/auth";

const serializePersona = (persona: {
  id: string;
  name: string;
  content: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}) => ({
  id: persona.id,
  name: persona.name,
  content: persona.content,
  isDefault: persona.isDefault,
  createdAt: persona.createdAt.toISOString(),
  updatedAt: persona.updatedAt.toISOString(),
});

export async function GET() {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const { personas, defaultPersona } = await ensureUserPersonas({
    userId: session.user.id!,
    userName: session.user.name,
  });

  return NextResponse.json({
    personas: personas.map(serializePersona),
    defaultPersona: serializePersona(defaultPersona),
  });
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  let payload: unknown;

  try {
    payload = await request.json();
  } catch {
    return NextResponse.json({ error: "Invalid JSON body." }, { status: 400 });
  }

  if (!payload || typeof payload !== "object") {
    return NextResponse.json({ error: "Invalid request payload." }, { status: 400 });
  }

  const { name, content, isDefault } = payload as {
    name?: unknown;
    content?: unknown;
    isDefault?: unknown;
  };

  if (typeof name !== "string" || name.trim().length === 0) {
    return NextResponse.json({ error: "Persona name is required." }, { status: 400 });
  }

  if (typeof content !== "string" || content.trim().length === 0) {
    return NextResponse.json({ error: "Persona content is required." }, { status: 400 });
  }

  const trimmedName = name.trim();
  const trimmedContent = content.trim();

  try {
    const persona = await prisma.userPersona.create({
      data: {
        userId: session.user.id!,
        name: trimmedName,
        content: trimmedContent,
        isDefault: Boolean(isDefault),
      },
    });

    if (persona.isDefault) {
      await prisma.userPersona.updateMany({
        where: {
          userId: session.user.id!,
          id: { not: persona.id },
          isDefault: true,
        },
        data: { isDefault: false },
      });
    }
  } catch (error) {
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return NextResponse.json(
        { error: "A persona with that name already exists." },
        { status: 409 },
      );
    }
    console.error("Create persona failed", error);
    return NextResponse.json({ error: "Unable to create persona." }, { status: 500 });
  }

  const { personas, defaultPersona } = await ensureUserPersonas({
    userId: session.user.id!,
    userName: session.user.name,
  });

  return NextResponse.json(
    {
      personas: personas.map(serializePersona),
      defaultPersona: serializePersona(defaultPersona),
    },
    { status: 201 },
  );
}
