import { NextResponse } from "next/server";

import { Prisma } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";
import { auth } from "@/auth";

type CreateModelPayload = {
  providerId?: unknown;
  identifier?: unknown;
  displayName?: unknown;
  description?: unknown;
  promptPricePer1MTokens?: unknown;
  completionPricePer1MTokens?: unknown;
  currency?: unknown;
  isDefault?: unknown;
};

const toDecimalOrNull = (value: unknown) => {
  if (typeof value === "undefined" || value === null || value === "") {
    return null;
  }

  if (typeof value === "number") {
    if (Number.isFinite(value)) {
      return new Prisma.Decimal(value);
    }
    throw new Error("Price must be a finite number.");
  }

  if (typeof value === "string") {
    const trimmed = value.trim();
    if (!trimmed) {
      return null;
    }

    const parsed = Number(trimmed);
    if (!Number.isFinite(parsed)) {
      throw new Error("Price must be a finite number.");
    }

    return new Prisma.Decimal(trimmed);
  }

  throw new Error("Price must be a number.");
};

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden." }, { status: 403 });
  }

  let payload: unknown;
  try {
    payload = await request.json();
  } catch {
    return NextResponse.json({ error: "Body must be valid JSON." }, { status: 400 });
  }

  if (!payload || typeof payload !== "object") {
    return NextResponse.json({ error: "Body must be an object." }, { status: 400 });
  }

  const {
    providerId,
    identifier,
    displayName,
    description,
    promptPricePer1MTokens,
    completionPricePer1MTokens,
    currency,
    isDefault,
  } = payload as CreateModelPayload;

  if (typeof providerId !== "string" || providerId.trim().length === 0) {
    return NextResponse.json({ error: "Provider id is required." }, { status: 400 });
  }

  if (typeof identifier !== "string" || identifier.trim().length === 0) {
    return NextResponse.json({ error: "Model identifier is required." }, { status: 400 });
  }

  if (typeof displayName !== "string" || displayName.trim().length === 0) {
    return NextResponse.json({ error: "Model display name is required." }, { status: 400 });
  }

  let promptPrice: Prisma.Decimal | null = null;
  let completionPrice: Prisma.Decimal | null = null;
  try {
    promptPrice = toDecimalOrNull(promptPricePer1MTokens);
    completionPrice = toDecimalOrNull(completionPricePer1MTokens);
  } catch (error) {
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Invalid price." },
      { status: 400 },
    );
  }

  const currencyCode =
    typeof currency === "string" && currency.trim().length > 0
      ? currency.trim().toUpperCase()
      : "USD";

  const shouldSetDefault = Boolean(isDefault);

  try {
    const result = await prisma.$transaction(async (tx) => {
      if (shouldSetDefault) {
        await tx.lLMModel.updateMany({
          where: {
            providerId: providerId.trim(),
            isDefault: true,
          },
          data: { isDefault: false },
        });
      }

      const model = await tx.lLMModel.create({
        data: {
          providerId: providerId.trim(),
          identifier: identifier.trim(),
          displayName: displayName.trim(),
          description:
            typeof description === "string" && description.trim().length > 0
              ? description.trim()
              : null,
          promptPricePer1MTokens: promptPrice,
          completionPricePer1MTokens: completionPrice,
          currency: currencyCode,
          isDefault: shouldSetDefault,
        },
      });

      return model.id;
    });

    return NextResponse.json({ success: true, modelId: result });
  } catch (error) {
    console.error("Failed to create LLM model", error);
    return NextResponse.json({ error: "Failed to create model." }, { status: 500 });
  }
}
