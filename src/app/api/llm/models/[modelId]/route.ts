import { NextResponse } from "next/server";

import { Prisma } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";
import { auth } from "@/auth";

type RouteParams = {
  params: Promise<{
    modelId: string;
  }>;
};

type UpdatePayload = {
  identifier?: unknown;
  displayName?: unknown;
  description?: unknown;
  promptPricePer1MTokens?: unknown;
  completionPricePer1MTokens?: unknown;
  currency?: unknown;
  isDefault?: unknown;
};

const toDecimalOrNull = (value: unknown) => {
  if (typeof value === "undefined") {
    return undefined;
  }

  if (value === null || value === "") {
    return null;
  }

  if (typeof value === "number") {
    if (!Number.isFinite(value)) {
      throw new Error("Price must be a finite number.");
    }
    return new Prisma.Decimal(value);
  }

  if (typeof value === "string") {
    const trimmed = value.trim();
    if (!trimmed) {
      return null;
    }
    const parsed = Number(trimmed);
    if (!Number.isFinite(parsed)) {
      throw new Error("Price must be a finite number.");
    }
    return new Prisma.Decimal(trimmed);
  }

  throw new Error("Price must be a number.");
};

export async function PATCH(request: Request, { params }: RouteParams) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden." }, { status: 403 });
  }

  const { modelId } = await params;

  let payload: unknown;
  try {
    payload = await request.json();
  } catch {
    return NextResponse.json({ error: "Body must be valid JSON." }, { status: 400 });
  }

  if (!payload || typeof payload !== "object") {
    return NextResponse.json({ error: "Body must be an object." }, { status: 400 });
  }

  const {
    identifier,
    displayName,
    description,
    promptPricePer1MTokens,
    completionPricePer1MTokens,
    currency,
    isDefault,
  } = payload as UpdatePayload;

  const data: Prisma.LLMModelUpdateInput = {};

  if (typeof identifier !== "undefined") {
    if (typeof identifier !== "string" || identifier.trim().length === 0) {
      return NextResponse.json({ error: "Identifier must be a non-empty string." }, { status: 400 });
    }
    data.identifier = identifier.trim();
  }

  if (typeof displayName !== "undefined") {
    if (typeof displayName !== "string" || displayName.trim().length === 0) {
      return NextResponse.json({ error: "Display name must be a non-empty string." }, { status: 400 });
    }
    data.displayName = displayName.trim();
  }

  if (typeof description !== "undefined") {
    if (description === null) {
      data.description = null;
    } else if (typeof description === "string") {
      const trimmed = description.trim();
      data.description = trimmed.length ? trimmed : null;
    } else {
      return NextResponse.json({ error: "Description must be a string." }, { status: 400 });
    }
  }

  try {
    const promptPrice = toDecimalOrNull(promptPricePer1MTokens);
    if (typeof promptPrice !== "undefined") {
      data.promptPricePer1MTokens = promptPrice;
    }

    const completionPrice = toDecimalOrNull(completionPricePer1MTokens);
    if (typeof completionPrice !== "undefined") {
      data.completionPricePer1MTokens = completionPrice;
    }
  } catch (error) {
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Invalid price." },
      { status: 400 },
    );
  }

  if (typeof currency !== "undefined") {
    if (currency === null) {
      data.currency = "USD";
    } else if (typeof currency === "string" && currency.trim().length > 0) {
      data.currency = currency.trim().toUpperCase();
    } else {
      return NextResponse.json({ error: "Currency must be a string." }, { status: 400 });
    }
  }

  const shouldSetDefault = typeof isDefault !== "undefined" ? Boolean(isDefault) : undefined;

  try {
    await prisma.$transaction(async (tx) => {
      const model = await tx.lLMModel.findUnique({
        where: { id: modelId },
        select: { providerId: true },
      });

      if (!model) {
        throw new Error("not-found");
      }

      if (shouldSetDefault) {
        await tx.lLMModel.updateMany({
          where: {
            providerId: model.providerId,
            isDefault: true,
          },
          data: {
            isDefault: false,
          },
        });
        data.isDefault = true;
      } else if (shouldSetDefault === false) {
        data.isDefault = false;
      }

      await tx.lLMModel.update({
        where: { id: modelId },
        data,
      });
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    if (error instanceof Error && error.message === "not-found") {
      return NextResponse.json({ error: "Model not found." }, { status: 404 });
    }

    console.error("Failed to update LLM model", error);
    return NextResponse.json({ error: "Failed to update model." }, { status: 500 });
  }
}

export async function DELETE(_: Request, { params }: RouteParams) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden." }, { status: 403 });
  }

  const { modelId } = await params;

  try {
    await prisma.lLMModel.delete({ where: { id: modelId } });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete LLM model", error);
    return NextResponse.json({ error: "Failed to delete model." }, { status: 500 });
  }
}
