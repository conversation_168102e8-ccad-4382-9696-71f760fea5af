import Link from "next/link";

import { auth } from "@/auth";

export default async function Home() {
  const session = await auth();

  if (!session?.user) {
    return (
      <main className="mx-auto flex min-h-screen w-full max-w-xl flex-col items-center justify-center gap-6 px-6 py-12 text-center">
        <h1 className="text-4xl font-semibold">OA Character Workspace</h1>
        <p className="text-base text-neutral-600">
          Sign in to import SillyTavern character cards, chat with them, and manage your workspace.
        </p>
        <div className="flex flex-wrap justify-center gap-3">
          <Link
            href="/login"
            className="inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white"
          >
            Log in
          </Link>
          <Link
            href="/register"
            className="inline-flex items-center justify-center rounded border border-neutral-300 px-4 py-2 text-sm font-semibold text-neutral-800"
          >
            Create account
          </Link>
        </div>
      </main>
    );
  }

  const isAdmin = session.user.role === "ADMIN";

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-3xl flex-col items-start justify-center gap-8 px-6 py-12">
      <section className="space-y-4">
        <h1 className="text-4xl font-semibold">OA Character Workspace</h1>
        <p className="text-base text-neutral-600">
          Start migrating SillyTavern features into this Next.js app. The first
          stop is importing character cards into Postgres.
        </p>
      </section>

      <div className="grid w-full gap-6 md:grid-cols-2 xl:grid-cols-3">
        <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
          <h2 className="text-xl font-medium">Bring in your first character</h2>
          <p className="mt-2 text-sm text-neutral-600">
            Upload an existing `*.json` character card and we will store both the
            normalized fields and the raw payload for future migration work.
          </p>
          <Link
            href="/characters/import"
            className="mt-4 inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white"
          >
            Import a Character
          </Link>
        </div>

        <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
          <h2 className="text-xl font-medium">Chat with imported characters</h2>
          <p className="mt-2 text-sm text-neutral-600">
            Browse your library and open a roleplay session that uses the
            character card fields to seed the first message and persona prompt.
          </p>
          <Link
            href="/characters"
            className="mt-4 inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white"
          >
            View Characters
          </Link>
        </div>

        {isAdmin ? (
          <div className="rounded-lg border border-neutral-200 bg-white p-6 shadow-sm">
            <h2 className="text-xl font-medium">Configure chat providers</h2>
            <p className="mt-2 text-sm text-neutral-600">
              Store API keys, endpoints, and pricing for each supported LLM provider, and
              curate the models you want available in chat sessions.
            </p>
            <Link
              href="/llm-models"
              className="mt-4 inline-flex items-center justify-center rounded bg-black px-4 py-2 text-sm font-semibold text-white"
            >
              Manage Providers & Models
            </Link>
          </div>
        ) : null}
      </div>
    </main>
  );
}
