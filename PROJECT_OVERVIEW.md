# OA Web Project Overview

## Pur<PERSON> & Scope
- Migrates SillyTavern character tooling into a Next.js 15 App Router experience.
- Focus areas today: importing SillyTavern character cards, browsing stored characters, managing character-centric chat sessions, and configuring LLM providers/models used for conversations.

## Tech Stack & Tooling
- **Framework**: Next.js 15 (App Router) with React 19 and Turbopack for dev/build (`npm run dev`, `npm run build`).
- **Styling**: Tailwind CSS v4 with a custom OKLCH theme defined in `src/app/globals.css` plus shadcn/ui components under `src/components/ui`.
- **Database**: PostgreSQL via Prisma 6 (`prisma/schema.prisma` generates clients in `src/generated/prisma`).
- **Server Runtime**: Next API routes with Prisma transactions; OpenAI SDK powers chat completion streaming.
- **Linting**: `npm run lint` wraps ESLint (ignores generated Prisma code and the `SillyTavern/` vendor mirror).
- **Authentication**: Auth.js (NextAuth) credentials flow with Prisma adapter, JWT sessions, and role-based access control (`USER` vs `ADMIN`).

## Authentication & RBAC
- Credentials-based login + registration backed by hashed passwords (`bcryptjs`).
- First registered user becomes `ADMIN`; subsequent users default to `USER`.
- `ADMIN` capabilities: configure LLM providers/models, manage all characters.
- `USER` capabilities: import characters (owner assigned automatically), edit/delete their characters, chat with owned characters.
- Server routes enforce access with `auth()`; shared helper `authorizeCharacterAccess` scopes character queries by owner unless admin.
- UI adapts to role: sign-in/out header, admin-only navigation, edit/delete controls on character detail pages, and admin gate on provider management.

## Application Structure Highlights
- `src/app/page.tsx`: Landing page framing three pillars—character import, library browsing/chat, and LLM configuration.
- `src/app/characters`:
  - `import/page.tsx`: Client form that uploads JSON/PNG cards to `/api/characters/import` and presents success/error feedback.
  - `page.tsx`: Server component listing characters (avatars, metadata) from Prisma; links into detailed views.
  - `[slug]/page.tsx`: Detailed character profile with persona metadata, greetings, character book entries, and navigation into chat.
  - `[slug]/chat/page.tsx`: Loads prior sessions/models server-side and hydrates the interactive `ChatPanel` client component.
- `src/app/llm-models/page.tsx`: Server page that fetches providers + nested models and renders the client `LLMSettings` management UI.
- `src/app/api`: REST-ish endpoints for character import, chat streaming, chat session CRUD (including message editing), and LLM provider/model CRUD.
- `SillyTavern/`: Vendor snapshot kept separate—treat as read-only unless syncing upstream changes.

## Key Backend Flows
- **Character Import (`POST /api/characters/import`)**
  - Accepts JSON or PNG card uploads (PNG metadata is base64-decoded) via `parseCharacterCardFile`.
  - Normalizes persona fields, greetings, tags, extensions, and character book entries; stores raw payload alongside parsed columns.
  - Ensures unique slugs per character/version and populates linked `CharacterBookEntry` records.
  - Associates imported characters with the authenticated user; prevents overwriting characters owned by others unless admin.
- **Chat Sessions (`/api/characters/[slug]/…`)**
  - `POST /sessions`: Creates a session, associates a selected LLM model, seeds the greeting, and returns full session payload.
  - `GET /sessions`: Lists summaries (message counts/previews) for sidebar selection.
  - `GET/DELETE /sessions/[sessionId]`: Fetches or removes a specific session; deleting is scoped to the character.
  - `PATCH /sessions/[sessionId]/messages/[messageId]`: Allows editing assistant/user messages while updating session timestamps.
  - `POST /chat`: Streams assistant replies using the chosen model/provider. Utilizes `buildCharacterSystemPrompt` plus `evaluateCharacterBook` to assemble persona + activated lore context, handles OpenRouter/OpenAI header/env overrides, and returns newline-delimited JSON stream chunks (`delta`, `done`, `error`). Access limited to character owners/admins.
- **LLM Provider & Model Management (`/api/llm/...`)**
  - `GET/POST /providers`: List or create providers with optional metadata JSON blobs.
  - `PATCH/DELETE /providers/[providerId]`: Update base URL/API key/metadata or remove a provider (cascade deletes models).
  - `POST /models`: Creates models under a provider, converting price inputs to Prisma `Decimal` and managing default flags transactionally.
  - `PATCH/DELETE /models/[modelId]`: Edit metadata or delete individual models; default flag flips reset other defaults within the provider. Admin-only endpoints.

## Shared Utilities (`src/lib`)
- `characterCard.ts`: Detects file type, parses JSON cards, and extracts SillyTavern metadata from PNG `tEXt` chunks.
- `characterBook.ts`: Implements SillyTavern world-info activation logic—compiles regex matchers, respects keys/secondary keys, depth/role routing, and produces structured lore buckets for prompts.
- `characterPrompt.ts`: Builds the system prompt by stitching persona fields, card spec info, optional instructions/examples, and evaluated character book sections.
- `chatModel.ts`: Resolves requested/fallback/default LLM models, guaranteeing a provider payload with pricing metadata.
- `prisma.ts`: Singleton Prisma client helper protecting against hot-reload connection storms.
- `utils.ts`: Tailwind-focused `cn()` helper (shadcn standard).

## Database Schema Summary (`prisma/schema.prisma`)
- `Character`: Core persona columns (description, greetings, tags, specs, extensions, raw JSON) plus relations to chat sessions and book entries. Enforces `(name, characterVersion)` uniqueness.
- `CharacterBookEntry`: Mirrors SillyTavern world-info rows with keys, optional insertion order, and free-form extensions.
- `ChatSession` & `ChatMessage`: Track per-character conversations, referencing optional `LLMModel` and enumerated `MessageRole` (USER/ASSISTANT).
- `LLMProvider` & `LLMModel`: Store provider credentials/endpoints plus model metadata, pricing (decimal), and default toggles.

## Frontend Experience Notes
- `ChatPanel` manages client-side state: session selection, greeting cycling, streaming assistant responses via ReadableStream, abort handling, message editing, and session list syncing.
- `LLMSettings` provides provider cards with inline edit/delete actions and a form to register new models, using optimistic UI refresh via `router.refresh()`. Rendered only for admins.
- Tailwind utilities + neutral palette deliver a dashboard aesthetic; avatars fall back to initials when no URL is present.

## Environment & Configuration
- Requires `DATABASE_URL` for Prisma.
- Chat providers look for `CHARACTER_CHAT_API_KEY`, `OPENAI_API_KEY`, and OpenRouter-specific `CHARACTER_CHAT_OPENROUTER_{API_KEY,SITE,TITLE}` overrides.
- Sample assets (`mainKiera_spec_v2.json`, etc.) in repo support manual imports.

## Process Guidelines (from `AGENTS.md`)
- Follow two-space indentation, TypeScript/React functional style, and `@/` import aliases.
- Keep reusable helpers in `src/lib`, UI components under `@/components/ui`, and avoid editing generated Prisma outputs directly.
- Run `npm run lint` before submissions; document new env vars in `.env.example` and README updates when setup changes.
