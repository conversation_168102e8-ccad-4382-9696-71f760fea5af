# Repository Guidelines

## Quick Index
- [Project Structure & Module Organization](#project-structure--module-organization)
- [Build, Test, and Development Commands](#build-test-and-development-commands)
- [Coding Style & Naming Conventions](#coding-style--naming-conventions)
- [Testing Guidelines](#testing-guidelines)
- [Commit & Pull Request Guidelines](#commit--pull-request-guidelines)
- [Database & Configuration](#database--configuration)

## Project Structure & Module Organization
- **App Router**: `src/app` contains all routes; group segments by feature and colocate server/client components.
- **Shared Utilities**: store reusable helpers in `src/lib`; `src/lib/utils.ts` exposes the `cn()` class merger.
- **Generated Prisma Types**: live in `src/generated/prisma`; never edit directly—modify `prisma/schema.prisma` and regenerate.
- **Static Assets**: keep images and other static files in `public/`.
- **UI Components**: `@/components/ui` hosts shadcn/ui-generated components defined by `components.json` (New York style, neutral base color, React Server Components enabled).
- **Vendor Bundle**: `SillyTavern/` mirrors upstream integration; treat as vendor code unless performing a sync.

## Build, Test, and Development Commands
| Task | Command | Notes |
| --- | --- | --- |
| Start local dev server | `npm run dev` | Uses Turbopack |
| Build production bundle | `npm run build` | Generates optimized output |
| Serve compiled build | `npm run start` | Runs after `npm run build` |
| Regenerate Prisma types | `npx prisma generate` | Requires `DATABASE_URL` in `.env.local`; run after schema edits |
| Create migration history | `npx prisma migrate dev` | Use when schema changes need tracked migrations |

## Coding Style & Naming Conventions
- **Languages**: TypeScript + React functional components.
- **Formatting**: two-space indentation; follow Prettier-compatible formatting.
- **Naming**: PascalCase for components, camelCase for hooks/utilities, UPPER_SNAKE_CASE for env vars.
- **Tailwind**: compose classes and use `cn()` for conditional styling; shadcn/ui styles extend `src/app/globals.css`.
- **Component Library**: leverage shadcn/ui scaffolding with Lucide icons; keep generated components under `@/components/ui` and utilities under `@/lib` per the configured aliases.
- **Imports**: prefer `@/` path aliases defined in `tsconfig.json`.

## Testing Guidelines
- **Baseline**: run `npm run lint` before submitting changes.
- **Coverage**: add tests alongside features using `*.test.ts(x)`. Please exclude the `SillyTavern/` directory from coverage reports.
- **Tooling**: prefer Playwright or React Testing Library.
- **Documentation**: note any new setup steps in the README until a dedicated test command exists.

## Commit & Pull Request Guidelines
- **Commit Messages**: short, imperative verbs (e.g., `add prisma`, `fix header link`).
- **Pull Requests**: summarize scope, highlight database or env updates, link to tracking issues, and add UI screenshots/recordings for visible changes.
- **Pre-Review Checks**: confirm linting (and migrations when applicable) before requesting review.

## Database & Configuration
- **Secrets**: store in `.env.local`; do not commit credentials.
- **Database**: `DATABASE_URL` points to your dev Postgres instance.
- **New Env Vars**: document in README and provide safe defaults or examples in `.env.example`.
